#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
静息态与刺激态对比分析 - 模块4：报告生成

功能：
- 加载前面三个模块的分析结果
- 整合结果，生成综合报告
- 提供理论解释和应用价值讨论

作者：AI助手
日期：2024年
"""

import os
import pandas as pd
from datetime import datetime

# 定义常量
DATA_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\14_rest_vs_test_analysis"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\14_rest_vs_test_analysis"

def find_latest_files(data_dir=DATA_DIR):
    """
    查找最新的分析结果文件
    
    参数:
    data_dir (str): 数据目录
    
    返回:
    dict: 最新的分析结果文件路径
    """
    print(f"查找分析结果文件: {data_dir}")
    
    # 查找最新的t检验结果文件
    ttest_files = [f for f in os.listdir(data_dir) if f.startswith("rest_vs_test_ttest_") and f.endswith('.csv')]
    if ttest_files:
        ttest_file = max([os.path.join(data_dir, f) for f in ttest_files], key=os.path.getmtime)
    else:
        ttest_file = None
    
    # 查找最新的相关分析结果文件
    corr_files = [f for f in os.listdir(data_dir) if f.startswith("resilience_correlation_") and f.endswith('.csv')]
    if corr_files:
        corr_file = max([os.path.join(data_dir, f) for f in corr_files], key=os.path.getmtime)
    else:
        corr_file = None
    
    # 查找最新的维度相关分析结果文件
    dim_files = [f for f in os.listdir(data_dir) if f.startswith("dimension_correlation_") and f.endswith('.csv')]
    if dim_files:
        dim_file = max([os.path.join(data_dir, f) for f in dim_files], key=os.path.getmtime)
    else:
        dim_file = None
    
    # 查找最新的调节效应分析结果文件
    mod_files = [f for f in os.listdir(data_dir) if f.startswith("resilience_moderation_") and f.endswith('.csv')]
    if mod_files:
        mod_file = max([os.path.join(data_dir, f) for f in mod_files], key=os.path.getmtime)
    else:
        mod_file = None
    
    # 查找最新的图表文件
    image_files = {}
    for prefix in ["rest_vs_test_comparison_", "effect_sizes_", "resilience_correlation_", "resilience_moderation_"]:
        files = [f for f in os.listdir(data_dir) if f.startswith(prefix) and f.endswith('.png')]
        if files:
            image_files[prefix.rstrip('_')] = max([os.path.join(data_dir, f) for f in files], key=os.path.getmtime)
    
    # 查找维度相关分析图表
    dimension_images = {}
    for dim in ["tenacity", "optimism", "strength"]:
        files = [f for f in os.listdir(data_dir) if f.startswith(f"dimension_{dim}_correlation_") and f.endswith('.png')]
        if files:
            dimension_images[dim] = max([os.path.join(data_dir, f) for f in files], key=os.path.getmtime)
    
    print(f"找到以下分析结果文件:")
    print(f"  - t检验结果: {os.path.basename(ttest_file) if ttest_file else 'None'}")
    print(f"  - 相关分析结果: {os.path.basename(corr_file) if corr_file else 'None'}")
    print(f"  - 维度相关分析结果: {os.path.basename(dim_file) if dim_file else 'None'}")
    print(f"  - 调节效应分析结果: {os.path.basename(mod_file) if mod_file else 'None'}")
    
    return {
        'ttest': ttest_file,
        'correlation': corr_file,
        'dimension': dim_file,
        'moderation': mod_file,
        'images': image_files,
        'dimension_images': dimension_images
    }

def load_results(file_paths):
    """
    加载分析结果
    
    参数:
    file_paths (dict): 分析结果文件路径
    
    返回:
    dict: 分析结果
    """
    print("加载分析结果...")
    
    results = {}
    
    # 加载t检验结果
    if file_paths['ttest']:
        results['ttest'] = pd.read_csv(file_paths['ttest'])
        print(f"加载t检验结果: {len(results['ttest'])}条记录")
    
    # 加载相关分析结果
    if file_paths['correlation']:
        results['correlation'] = pd.read_csv(file_paths['correlation'])
        print(f"加载相关分析结果: {len(results['correlation'])}条记录")
    
    # 加载维度相关分析结果
    if file_paths['dimension']:
        results['dimension'] = pd.read_csv(file_paths['dimension'])
        print(f"加载维度相关分析结果: {len(results['dimension'])}条记录")
    
    # 加载调节效应分析结果
    if file_paths['moderation']:
        results['moderation'] = pd.read_csv(file_paths['moderation'])
        print(f"加载调节效应分析结果: {len(results['moderation'])}条记录")
    
    return results

def generate_comprehensive_report(results, file_paths, output_dir=OUTPUT_DIR):
    """
    生成综合报告
    
    参数:
    results (dict): 分析结果
    file_paths (dict): 分析结果文件路径
    output_dir (str): 输出目录
    
    返回:
    str: 报告文件路径
    """
    print("生成综合报告...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 创建报告
    report = """# 静息态与刺激态HEP振幅差异及心理韧性调节效应分析报告

## 1. 研究概述

本研究探索了静息态（rest1-3）与刺激态（test1-3）条件下的HEP振幅差异，
以及心理韧性（总分及坚韧、乐观、力量三个维度）对这些差异的调节作用。
重点关注了Fz、Cz、Pz导联在0.4-0.6s和0.5-0.7s时间窗口的HEP振幅变化。

## 2. 静息态vs刺激态HEP振幅差异

"""
    
    # 添加t检验结果
    if 'ttest' in results:
        ttest_df = results['ttest']
        
        # 筛选显著结果
        sig_results = ttest_df[ttest_df['p_value'] < 0.05]
        
        if len(sig_results) > 0:
            report += "### 2.1 显著差异\n\n"
            report += "以下通道和时间窗口的HEP振幅在静息态与刺激态之间存在显著差异：\n\n"
            
            # 添加显著结果表格
            sig_table = sig_results[['channel', 'window', 'rest_mean', 'test_mean', 'diff_mean', 't_stat', 'p_value', 'cohen_d']].round(4)
            sig_table = sig_table.rename(columns={
                'channel': '通道',
                'window': '时间窗口',
                'rest_mean': '静息态均值',
                'test_mean': '刺激态均值',
                'diff_mean': '差异均值',
                't_stat': 't值',
                'p_value': 'p值',
                'cohen_d': "Cohen's d"
            })
            
            report += sig_table.to_markdown(index=False)
            
            report += "\n\n"
            
            # 添加解释
            report += "这些结果表明，压力刺激显著影响了内感受信号的神经加工，特别是在"
            
            if 'Pz' in sig_results['channel'].values:
                report += "顶叶区域（Pz）"
                if 'Cz' in sig_results['channel'].values or 'Fz' in sig_results['channel'].values:
                    report += "、"
            
            if 'Cz' in sig_results['channel'].values:
                report += "中央区域（Cz）"
                if 'Fz' in sig_results['channel'].values:
                    report += "和"
            
            if 'Fz' in sig_results['channel'].values:
                report += "前额区域（Fz）"
            
            report += "。\n\n"
        else:
            report += "未发现静息态与刺激态之间的显著差异。\n\n"
        
        # 添加效应量分析
        report += "### 2.2 效应量分析\n\n"
        
        large_effects = ttest_df[abs(ttest_df['cohen_d']) > 0.8]
        medium_effects = ttest_df[(abs(ttest_df['cohen_d']) > 0.5) & (abs(ttest_df['cohen_d']) <= 0.8)]
        small_effects = ttest_df[(abs(ttest_df['cohen_d']) > 0.2) & (abs(ttest_df['cohen_d']) <= 0.5)]
        
        if len(large_effects) > 0:
            report += "**大效应（|d| > 0.8）**：\n"
            for _, row in large_effects.iterrows():
                report += f"- {row['channel']}通道，{row['window']}时间窗口：d = {row['cohen_d']:.2f}\n"
        
        if len(medium_effects) > 0:
            report += "\n**中等效应（0.5 < |d| ≤ 0.8）**：\n"
            for _, row in medium_effects.iterrows():
                report += f"- {row['channel']}通道，{row['window']}时间窗口：d = {row['cohen_d']:.2f}\n"
        
        if len(small_effects) > 0:
            report += "\n**小效应（0.2 < |d| ≤ 0.5）**：\n"
            for _, row in small_effects.iterrows():
                report += f"- {row['channel']}通道，{row['window']}时间窗口：d = {row['cohen_d']:.2f}\n"
        
        report += "\n"
    
    # 添加图表引用
    if 'images' in file_paths and 'rest_vs_test_comparison' in file_paths['images']:
        report += "### 2.3 静息态vs刺激态对比图\n\n"
        fig_name = os.path.basename(file_paths['images']['rest_vs_test_comparison'])
        report += f"![静息态vs刺激态对比图]({fig_name})\n\n"
    
    if 'images' in file_paths and 'effect_sizes' in file_paths['images']:
        report += "### 2.4 效应量图\n\n"
        fig_name = os.path.basename(file_paths['images']['effect_sizes'])
        report += f"![效应量图]({fig_name})\n\n"
    
    report += """
## 3. 心理韧性与HEP振幅的相关性

"""
    
    # 添加相关分析结果
    if 'correlation' in results:
        corr_df = results['correlation']
        
        # 筛选显著结果
        sig_rest = corr_df[corr_df['p_rest'] < 0.05]
        sig_test = corr_df[corr_df['p_test'] < 0.05]
        sig_diff = corr_df[corr_df['p_diff'] < 0.05]
        
        report += "### 3.1 心理韧性总分与HEP振幅的相关性\n\n"
        
        if len(sig_rest) > 0 or len(sig_test) > 0 or len(sig_diff) > 0:
            report += "以下是心理韧性总分与HEP振幅的显著相关：\n\n"
            
            # 静息态
            if len(sig_rest) > 0:
                report += "**静息态HEP振幅**：\n"
                for _, row in sig_rest.iterrows():
                    report += f"- {row['channel']}通道，{row['window']}时间窗口：r = {row['r_rest']:.3f}, p = {row['p_rest']:.3f}\n"
            
            # 刺激态
            if len(sig_test) > 0:
                report += "\n**刺激态HEP振幅**：\n"
                for _, row in sig_test.iterrows():
                    report += f"- {row['channel']}通道，{row['window']}时间窗口：r = {row['r_test']:.3f}, p = {row['p_test']:.3f}\n"
            
            # 差异
            if len(sig_diff) > 0:
                report += "\n**静息态-刺激态差异**：\n"
                for _, row in sig_diff.iterrows():
                    report += f"- {row['channel']}通道，{row['window']}时间窗口：r = {row['r_diff']:.3f}, p = {row['p_diff']:.3f}\n"
        else:
            report += "未发现心理韧性总分与HEP振幅的显著相关。\n"
        
        report += "\n"
    
    # 添加维度相关分析结果
    if 'dimension' in results:
        dim_df = results['dimension']
        
        report += "### 3.2 心理韧性三个维度与HEP振幅的相关性\n\n"
        
        for dimension in ['tenacity', 'optimism', 'strength']:
            # 筛选当前维度的数据
            dim_data = dim_df[dim_df['dimension'] == dimension]
            
            # 筛选显著结果
            sig_rest = dim_data[dim_data['p_rest'] < 0.05]
            sig_test = dim_data[dim_data['p_test'] < 0.05]
            sig_diff = dim_data[dim_data['p_diff'] < 0.05]
            
            dimension_names = {
                'tenacity': '坚韧性',
                'optimism': '乐观性',
                'strength': '力量性'
            }
            
            report += f"**{dimension_names.get(dimension, dimension)}**：\n"
            
            if len(sig_rest) > 0 or len(sig_test) > 0 or len(sig_diff) > 0:
                # 静息态
                if len(sig_rest) > 0:
                    report += "- 静息态HEP振幅：\n"
                    for _, row in sig_rest.iterrows():
                        report += f"  - {row['channel']}通道，{row['window']}时间窗口：r = {row['r_rest']:.3f}, p = {row['p_rest']:.3f}\n"
                
                # 刺激态
                if len(sig_test) > 0:
                    report += "- 刺激态HEP振幅：\n"
                    for _, row in sig_test.iterrows():
                        report += f"  - {row['channel']}通道，{row['window']}时间窗口：r = {row['r_test']:.3f}, p = {row['p_test']:.3f}\n"
                
                # 差异
                if len(sig_diff) > 0:
                    report += "- 静息态-刺激态差异：\n"
                    for _, row in sig_diff.iterrows():
                        report += f"  - {row['channel']}通道，{row['window']}时间窗口：r = {row['r_diff']:.3f}, p = {row['p_diff']:.3f}\n"
            else:
                report += "未发现显著相关。\n"
            
            report += "\n"
    
    # 添加图表引用
    if 'images' in file_paths and 'resilience_correlation' in file_paths['images']:
        report += "### 3.3 心理韧性与HEP振幅相关性图\n\n"
        fig_name = os.path.basename(file_paths['images']['resilience_correlation'])
        report += f"![心理韧性与HEP振幅相关性图]({fig_name})\n\n"
    
    if 'dimension_images' in file_paths:
        report += "### 3.4 心理韧性维度与HEP振幅相关性图\n\n"
        
        dimension_names = {
            'tenacity': '坚韧性',
            'optimism': '乐观性',
            'strength': '力量性'
        }
        
        for dim, path in file_paths['dimension_images'].items():
            fig_name = os.path.basename(path)
            report += f"**{dimension_names.get(dim, dim)}**：\n\n"
            report += f"![{dimension_names.get(dim, dim)}与HEP振幅相关性图]({fig_name})\n\n"
    
    report += """
## 4. 心理韧性的调节效应

"""
    
    # 添加调节效应分析结果
    if 'moderation' in results:
        mod_df = results['moderation']
        
        # 筛选显著结果
        sig_mod = mod_df[mod_df['p_change'] < 0.05]
        
        report += "### 4.1 心理韧性对HEP振幅关系的调节效应\n\n"
        
        if len(sig_mod) > 0:
            report += "以下通道和时间窗口的HEP振幅关系受到心理韧性的显著调节：\n\n"
            
            # 添加显著结果表格
            sig_table = sig_mod[['channel', 'window', 'r2_model1', 'r2_model2', 'r2_change', 'p_change', 'interaction_coef']].round(4)
            sig_table = sig_table.rename(columns={
                'channel': '通道',
                'window': '时间窗口',
                'r2_model1': '模型1 R²',
                'r2_model2': '模型2 R²',
                'r2_change': 'R²变化量',
                'p_change': 'p值',
                'interaction_coef': '交互项系数'
            })
            
            report += sig_table.to_markdown(index=False)
            
            report += "\n\n"
            
            # 添加解释
            report += "这些结果表明，心理韧性显著调节了静息态HEP振幅与刺激态HEP振幅之间的关系，特别是在"
            
            if 'Pz' in sig_mod['channel'].values:
                report += "顶叶区域（Pz）"
                if 'Cz' in sig_mod['channel'].values or 'Fz' in sig_mod['channel'].values:
                    report += "、"
            
            if 'Cz' in sig_mod['channel'].values:
                report += "中央区域（Cz）"
                if 'Fz' in sig_mod['channel'].values:
                    report += "和"
            
            if 'Fz' in sig_mod['channel'].values:
                report += "前额区域（Fz）"
            
            report += "。\n\n"
            
            # 添加交互项系数解释
            pos_coef = sig_mod[sig_mod['interaction_coef'] > 0]
            neg_coef = sig_mod[sig_mod['interaction_coef'] < 0]
            
            if len(pos_coef) > 0:
                report += "**正向调节效应**：在"
                channels = pos_coef['channel'].unique()
                windows = pos_coef['window'].unique()
                
                report += "、".join(channels)
                report += "通道的"
                report += "、".join(windows)
                report += "时间窗口，心理韧性增强了静息态HEP振幅与刺激态HEP振幅的正相关关系。这表明，高心理韧性个体的内感受加工在静息态和刺激态之间更为一致。\n\n"
            
            if len(neg_coef) > 0:
                report += "**负向调节效应**：在"
                channels = neg_coef['channel'].unique()
                windows = neg_coef['window'].unique()
                
                report += "、".join(channels)
                report += "通道的"
                report += "、".join(windows)
                report += "时间窗口，心理韧性减弱了静息态HEP振幅与刺激态HEP振幅的正相关关系。这表明，高心理韧性个体的内感受加工在静息态和刺激态之间更为灵活，能够根据情境需要进行调整。\n\n"
        else:
            report += "未发现心理韧性对HEP振幅关系的显著调节效应。\n\n"
    
    # 添加图表引用
    if 'images' in file_paths and 'resilience_moderation' in file_paths['images']:
        report += "### 4.2 心理韧性调节效应图\n\n"
        fig_name = os.path.basename(file_paths['images']['resilience_moderation'])
        report += f"![心理韧性调节效应图]({fig_name})\n\n"
    
    report += """
## 5. 理论意义与应用价值

### 5.1 理论意义

本研究结果对内感受理论和心理韧性研究具有重要意义：

1. **内感受加工的情境依赖性**：静息态与刺激态HEP振幅的差异表明，内感受加工受到外部情境的影响，支持了内感受加工的情境依赖性假设。

2. **心理韧性与内感受加工的关联**：心理韧性与HEP振幅的相关性表明，心理韧性可能通过影响内感受加工来发挥保护作用，为理解心理韧性的神经机制提供了新视角。

3. **心理韧性的调节作用**：心理韧性对静息态-刺激态HEP振幅关系的调节效应表明，心理韧性可能影响个体在不同情境下内感受加工的一致性或灵活性，支持了心理韧性的"缓冲假设"。

4. **心理韧性维度的差异性作用**：心理韧性三个维度与HEP振幅的不同相关模式表明，心理韧性的不同方面可能通过不同机制影响内感受加工。

### 5.2 应用价值

本研究结果具有潜在的临床和实践应用价值：

1. **焦虑干预启示**：结果表明，提高心理韧性可能是改善内感受加工、降低焦虑的有效策略，为基于内感受的焦虑干预提供了理论基础。

2. **个性化干预策略**：根据个体的心理韧性水平和内感受敏感性，可以制定个性化干预方案，特别是针对内感受敏感性高但心理韧性低的个体。

3. **生物标记指标**：HEP振幅可能作为评估心理韧性干预效果的生物标记，为临床实践提供客观评估工具。

4. **压力管理应用**：了解心理韧性如何调节静息态与刺激态的内感受加工差异，有助于开发更有效的压力管理技术。

## 6. 局限性与未来方向

### 6.1 研究局限性

1. **样本量限制**：本研究的样本量（29个被试）可能不足以检测小效应，影响统计检验力。

2. **因果关系推断**：相关分析无法确定因果关系，需要实验操纵来验证心理韧性对内感受加工的影响。

3. **测量时间点**：心理韧性在实验结束时测量，可能受到实验过程的影响。

4. **其他调节变量**：未考虑其他可能的调节变量（如应对策略、社会支持等）。

### 6.2 未来研究方向

1. **纵向设计**：采用纵向设计，在多个时间点测量心理韧性和HEP，更好地揭示变量间的动态关系。

2. **实验操纵**：通过实验操纵提高/降低心理韧性，观察对HEP振幅的影响，增强因果推断的可能性。

3. **多模态整合**：结合其他生理指标（如心率变异性、皮电反应），构建更全面的内感受-情绪调节模型。

4. **临床群体扩展**：将研究扩展到焦虑障碍患者群体，比较临床群体与普通人群的调节模式差异。

5. **干预研究**：开发并测试基于内感受的心理韧性训练方案，评估其对HEP振幅和焦虑水平的影响。
"""
    
    # 保存报告
    report_path = os.path.join(output_dir, f"comprehensive_report_{timestamp}.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"综合报告已保存至: {report_path}")
    
    return report_path

def main():
    """主函数"""
    # 查找最新的分析结果文件
    file_paths = find_latest_files()
    
    # 加载分析结果
    results = load_results(file_paths)
    
    # 生成综合报告
    report_path = generate_comprehensive_report(results, file_paths)
    
    print("\n报告生成完成")
    print(f"综合报告已保存至: {report_path}")

if __name__ == "__main__":
    main()
