#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
18. 各阶段频段特征统计分析（分sheet保存）

- 读取4个频段特征csv
- 对每个对比组分别统计所有数值型指标（均值、标准差、极值、显著性计数等）
- 每个对比组结果分别保存到Excel不同sheet
- 所有数值保留三位小数
- 结果保存到14_rest_vs_test_analysis目录
"""
import pandas as pd
import os

# 文件路径
files = [
    r"D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/frequency_band_features_rest1_vs_rest3.csv",
    r"D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/frequency_band_features_rest1_vs_test1.csv",
    r"D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/frequency_band_features_rest1_vs_test2.csv",
    r"D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/frequency_band_features_rest1_vs_rest2.csv"
]

# 对比组名
compare_names = [
    'Rest1_vs_Rest3',
    'Rest1_vs_Test1',
    'Rest1_vs_Test2',
    'Rest1_vs_Rest2'
]

# 结果保存路径
output_path = r"D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/18_频段特征统计分阶段.xlsx"

writer = pd.ExcelWriter(output_path, engine='xlsxwriter')

for file, compare in zip(files, compare_names):
    df = pd.read_csv(file, encoding='utf-8')
    # 只保留数值型和关键信息
    id_cols = ['region', 'frequency_band', 'window_start_ms', 'window_end_ms', 'in_classic_window']
    id_cols = [c for c in id_cols if c in df.columns]
    value_cols = [c for c in df.columns if df[c].dtype in ['float64', 'float32', 'int64', 'int32'] and c not in id_cols]
    stat_cols = id_cols + value_cols
    stat_df = df[stat_cols].copy()

    # 分region+frequency_band分组统计所有数值型指标
    agg_dict = {col: ['mean', 'std', 'min', 'max'] for col in value_cols}
    group = stat_df.groupby(id_cols[:-1]).agg(agg_dict)
    # 展平多级列名
    group.columns = ['{}_{}'.format(a, b) for a, b in group.columns]
    group = group.reset_index()

    # 显著性计数（如有significance/p_value）
    if 'p_value' in df.columns:
        sig_count = stat_df.groupby(id_cols[:-1])['p_value'].apply(lambda x: (x < 0.05).sum()).reset_index(name='p<0.05计数')
        group = pd.merge(group, sig_count, on=id_cols[:-1], how='left')

    # 保留三位小数
    for col in group.select_dtypes(include=['float', 'float64', 'float32']).columns:
        group[col] = group[col].round(3)

    # ========== 特征名及统计量中英文映射 ==========
    feature_name_map = {
        'mean_difference_uv': '均值差异(μV)',
        'effect_size': '效应量',
        'p_value': 'p值',
        'peak_to_peak_rest1': '峰峰值(Rest1)',
        'peak_to_peak_rest3': '峰峰值(Test)',
        'std_rest1': '标准差(Rest1)',
        'std_rest3': '标准差(Test)',
        'rms_rest1': '均方根(Rest1)',
        'rms_rest3': '均方根(Test)',
        'dominant_frequency': '主频率',
        'spectral_power': '频谱能量',
        'high_freq_power': '高频能量',
        'late_component_area': '迟发面积',
        'late_pos_component': '迟发正向成分',
        'negative_peak_amp': '负向峰值',
        'pos_neg_peak_ratio': '正负峰比',
        'hep_cv': 'HEP变异系数',
        'peak_latency_ms': '峰值潜伏期',
        'onset_latency_ms': '起始潜伏期',
        'offset_latency_ms': '结束潜伏期',
        'response_duration_ms': '响应持续时间',
        'area_under_curve': '曲线下面积',
        'rising_slope': '上升斜率',
        'hep_recovery_slope': 'HEP恢复速率',
        # 可根据实际表格补充
    }
    stat_map = {'mean': '均值', 'std': '标准差', 'min': '最小值', 'max': '最大值'}
    # 替换列名
    def translate_col(col):
        for k, v in feature_name_map.items():
            if col.startswith(k + '_'):
                stat = col[len(k)+1:]
                stat_cn = stat_map.get(stat, stat)
                return f'{v}({stat_cn})'
        if col in feature_name_map:
            return feature_name_map[col]
        return col
    group.columns = [translate_col(c) for c in group.columns]

    # ========== 行为特征，列为脑区+频段 ==========
    # 先提取所有特征统计项（行为索引）
    stat_rows = [c for c in group.columns if c not in ['region', 'frequency_band', 'p<0.05计数']]
    pivot = group.melt(id_vars=['region', 'frequency_band'], value_vars=stat_rows, var_name='特征统计', value_name='数值')
    pivot['脑区_频段'] = pivot['region'] + '_' + pivot['frequency_band']
    table = pivot.pivot(index='特征统计', columns='脑区_频段', values='数值')
    # p<0.05计数单独处理
    if 'p<0.05计数' in group.columns:
        p05 = group[['region', 'frequency_band', 'p<0.05计数']].copy()
        p05['脑区_频段'] = p05['region'] + '_' + p05['frequency_band']
        p05 = p05.set_index('脑区_频段')['p<0.05计数']
        table.loc['p<0.05计数'] = p05
    table = table.reset_index()

    # 保存到sheet前，特征统计行全部用中文
    stat_row_map = {
        'freq_high_hz_max': '高频上限(最大值)',
        'freq_high_hz_mean': '高频上限(均值)',
        'freq_high_hz_min': '高频上限(最小值)',
        'freq_high_hz_std': '高频上限(标准差)',
        'freq_low_hz_max': '低频下限(最大值)',
        'freq_low_hz_mean': '低频下限(均值)',
        'freq_low_hz_min': '低频下限(最小值)',
        'freq_low_hz_std': '低频下限(标准差)',
        'max_difference_uv_max': '最大差值(最大值)',
        'max_difference_uv_mean': '最大差值(均值)',
        'max_difference_uv_min': '最大差值(最小值)',
        'max_difference_uv_std': '最大差值(标准差)',
        'peak_time_ms_max': '峰值时间(最大值)',
        'peak_time_ms_mean': '峰值时间(均值)',
        'peak_time_ms_min': '峰值时间(最小值)',
        'peak_time_ms_std': '峰值时间(标准差)',
        'peak_to_peak_change_max': '峰峰值变化(最大值)',
        'peak_to_peak_change_mean': '峰峰值变化(均值)',
        'peak_to_peak_change_min': '峰峰值变化(最小值)',
        'peak_to_peak_change_std': '峰峰值变化(标准差)',
        'peak_to_peak_effect_size_max': '峰峰值效应量(最大值)',
        'peak_to_peak_effect_size_mean': '峰峰值效应量(均值)',
        'peak_to_peak_effect_size_min': '峰峰值效应量(最小值)',
        'peak_to_peak_effect_size_std': '峰峰值效应量(标准差)',
        'peak_to_peak_p_value_max': '峰峰值p值(最大值)',
        'peak_to_peak_p_value_mean': '峰峰值p值(均值)',
        'peak_to_peak_p_value_min': '峰峰值p值(最小值)',
        'peak_to_peak_p_value_std': '峰峰值p值(标准差)',
        'peak_to_peak_t_value_max': '峰峰值t值(最大值)',
        'peak_to_peak_t_value_mean': '峰峰值t值(均值)',
        'peak_to_peak_t_value_min': '峰峰值t值(最小值)',
        'peak_to_peak_t_value_std': '峰峰值t值(标准差)',
        'p_value_mean': 'p值(均值)',
        'p_value_max': 'p值(最大值)',
        'p_value_min': 'p值(最小值)',
        'p_value_std': 'p值(标准差)',
        'rest1_mean_uv_max': 'Rest1均值(最大值)',
        'rest1_mean_uv_mean': 'Rest1均值(均值)',
        'rest1_mean_uv_min': 'Rest1均值(最小值)',
        'rest1_mean_uv_std': 'Rest1均值(标准差)',
        'rest3_mean_uv_max': 'Rest3均值(最大值)',
        'rest3_mean_uv_mean': 'Rest3均值(均值)',
        'rest3_mean_uv_min': 'Rest3均值(最小值)',
        'rest3_mean_uv_std': 'Rest3均值(标准差)',
        'rms_change_max': 'RMS变化(最大值)',
        'rms_change_mean': 'RMS变化(均值)',
        'rms_change_min': 'RMS变化(最小值)',
        'rms_change_std': 'RMS变化(标准差)',
        'rms_effect_size_max': 'RMS效应量(最大值)',
        'rms_effect_size_mean': 'RMS效应量(均值)',
        'rms_effect_size_min': 'RMS效应量(最小值)',
        'rms_effect_size_std': 'RMS效应量(标准差)',
        'rms_p_value_max': 'RMS p值(最大值)',
        'rms_p_value_mean': 'RMS p值(均值)',
        'rms_p_value_min': 'RMS p值(最小值)',
        'rms_p_value_std': 'RMS p值(标准差)',
        'rms_t_value_max': 'RMS t值(最大值)',
        'rms_t_value_mean': 'RMS t值(均值)',
        'rms_t_value_min': 'RMS t值(最小值)',
        'rms_t_value_std': 'RMS t值(标准差)',
        'std_change_max': '标准差变化(最大值)',
        'std_change_mean': '标准差变化(均值)',
        'std_change_min': '标准差变化(最小值)',
        'std_change_std': '标准差变化(标准差)',
        'std_effect_size_max': '标准差效应量(最大值)',
        'std_effect_size_mean': '标准差效应量(均值)',
        'std_effect_size_min': '标准差效应量(最小值)',
        'std_effect_size_std': '标准差效应量(标准差)',
        'std_p_value_max': '标准差p值(最大值)',
        'std_p_value_mean': '标准差p值(均值)',
        'std_p_value_min': '标准差p值(最小值)',
        'std_p_value_std': '标准差p值(标准差)',
        'std_t_value_max': '标准差t值(最大值)',
        'std_t_value_mean': '标准差t值(均值)',
        'std_t_value_min': '标准差t值(最小值)',
        'std_t_value_std': '标准差t值(标准差)',
        't_value_max': 't值(最大值)',
        't_value_mean': 't值(均值)',
        't_value_min': 't值(最小值)',
        't_value_std': 't值(标准差)',
        'window_end_ms': '窗口终点(ms)',
        'window_length_ms_max': '窗口长度(最大值)',
        'window_length_ms_mean': '窗口长度(均值)',
        'window_length_ms_min': '窗口长度(最小值)',
        'window_length_ms_std': '窗口长度(标准差)',
        'window_start_ms': '窗口起点(ms)',
        '均值差异(μV)(均值)': '均值差异(μV)(均值)',
        '均值差异(μV)(最大值)': '均值差异(μV)(最大值)',
        '均值差异(μV)(最小值)': '均值差异(μV)(最小值)',
        '均值差异(μV)(标准差)': '均值差异(μV)(标准差)',
        '均方根(Rest1)(均值)': '均方根(Rest1)(均值)',
        '均方根(Rest1)(最大值)': '均方根(Rest1)(最大值)',
        '均方根(Rest1)(最小值)': '均方根(Rest1)(最小值)',
        '均方根(Rest1)(标准差)': '均方根(Rest1)(标准差)',
        '均方根(Test)(均值)': '均方根(Test)(均值)',
        '均方根(Test)(最大值)': '均方根(Test)(最大值)',
        '均方根(Test)(最小值)': '均方根(Test)(最小值)',
        '均方根(Test)(标准差)': '均方根(Test)(标准差)',
        '峰峰值(Rest1)(均值)': '峰峰值(Rest1)(均值)',
        '峰峰值(Rest1)(最大值)': '峰峰值(Rest1)(最大值)',
        '峰峰值(Rest1)(最小值)': '峰峰值(Rest1)(最小值)',
        '峰峰值(Rest1)(标准差)': '峰峰值(Rest1)(标准差)',
        '峰峰值(Test)(均值)': '峰峰值(Test)(均值)',
        '峰峰值(Test)(最大值)': '峰峰值(Test)(最大值)',
        '峰峰值(Test)(最小值)': '峰峰值(Test)(最小值)',
        '峰峰值(Test)(标准差)': '峰峰值(Test)(标准差)',
        '效应量(均值)': '效应量(均值)',
        '效应量(最大值)': '效应量(最大值)',
        '效应量(最小值)': '效应量(最小值)',
        '效应量(标准差)': '效应量(标准差)',
        '标准差(Rest1)(均值)': '标准差(Rest1)(均值)',
        '标准差(Rest1)(最大值)': '标准差(Rest1)(最大值)',
        '标准差(Rest1)(最小值)': '标准差(Rest1)(最小值)',
        '标准差(Rest1)(标准差)': '标准差(Rest1)(标准差)',
        '标准差(Test)(均值)': '标准差(Test)(均值)',
        '标准差(Test)(最大值)': '标准差(Test)(最大值)',
        '标准差(Test)(最小值)': '标准差(Test)(最小值)',
        '标准差(Test)(标准差)': '标准差(Test)(标准差)',
        'p<0.05计数': 'p<0.05计数',
    }
    table['特征统计'] = table['特征统计'].map(stat_row_map).fillna(table['特征统计'])

    # 保存到sheet
    table.to_excel(writer, sheet_name=compare, index=False)

writer.close()
print(f"已导出分阶段频段特征统计表：{output_path}") 