import pandas as pd
import numpy as np
from scipy.stats import ttest_rel, wilcoxon
import matplotlib.pyplot as plt
import os
from scipy.stats import pearsonr
import statsmodels.api as sm

# 只保留关心的五个指标
COLUMNS = [
    '前测特质焦虑',
    '前测状态焦虑',
    '后测状态焦虑',
    '后测特质焦虑',
    '心理韧性'
]

# 变量名映射
col_map = {
    '特质焦虑1': '前测特质焦虑',
    '状态焦虑1': '前测状态焦虑',
    '状态焦虑2': '后测状态焦虑',
    '特质焦虑2': '后测特质焦虑',
    '心理韧性': '心理韧性'
}

file = r'C:/Users/<USER>/Desktop/stress0422.xlsx'
df = pd.read_excel(file)
print('所有列名:', df.columns.tolist())
# 重命名列
for old, new in col_map.items():
    if old in df.columns:
        df = df.rename(columns={old: new})
selected = [col for col in COLUMNS if col in df.columns]
df = df[selected]

# 输出目录
out_dir = r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/stress_scale_analysis'
os.makedirs(out_dir, exist_ok=True)

# 描述性统计
print('描述性统计:')
desc = df.describe().T
print(desc)
desc.to_excel(os.path.join(out_dir, 'stress_scale_describe.xlsx'))

# 配对t检验（如有前后测）
results = []
if '前测状态焦虑' in df.columns and '后测状态焦虑' in df.columns:
    t_stat, p_val = ttest_rel(df['前测状态焦虑'], df['后测状态焦虑'])
    results.append(('前测状态焦虑', '后测状态焦虑', t_stat, p_val))
if '前测特质焦虑' in df.columns and '后测特质焦虑' in df.columns:
    t_stat, p_val = ttest_rel(df['前测特质焦虑'], df['后测特质焦虑'])
    results.append(('前测特质焦虑', '后测特质焦虑', t_stat, p_val))

# 保存配对t检验结果
ttest_df = pd.DataFrame(results, columns=['指标A', '指标B', 't值', 'p值'])
ttest_df.to_excel(os.path.join(out_dir, 'stress_scale_ttest.xlsx'), index=False)

print('\n配对t检验结果:')
for a, b, t, p in results:
    print(f'{a} vs {b}: t={t:.3f}, p={p:.4f}')

# 计算高低焦虑分组（前测特质焦虑≥40为高焦虑，<40为低焦虑）
if '前测特质焦虑' in df.columns:
    df['焦虑分组'] = np.where(df['前测特质焦虑'] >= 40, '高焦虑', '低焦虑')
else:
    df['焦虑分组'] = '未知'

# 相关性分析配置
cor_pairs = [
    ('前测状态焦虑', '后测状态焦虑'),
    ('前测特质焦虑', '前测状态焦虑'),
    ('前测特质焦虑', '后测状态焦虑'),
    ('前测特质焦虑', '心理韧性'),
    ('前测状态焦虑', '心理韧性'),
    ('后测状态焦虑', '心理韧性'),
]

# 先计算所有组合的r和p
cor_results = []
for x_name, y_name in cor_pairs:
    if x_name in df.columns and y_name in df.columns:
        x = pd.to_numeric(df[x_name], errors='coerce')
        y = pd.to_numeric(df[y_name], errors='coerce')
        valid = x.notna() & y.notna()
        x = x[valid]
        y = y[valid]
        if len(x) > 1:
            r, p = pearsonr(x, y)
            cor_results.append({'x': x_name, 'y': y_name, 'r': r, 'p': p, 'xdata': x, 'ydata': y})

# 只保留p<0.05的组合，并按p值从小到大排序
sig_results = sorted([item for item in cor_results if item['p'] < 0.05], key=lambda x: x['p'])

# 可视化
n = len(sig_results)
if n == 0:
    print('无P值显著的相关性组合！')
else:
    ncols = 2
    nrows = 2 if n > 2 else 1
    fig, axes = plt.subplots(nrows, ncols, figsize=(5*ncols, 4*nrows), facecolor='white', constrained_layout=True)
    axes = axes.flatten() if n > 1 else [axes]
    for i, item in enumerate(sig_results):
        ax = axes[i]
        x, y = item['xdata'], item['ydata']
        group = df.loc[x.index, '焦虑分组']
        colors = group.map({'高焦虑': '#d62728', '低焦虑': '#1f77b4'}).values
        for g, color in [('高焦虑', '#d62728'), ('低焦虑', '#1f77b4')]:
            mask = (group == g)
            ax.scatter(x[mask], y[mask], color=color, alpha=0.7, s=30, edgecolor='black', linewidth=0.8)
        # 拟合线及置信区间
        x_sorted = np.sort(x)
        X = sm.add_constant(x_sorted)
        model = sm.OLS(y[np.argsort(x)], X).fit()
        pred = model.get_prediction(X)
        pred_summary = pred.summary_frame(alpha=0.05)
        y_pred = pred_summary['mean']
        iv_l = pred_summary['obs_ci_lower']
        iv_u = pred_summary['obs_ci_upper']
        ax.plot(x_sorted, y_pred, color='#b2182b', linewidth=2)
        ax.fill_between(x_sorted, iv_l, iv_u, color='#b2182b', alpha=0.18)
        ax.set_xlabel(item['x'], fontsize=10, fontname='SimHei')
        ax.set_ylabel(item['y'], fontsize=10, fontname='SimHei')
        ax.set_title(f"{item['x']} vs {item['y']}", fontsize=10, fontweight='bold', fontname='SimHei')
        for spine in ax.spines.values():
            spine.set_linewidth(1.5)
        ax.grid(False)
        ax.text(0.98, 0.02, f"r={item['r']:.2f}\np={item['p']:.4f}", fontsize=10, color='black', ha='right', va='bottom',
                transform=ax.transAxes, bbox=dict(facecolor='white', alpha=0.8, edgecolor='none', boxstyle='round,pad=0.2'))
    for j in range(i+1, len(axes)):
        axes[j].set_visible(False)
    from matplotlib.lines import Line2D
    legend_handles = [
        Line2D([0], [0], marker='o', color='w', label='高焦虑', markerfacecolor='#d62728', markersize=8, markeredgecolor='black', alpha=0.7),
        Line2D([0], [0], marker='o', color='w', label='低焦虑', markerfacecolor='#1f77b4', markersize=8, markeredgecolor='black', alpha=0.7)
    ]
    fig.suptitle('基于心理学量表的造压前后测状态对比分析', fontsize=12, fontweight='bold', fontname='SimHei', y=1.08)
    fig.legend(handles=legend_handles, labels=['高焦虑', '低焦虑'], fontsize=10, loc='upper center', bbox_to_anchor=(0.5, 1.04), ncol=2, frameon=False)
    fig.savefig(os.path.join(out_dir, 'stress_scale_correlation_trends_sig.png'), dpi=300, bbox_inches='tight')
    fig.savefig(os.path.join(out_dir, 'stress_scale_correlation_trends_sig.svg'), format='svg', bbox_inches='tight')
    plt.close(fig) 