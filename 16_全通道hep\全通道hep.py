#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
改进的心跳诱发电位（Heartbeat Evoked Potential，HEP）分析脚本

本脚本基于施秋霞(2023)《面向抑郁障碍的脑—心非线性交互作用及相关算法研究》
中的实验设计与分析方法，实现了HEP的提取与分析。

主要改进：
1. 分阶段显示HEP，每个阶段单独一个子图，便于比较
2. 调整纵坐标范围，使波形变化更加明显
3. 专注于Fz等关键通道在250-450ms时间窗口的分析
4. 优化图表大小，避免超出屏幕
5. 增强统计分析功能，更清晰地标注P值

作者：AI助手
日期：2024年
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import mne
import neurokit2 as nk
from scipy import stats
from matplotlib.ticker import AutoMinorLocator
from mpl_toolkits.axes_grid1 import make_axes_locatable

# 设置matplotlib参数，符合IEEE Transactions风格
plt.rcParams.update({
    'font.family': 'LXGW Wenkai',  # 使用LXGW Wenkai字体
    'font.size': 10,               # 字体大小设为10
    'axes.titlesize': 10,          # 标题字体大小
    'axes.labelsize': 10,          # 轴标签字体大小
    'xtick.labelsize': 10,         # x轴刻度标签字体大小
    'ytick.labelsize': 10,         # y轴刻度标签字体大小
    'legend.fontsize': 10,         # 图例字体大小
    'figure.titlesize': 10,        # 图标题字体大小
    'figure.dpi': 300,             # 图像DPI
    'savefig.dpi': 300,            # 保存图像的DPI
    'figure.figsize': (8, 6),      # 默认图像大小
    'figure.constrained_layout.use': True,  # 使用约束布局
    'axes.grid': False,            # 不显示网格
    'axes.spines.top': True,       # 显示上边框
    'axes.spines.right': True,     # 显示右边框
    'axes.linewidth': 0.5,         # 轴线宽度
    'lines.linewidth': 1.0,        # 线宽
    'lines.markersize': 4,         # 标记大小
})

# 检查LXGW Wenkai字体是否可用
font_available = False
for font in fm.findSystemFonts():
    if 'LXGW' in font:
        font_available = True
        plt.rcParams['font.family'] = 'LXGW Wenkai'
        break

if not font_available:
    print("警告: 未找到LXGW Wenkai字体，将使用系统默认字体。")
    plt.rcParams['font.family'] = 'sans-serif'

# 定义颜色方案
colors = {
    'prac': '#E63946',  # 红色系(练习阶段)
    'rest1': '#457B9D',  # 蓝色系(静息态1)
    'test1': '#1D3557',  # 深蓝色(刺激态1)
    'rest2': '#43AA8B',  # 绿色系(静息态2)
    'test2': '#254441',  # 深绿色(刺激态2)
    'rest3': '#577590',  # 蓝紫色(静息态3)
    'test3': '#2A3D45',  # 深蓝紫色(刺激态3)
}

# 定义常量
SAMPLE_RATE = 500  # 采样率
HEP_START = -0.2   # HEP开始时间（秒）
HEP_END = 0.6      # HEP结束时间（秒）
EEG_CHANNELS = 63  # 脑电通道数
ECG_CHANNELS = 58  # 心电通道数
DATA_DIR = r"D:/ecgeeg/19-eegecg手动预处理6-ICA3"  # 数据目录
OUTPUT_DIR = r"D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis"  # 输出目录

# 创建输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 定义实验阶段映射
stage_mapping = {
    'prac': ['01', 'prac'],  # 练习阶段
    'rest1': ['01', 'rest'],  # 静息态1
    'test1': ['01', 'test'],  # 刺激态1
    'rest2': ['02', 'rest'],  # 静息态2
    'test2': ['02', 'test'],  # 刺激态2
    'rest3': ['03', 'rest'],  # 静息态3
    'test3': ['03', 'test'],  # 刺激态3
}

# 定义阶段显示名称
stage_display_names = {
    'prac': '练习阶段',
    'rest1': '静息态1',
    'test1': '刺激态1',
    'rest2': '静息态2',
    'test2': '刺激态2',
    'rest3': '静息态3',
    'test3': '刺激态3',
}

# 定义关注的通道
roi_channels = ['Fz', 'F7', 'F8', 'Cz', 'F3', 'F4']  # 感兴趣的通道
roi_time = (0.25, 0.45)  # 感兴趣的时间窗口（秒）

# 脑电通道名称
eeg_channel_names = ['Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2',
                      'F7', 'F8', 'T7', 'T8', 'P7', 'P8', 'Fz', 'Cz', 'Pz', 'FC1',
                      'FC2', 'CP1', 'CP2', 'FC5', 'FC6', 'CP5', 'CP6', 'FT9', 'FT10',
                      'TP9', 'TP10', 'F1', 'F2', 'C1', 'C2', 'P1', 'P2', 'AF3', 'AF4',
                      'FC3', 'FC4', 'CP3', 'CP4', 'PO3', 'PO4', 'F5', 'F6', 'C5', 'C6',
                      'P5', 'P6', 'AF7', 'AF8', 'FT7', 'FT8', 'TP7', 'TP8', 'PO7', 'PO8',
                      'Fpz', 'CPz', 'POz', 'Oz']

def load_data(file_path):
    """
    加载预处理好的fif文件

    参数:
        file_path: fif文件路径

    返回:
        raw: MNE Raw对象
        eeg_data: 脑电数据
        ecg_data: 心电数据
    """
    print(f"加载数据: {file_path}")
    raw = mne.io.read_raw_fif(file_path, preload=True)

    # 分离脑电和心电数据
    eeg_channels = raw.ch_names[:EEG_CHANNELS]
    ecg_channels = raw.ch_names[EEG_CHANNELS:EEG_CHANNELS+ECG_CHANNELS]

    eeg_data = raw.copy().pick_channels(eeg_channels)
    ecg_data = raw.copy().pick_channels(ecg_channels)

    return raw, eeg_data, ecg_data

def find_best_ecg_channel(ecg_data):
    """
    找到信号质量最好的心电通道

    参数:
        ecg_data: 心电数据

    返回:
        best_channel: 最佳通道名称
        best_signal: 最佳通道信号
    """
    best_channel = None
    best_quality = -float('inf')
    best_signal = None

    for ch in ecg_data.ch_names:
        signal = ecg_data.get_data(picks=ch).flatten()

        # 清洗心电信号
        ecg_cleaned = nk.ecg_clean(signal, sampling_rate=SAMPLE_RATE)

        # 检测 R峰
        _, rpeaks = nk.ecg_peaks(ecg_cleaned, sampling_rate=SAMPLE_RATE)

        if 'ECG_R_Peaks' not in rpeaks or len(rpeaks['ECG_R_Peaks']) < 10:
            continue

        # 使用简单的方法评估信号质量
        # 计算R峰间隔的变异系数，越小越好
        rr_intervals = np.diff(rpeaks['ECG_R_Peaks']) / SAMPLE_RATE * 1000  # 转换为毫秒
        if len(rr_intervals) < 5:
            continue

        # 计算变异系数（标准差/平均值）
        cv = np.std(rr_intervals) / np.mean(rr_intervals)

        # 变异系数应该在合理范围内（0.05-0.15之间是正常的）
        if cv < 0.03 or cv > 0.2:
            continue

        # 计算信号质量分数
        quality_score = 1.0 - cv

        if quality_score > best_quality:
            best_quality = quality_score
            best_channel = ch
            best_signal = ecg_cleaned

    if best_channel is None:
        raise ValueError("无法找到合适的心电通道")

    print(f"选择的最佳心电通道: {best_channel}, 信号质量评分: {best_quality:.2f}")
    return best_channel, best_signal

def detect_r_peaks(ecg_signal, sampling_rate=SAMPLE_RATE):
    """
    检测心电信号中的R峰

    参数:
        ecg_signal: 心电信号
        sampling_rate: 采样率

    返回:
        r_peaks: R峰位置（样本点）
    """
    # 检测R峰
    _, rpeaks = nk.ecg_peaks(ecg_signal, sampling_rate=sampling_rate)

    if 'ECG_R_Peaks' not in rpeaks:
        raise ValueError("R峰检测失败")

    r_peaks = rpeaks['ECG_R_Peaks']
    print(f"检测到 {len(r_peaks)} 个R峰")

    return r_peaks

def extract_hep(eeg_data, r_peaks, tmin=HEP_START, tmax=HEP_END):
    """
    提取心跳诱发电位

    参数:
        eeg_data: 脑电数据
        r_peaks: R峰位置（样本点）
        tmin: 开始时间（秒）
        tmax: 结束时间（秒）

    返回:
        epochs: 心跳诱发电位epochs
        avg_hep: 平均心跳诱发电位
    """
    # 创建事件数组
    events = np.column_stack([r_peaks, np.zeros_like(r_peaks), np.ones_like(r_peaks)])

    # 创建epochs
    epochs = mne.Epochs(eeg_data, events, tmin=tmin, tmax=tmax,
                        baseline=None,  # 不进行基线校正，符合原文要求
                        preload=True,
                        reject=None,  # 不进行自动拒绝
                        verbose=False)

    # 计算平均HEP
    avg_hep = epochs.average()

    return epochs, avg_hep

def analyze_hep_by_stage(data_dir, subject_ids, stages):
    """
    按实验阶段分析HEP

    参数:
        data_dir: 数据目录
        subject_ids: 被试ID列表
        stages: 实验阶段列表

    返回:
        results: 分析结果字典，包含各阶段、各通道的HEP数据
    """
    results = {
        'subjects': [],    # 被试ID
        'stages': [],      # 实验阶段
        'hep_data': [],    # HEP数据
        'roi_values': [],  # 感兴趣区域的值
        'times': None,     # 时间点
        'ch_names': None   # 通道名称
    }

    # 记录已处理的文件，避免重复处理
    processed_files = set()

    for subject_id in subject_ids:
        print(f"\n处理被试 {subject_id}")

        for stage in stages:
            stage_info = stage_mapping.get(stage)
            if not stage_info:
                print(f"警告: 未知阶段 {stage}")
                continue

            # 构建文件路径
            file_pattern = f"{subject_id}_{stage_info[0]}_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{stage_info[1]}.fif"
            file_path = os.path.join(data_dir, file_pattern)

            # 检查是否已处理过该文件
            if file_path in processed_files:
                print(f"跳过已处理的文件: {file_path}")
                continue

            if not os.path.exists(file_path):
                print(f"警告: 文件不存在 {file_path}")
                continue

            # 标记文件为已处理
            processed_files.add(file_path)

            # 加载数据
            _, eeg_data, ecg_data = load_data(file_path)

            # 保存通道名称
            if results['ch_names'] is None:
                results['ch_names'] = eeg_data.ch_names

            # 找到最佳心电通道
            _, best_ecg = find_best_ecg_channel(ecg_data)

            # 检测R峰
            r_peaks = detect_r_peaks(best_ecg)

            # 提取HEP
            _, avg_hep = extract_hep(eeg_data, r_peaks)

            # 保存结果
            results['subjects'].append(subject_id)
            results['stages'].append(stage)
            results['hep_data'].append(avg_hep.data)

            # 保存时间轴
            if results['times'] is None:
                results['times'] = avg_hep.times

            # 提取感兴趣区域的值
            roi_mask = np.logical_and(avg_hep.times >= roi_time[0], avg_hep.times <= roi_time[1])

            # 查找感兴趣通道的索引
            roi_indices = []
            for ch in roi_channels:
                if ch in avg_hep.ch_names:
                    roi_indices.append(avg_hep.ch_names.index(ch))

            if roi_indices:
                # 对感兴趣的时间窗口和通道取平均值
                roi_values = {}
                for idx in roi_indices:
                    ch_name = avg_hep.ch_names[idx]
                    roi_values[ch_name] = np.mean(avg_hep.data[idx, roi_mask])

                results['roi_values'].append(roi_values)
            else:
                results['roi_values'].append({})

            print(f"成功处理 {subject_id} 的 {stage} 阶段")

    # 检查是否有数据被成功处理
    if not results['hep_data']:
        print("没有成功处理任何数据，请检查数据路径和文件是否存在")
        raise ValueError("没有成功处理任何数据")
    else:
        print(f"成功处理了 {len(results['hep_data'])} 个数据文件")

    return results

def plot_hep_by_stage_separate(results, output_dir=OUTPUT_DIR):
    """
    为每个实验阶段单独绘制HEP图，便于比较不同阶段的差异

    参数:
        results: 分析结果字典
        output_dir: 输出目录
    """
    if not results['hep_data']:
        print("没有可用的HEP数据进行绘图")
        return

    times = results['times']
    stages = sorted(set(results['stages']))

    # 为每个阶段创建单独的图
    for stage in stages:
        stage_indices = [i for i, s in enumerate(results['stages']) if s == stage]
        if not stage_indices:
            continue

        # 提取该阶段的所有数据
        stage_data = {}
        for ch_idx in range(min(63, results['hep_data'][0].shape[0])):
            ch_data = []
            for idx in stage_indices:
                if ch_idx < results['hep_data'][idx].shape[0]:
                    ch_data.append(results['hep_data'][idx][ch_idx])

            if ch_data:
                # 获取通道名称
                ch_name = results['ch_names'][ch_idx] if results['ch_names'] and ch_idx < len(results['ch_names']) else f"CH{ch_idx+1}"
                stage_data[ch_name] = ch_data

        if not stage_data:
            continue

        # 创建图表 (7x9布局，显示63个通道)
        fig, axes = plt.subplots(7, 9, figsize=(14, 10), sharex=True)
        fig.suptitle(f'心跳诱发电位 (HEP) - {stage_display_names.get(stage, stage)}', fontsize=14)

        # 将轴对象展平为一维数组
        axes = axes.flatten()

        # 自动确定所有通道的Y轴范围
        y_min, y_max = float('inf'), float('-inf')
        for ch_name, data_list in stage_data.items():
            data_mean = np.mean(data_list, axis=0)
            data_sem = stats.sem(data_list, axis=0)
            y_min = min(y_min, np.min(data_mean - data_sem))
            y_max = max(y_max, np.max(data_mean + data_sem))

        # 为了更好地可视化波形，增加一点边距
        y_margin = (y_max - y_min) * 0.2
        y_min -= y_margin
        y_max += y_margin

        # 绘制每个通道的数据
        for i, (ch_name, data_list) in enumerate(stage_data.items()):
            if i >= len(axes):
                break

            ax = axes[i]

            # 计算平均值和标准误
            mean_data = np.mean(data_list, axis=0)
            sem_data = stats.sem(data_list, axis=0)

            # 绘制平均值线
            ax.plot(times, mean_data, color=colors.get(stage, 'blue'), linewidth=1.2)

            # 绘制标准误区域
            ax.fill_between(times, mean_data - sem_data, mean_data + sem_data,
                            color=colors.get(stage, 'blue'), alpha=0.2)

            # 添加垂直线标记感兴趣的时间窗口
            ax.axvline(x=roi_time[0], color='gray', linestyle='--', linewidth=0.5)
            ax.axvline(x=roi_time[1], color='gray', linestyle='--', linewidth=0.5)

            # 标记R峰位置（0点）
            ax.axvline(x=0, color='black', linestyle='-', linewidth=0.5)

            # 添加水平线标记零点
            ax.axhline(y=0, color='black', linestyle='-', linewidth=0.5)

            # 设置通道标题
            ax.set_title(ch_name, fontsize=9)

            # 设置Y轴范围一致，使波形更容易比较
            ax.set_ylim(y_min, y_max)

            # 为感兴趣的通道添加强调
            if ch_name in roi_channels:
                # 加粗边框
                for spine in ax.spines.values():
                    spine.set_linewidth(1.5)
                    spine.set_color('red')

                # 添加时间窗口区域的背景色
                roi_mask = np.logical_and(times >= roi_time[0], times <= roi_time[1])
                ax.axvspan(roi_time[0], roi_time[1], alpha=0.1, color='yellow')

        # 去除未使用的子图
        for i in range(len(stage_data), len(axes)):
            fig.delaxes(axes[i])

        # 添加共享的横轴标签
        fig.text(0.5, 0.04, '时间 (秒)', ha='center', fontsize=10)
        fig.text(0.04, 0.5, '振幅 (μV)', va='center', rotation='vertical', fontsize=10)

        # 调整布局
        plt.tight_layout(rect=[0.05, 0.05, 1, 0.95])

        # 保存图像
        output_file = os.path.join(output_dir, f'HEP_channels_{stage}.svg')
        plt.savefig(output_file, format='svg', bbox_inches='tight')
        print(f"保存图像: {output_file}")
        plt.close(fig)

    print("所有阶段的分离图表绘制完成")

def plot_hep_roi_comparison(results, output_dir=OUTPUT_DIR):
    """
    绘制感兴趣通道的HEP对比图，所有阶段在一张图上对比

    参数:
        results: 分析结果字典
        output_dir: 输出目录
    """
    if not results['hep_data']:
        print("没有可用的HEP数据进行绘图")
        return

    times = results['times']
    stages = sorted(set(results['stages']))

    # 创建图表
    n_rows = 2 if len(roi_channels) <= 6 else 3
    n_cols = (len(roi_channels) + n_rows - 1) // n_rows
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(n_cols*4, n_rows*3.5), sharey=True)

    if n_rows == 1 and n_cols == 1:
        axes = np.array([axes])  # 让单个轴可用于索引

    # 将轴对象展平为一维数组
    axes = axes.flatten() if hasattr(axes, 'flatten') else [axes]

    # 自动确定所有通道的Y轴范围
    y_min, y_max = float('inf'), float('-inf')
    for ch_name in roi_channels:
        if results['ch_names'] and ch_name in results['ch_names']:
            ch_idx = results['ch_names'].index(ch_name)

            for stage in stages:
                stage_indices = [i for i, s in enumerate(results['stages']) if s == stage]
                if not stage_indices:
                    continue

                ch_data = []
                for idx in stage_indices:
                    if ch_idx < results['hep_data'][idx].shape[0]:
                        ch_data.append(results['hep_data'][idx][ch_idx])

                if ch_data:
                    data_mean = np.mean(ch_data, axis=0)
                    data_sem = stats.sem(ch_data, axis=0)
                    y_min = min(y_min, np.min(data_mean - data_sem))
                    y_max = max(y_max, np.max(data_mean + data_sem))

    # 为了更好地可视化波形，增加一点边距
    y_margin = (y_max - y_min) * 0.15
    y_min -= y_margin
    y_max += y_margin

    # 绘制每个ROI通道的数据
    for i, ch_name in enumerate(roi_channels):
        if i >= len(axes):
            break

        ax = axes[i]

        if results['ch_names'] and ch_name in results['ch_names']:
            ch_idx = results['ch_names'].index(ch_name)

            # 绘制每个阶段的数据
            for stage in stages:
                stage_indices = [i for i, s in enumerate(results['stages']) if s == stage]
                if not stage_indices:
                    continue

                ch_data = []
                for idx in stage_indices:
                    if ch_idx < results['hep_data'][idx].shape[0]:
                        ch_data.append(results['hep_data'][idx][ch_idx])

                if not ch_data:
                    continue

                # 计算平均值和标准误
                mean_data = np.mean(ch_data, axis=0)
                sem_data = stats.sem(ch_data, axis=0)

                # 绘制平均值线
                ax.plot(times, mean_data,
                         label=stage_display_names.get(stage, stage),
                         color=colors.get(stage, 'black'),
                         linewidth=1.5)

                # 绘制标准误区域
                ax.fill_between(times, mean_data - sem_data, mean_data + sem_data,
                                color=colors.get(stage, 'black'), alpha=0.15)

        # 添加垂直线标记感兴趣的时间窗口
        ax.axvline(x=roi_time[0], color='gray', linestyle='--', linewidth=0.8)
        ax.axvline(x=roi_time[1], color='gray', linestyle='--', linewidth=0.8)

        # 添加垂直线标记R峰（0点）
        ax.axvline(x=0, color='black', linestyle='-', linewidth=1.0)

        # 添加水平线标记零点
        ax.axhline(y=0, color='black', linestyle='-', linewidth=0.5)

        # 设置Y轴范围一致
        ax.set_ylim(y_min, y_max)

        # 标记感兴趣的时间窗口
        ax.axvspan(roi_time[0], roi_time[1], alpha=0.1, color='yellow')

        # 设置标题和轴标签
        ax.set_title(f'通道 {ch_name}', fontsize=11)
        ax.set_xlabel('时间 (秒)', fontsize=10)
        if i % n_cols == 0:  # 仅为最左侧的子图添加y轴标签
            ax.set_ylabel('振幅 (μV)', fontsize=10)

        # 添加网格线使波形更易读
        ax.grid(True, linestyle=':', alpha=0.3)

        # 添加次要刻度
        ax.xaxis.set_minor_locator(AutoMinorLocator(2))
        ax.yaxis.set_minor_locator(AutoMinorLocator(2))

    # 添加共享图例
    handles, labels = axes[0].get_legend_handles_labels()
    fig.legend(handles, labels, loc='upper center', bbox_to_anchor=(0.5, 0.98),
               ncol=len(stages), frameon=False, fontsize=10)

    # 调整布局
    plt.tight_layout(rect=[0, 0, 1, 0.95])  # 为图例留出空间

    # 保存图像
    output_file = os.path.join(output_dir, 'HEP_roi_channels_comparison.svg')
    plt.savefig(output_file, format='svg', bbox_inches='tight')
    print(f"保存图像: {output_file}")
    plt.close(fig)

def analyze_hep_statistically(results, output_dir=OUTPUT_DIR):
    """
    对HEP数据进行统计分析，特别关注ROI通道在感兴趣时间窗口的统计差异

    参数:
        results: 分析结果字典
        output_dir: 输出目录
    """
    if not results['hep_data']:
        print("没有可用的HEP数据进行统计分析")
        return

    stages = sorted(set(results['stages']))
    if len(stages) < 2:
        print("至少需要两个阶段进行比较")
        return

    # 统计结果
    stats_results = {
        'channel': [],        # 通道名称
        'stage1': [],         # 阶段1
        'stage2': [],         # 阶段2
        'mean1': [],          # 阶段1平均值
        'mean2': [],          # 阶段2平均值
        'sem1': [],           # 阶段1标准误
        'sem2': [],           # 阶段2标准误
        'paired_t': [],       # 配对t值
        'p_value': [],        # p值
        'sig_level': [],      # 显著性水平
        'effect_size': []     # 效应量
    }

    # 分析每个感兴趣通道在感兴趣时间窗口的数据
    for ch_name in roi_channels:
        if results['ch_names'] and ch_name in results['ch_names']:
            ch_idx = results['ch_names'].index(ch_name)

            # 提取各阶段在感兴趣时间窗口的数据
            stage_data = {}

            for stage in stages:
                # 获取该阶段的所有数据
                stage_indices = [i for i, s in enumerate(results['stages']) if s == stage]
                if not stage_indices:
                    continue

                # 提取该通道在感兴趣时间窗口的数据
                values = []
                subjects = []

                for idx in stage_indices:
                    subject = results['subjects'][idx]
                    if ch_idx < results['hep_data'][idx].shape[0]:
                        # 提取感兴趣时间窗口的数据
                        roi_mask = np.logical_and(results['times'] >= roi_time[0],
                                                 results['times'] <= roi_time[1])
                        roi_value = np.mean(results['hep_data'][idx][ch_idx, roi_mask])

                        values.append(roi_value)
                        subjects.append(subject)

                if values:
                    stage_data[stage] = {
                        'values': values,
                        'subjects': subjects,
                        'mean': np.mean(values),
                        'sem': stats.sem(values),
                        'std': np.std(values, ddof=1),
                        'n': len(values)
                    }

            # 进行每对阶段的统计比较
            for i, stage1 in enumerate(stages):
                if stage1 not in stage_data:
                    continue

                for j, stage2 in enumerate(stages[i+1:], i+1):
                    if stage2 not in stage_data:
                        continue

                    # 获取配对数据
                    paired_data = []
                    for s1, v1 in zip(stage_data[stage1]['subjects'], stage_data[stage1]['values']):
                        if s1 in stage_data[stage2]['subjects']:
                            idx2 = stage_data[stage2]['subjects'].index(s1)
                            v2 = stage_data[stage2]['values'][idx2]
                            paired_data.append((v1, v2))

                    if len(paired_data) >= 3:  # 至少需要3对数据进行配对t检验
                        values1 = [d[0] for d in paired_data]
                        values2 = [d[1] for d in paired_data]

                        # 进行配对t检验
                        t_stat, p_value = stats.ttest_rel(values1, values2)

                        # 计算效应量 (Cohen's d for paired samples)
                        mean_diff = np.mean(values1) - np.mean(values2)
                        pooled_std = np.sqrt((np.var(values1, ddof=1) + np.var(values2, ddof=1)) / 2)
                        effect_size = abs(mean_diff / pooled_std) if pooled_std != 0 else 0

                        # 标记显著性水平
                        if p_value < 0.001:
                            sig_level = '***'
                        elif p_value < 0.01:
                            sig_level = '**'
                        elif p_value < 0.05:
                            sig_level = '*'
                        else:
                            sig_level = 'ns'

                        # 保存结果
                        stats_results['channel'].append(ch_name)
                        stats_results['stage1'].append(stage_display_names.get(stage1, stage1))
                        stats_results['stage2'].append(stage_display_names.get(stage2, stage2))
                        stats_results['mean1'].append(np.mean(values1))
                        stats_results['mean2'].append(np.mean(values2))
                        stats_results['sem1'].append(stats.sem(values1))
                        stats_results['sem2'].append(stats.sem(values2))
                        stats_results['paired_t'].append(t_stat)
                        stats_results['p_value'].append(p_value)
                        stats_results['sig_level'].append(sig_level)
                        stats_results['effect_size'].append(effect_size)

    # 保存统计结果
    if stats_results['channel']:
        df = pd.DataFrame(stats_results)
        output_file = os.path.join(output_dir, 'HEP_statistical_results.csv')
        df.to_csv(output_file, index=False, float_format='%.6f')
        print(f"统计分析结果已保存到: {output_file}")

        # 返回结果用于绘图
        return df
    else:
        print("未找到足够的数据进行统计分析")
        return None

def plot_hep_statistical_comparison(stats_df, output_dir=OUTPUT_DIR):
    """
    绘制HEP统计分析结果图

    参数:
        stats_df: 统计分析结果数据框
        output_dir: 输出目录
    """
    if stats_df is None or stats_df.empty:
        print("没有统计分析结果可供绘图")
        return

    # 获取唯一的通道
    channels = stats_df['channel'].unique()

    # 对每个通道绘制条形图
    n_rows = 2 if len(channels) <= 6 else 3
    n_cols = (len(channels) + n_rows - 1) // n_rows

    fig, axes = plt.subplots(n_rows, n_cols, figsize=(n_cols*5, n_rows*4))

    if n_rows == 1 and n_cols == 1:
        axes = np.array([axes])

    # 将轴对象展平为一维数组
    axes = axes.flatten() if hasattr(axes, 'flatten') else [axes]

    for i, channel in enumerate(channels):
        if i >= len(axes):
            break

        ax = axes[i]

        # 获取该通道的数据
        channel_data = stats_df[stats_df['channel'] == channel]

        if channel_data.empty:
            continue

        # 获取该通道涉及的所有阶段
        all_stages = list(set(channel_data['stage1'].tolist() + channel_data['stage2'].tolist()))
        all_stages = sorted(all_stages, key=lambda x: list(stage_display_names.values()).index(x)
                           if x in stage_display_names.values() else 99)

        # 准备条形图数据
        means = []
        sems = []
        stage_colors = []

        for stage in all_stages:
            # 查找该阶段的数据（可能在stage1或stage2中）
            stage_mean = None
            stage_sem = None

            # 检查stage1
            stage1_data = channel_data[channel_data['stage1'] == stage]
            if not stage1_data.empty:
                stage_mean = stage1_data['mean1'].iloc[0]
                stage_sem = stage1_data['sem1'].iloc[0]

            # 检查stage2
            stage2_data = channel_data[channel_data['stage2'] == stage]
            if not stage2_data.empty:
                stage_mean = stage2_data['mean2'].iloc[0]
                stage_sem = stage2_data['sem2'].iloc[0]

            if stage_mean is not None and stage_sem is not None:
                means.append(stage_mean)
                sems.append(stage_sem)

                # 查找原始阶段名对应的颜色
                orig_stage = [k for k, v in stage_display_names.items() if v == stage]
                stage_color = colors.get(orig_stage[0] if orig_stage else 'gray', 'gray')
                stage_colors.append(stage_color)

        # 绘制条形图
        bars = ax.bar(range(len(all_stages)), means, yerr=sems,
                     color=stage_colors, edgecolor='black', linewidth=0.5,
                     capsize=5, error_kw={'elinewidth': 1, 'capthick': 1})

        # 设置x轴刻度标签
        ax.set_xticks(range(len(all_stages)))
        ax.set_xticklabels(all_stages, rotation=45, ha='right', fontsize=9)

        # 设置标题和轴标签
        ax.set_title(f'通道 {channel} 在 {roi_time[0]}-{roi_time[1]}s 的HEP振幅', fontsize=11)
        ax.set_ylabel('平均振幅 (μV)', fontsize=10)

        # 添加网格线
        ax.grid(True, axis='y', linestyle=':', alpha=0.3)

        # 添加显著性标记
        y_max = max(means) + max(sems) * 1.2
        y_range = y_max - min(means) + max(sems)

        # 创建阶段到索引的映射
        stage_to_idx = {stage: idx for idx, stage in enumerate(all_stages)}

        # 添加显著性标注
        annotations = []
        for _, row in channel_data.iterrows():
            stage1 = row['stage1']
            stage2 = row['stage2']
            p_value = row['p_value']
            sig_level = row['sig_level']

            if stage1 in stage_to_idx and stage2 in stage_to_idx:
                idx1 = stage_to_idx[stage1]
                idx2 = stage_to_idx[stage2]
                x1, x2 = idx1, idx2

                # 确保x1 < x2
                if x1 > x2:
                    x1, x2 = x2, x1

                # 计算标注高度，避免重叠
                used_heights = [a[1] for a in annotations if
                               (a[0] <= x1 <= a[2]) or (a[0] <= x2 <= a[2]) or
                               (x1 <= a[0] and x2 >= a[2])]

                if used_heights:
                    bar_height = y_max + y_range * 0.05 + y_range * 0.05 * len(used_heights)
                else:
                    bar_height = y_max + y_range * 0.05

                # 保存当前标注信息以便后续检查重叠
                annotations.append((x1, bar_height, x2))

                # 绘制连接线
                ax.plot([x1, x2], [bar_height, bar_height], 'k-', linewidth=0.8)

                # 添加显著性标记
                ax.text((x1 + x2) / 2, bar_height + y_range * 0.01, sig_level,
                       ha='center', va='bottom', fontsize=10)

                # 在显著性标记下方添加p值
                p_text = f'p={p_value:.3f}' if p_value >= 0.001 else 'p<0.001'
                ax.text((x1 + x2) / 2, bar_height - y_range * 0.02, p_text,
                       ha='center', va='top', fontsize=7)

        # 调整y轴范围以容纳标注
        if annotations:
            max_height = max([a[1] for a in annotations]) + y_range * 0.1
            ax.set_ylim(ax.get_ylim()[0], max_height)

    # 去除未使用的子图
    for i in range(len(channels), len(axes)):
        fig.delaxes(axes[i])

    # 调整布局
    plt.tight_layout()

    # 保存图像
    output_file = os.path.join(output_dir, 'HEP_statistical_comparison.svg')
    plt.savefig(output_file, format='svg', bbox_inches='tight')
    print(f"保存统计对比图: {output_file}")
    plt.close(fig)

def plot_hep_topography(results, output_dir=OUTPUT_DIR):
    """
    绘制HEP的头皮地形图

    参数:
        results: 分析结果字典
        output_dir: 输出目录
    """
    if not results['hep_data'] or not results['ch_names']:
        print("没有可用的HEP数据进行头皮地形图绘制")
        return

    times = results['times']
    stages = sorted(set(results['stages']))

    # 创建info对象用于地形图绘制
    montage = mne.channels.make_standard_montage('standard_1020')
    info = mne.create_info(ch_names=results['ch_names'][:EEG_CHANNELS],
                          sfreq=SAMPLE_RATE, ch_types='eeg')
    try:
        info.set_montage(montage)
    except Exception as e:
        print(f"设置蒙太奇时出错: {str(e)}")
        return

    # 关注的时间窗口 (250-450ms)
    roi_mask = np.logical_and(times >= roi_time[0], times <= roi_time[1])

    # 创建一个图，显示所有阶段在ROI时间窗口的头皮分布
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    axes = axes.flatten()

    fig.suptitle(f'心跳诱发电位 (HEP) 在 {roi_time[0]}-{roi_time[1]}s 的头皮分布', fontsize=14)

    vmin, vmax = None, None  # 用于统一色标
    stage_data = {}

    # 第一遍循环，计算所有阶段的数据和统一的色标范围
    for i, stage in enumerate(stages):
        if i >= len(axes):
            break

        # 获取该阶段的所有数据
        stage_indices = [i for i, s in enumerate(results['stages']) if s == stage]
        if not stage_indices:
            continue

        # 提取该阶段的HEP数据并计算平均值
        all_hep_data = []
        for idx in stage_indices:
            if results['hep_data'][idx].shape[0] >= EEG_CHANNELS:
                # 计算ROI时间窗口的平均值
                roi_data = np.mean(results['hep_data'][idx][:EEG_CHANNELS, roi_mask], axis=1)
                all_hep_data.append(roi_data)

        if all_hep_data:
            # 计算该阶段所有被试的平均HEP
            avg_hep = np.mean(all_hep_data, axis=0)
            stage_data[stage] = avg_hep

            # 更新全局最小值和最大值
            stage_min = np.min(avg_hep)
            stage_max = np.max(avg_hep)

            if vmin is None or stage_min < vmin:
                vmin = stage_min
            if vmax is None or stage_max > vmax:
                vmax = stage_max

    # 第二遍循环，使用统一的色标绘制地形图
    for i, stage in enumerate(stages):
        if i >= len(axes) or stage not in stage_data:
            continue

        # 绘制地形图
        try:
            # 尝试使用vmin和vmax参数
            im, _ = mne.viz.plot_topomap(stage_data[stage], info, axes=axes[i],
                                      show=False, contours=6, cmap='RdBu_r',
                                      vmin=vmin, vmax=vmax)
        except TypeError:
            # 如果不支持vmin和vmax参数，使用vlim参数
            im, _ = mne.viz.plot_topomap(stage_data[stage], info, axes=axes[i],
                                      show=False, contours=6, cmap='RdBu_r',
                                      vlim=(vmin, vmax))

        # 设置标题
        axes[i].set_title(f'{stage_display_names.get(stage, stage)}', fontsize=11)

    # 去除未使用的子图
    for i in range(len(stages), len(axes)):
        fig.delaxes(axes[i])

    # 添加颜色条
    cbar_ax = fig.add_axes([0.92, 0.2, 0.02, 0.6])
    cbar = fig.colorbar(im, cax=cbar_ax)
    cbar.set_label('振幅 (μV)', fontsize=10)

    # 调整布局
    plt.tight_layout(rect=[0, 0, 0.9, 0.95])

    # 保存图像
    output_file = os.path.join(output_dir, 'HEP_roi_topography.svg')
    plt.savefig(output_file, format='svg', bbox_inches='tight')
    print(f"保存头皮地形图: {output_file}")
    plt.close(fig)

    print("头皮地形图绘制完成")

def plot_fz_special_component(results, output_dir=OUTPUT_DIR):
    """
    专门绘制Fz通道在R波后250-450ms处的显著成分，参考Schandry等人的研究

    参数:
        results: 分析结果字典
        output_dir: 输出目录
    """
    if not results['hep_data']:
        print("没有可用的HEP数据进行Fz通道分析")
        return

    times = results['times']
    stages = sorted(set(results['stages']))

    # 定义阶段颜色映射（确保colors变量在函数内部定义）
    stage_colors = {
        'prac': '#E63946',  # 红色系(练习阶段)
        'rest1': '#457B9D',  # 蓝色系(静息态1)
        'test1': '#1D3557',  # 深蓝色(刺激态1)
        'rest2': '#43AA8B',  # 绿色系(静息态2)
        'test2': '#254441',  # 深绿色(刺激态2)
        'rest3': '#577590',  # 蓝紫色(静息态3)
        'test3': '#2A3D45',  # 深蓝紫色(刺激态3)
    }

    # 查找Fz通道的索引
    if 'Fz' not in results['ch_names']:
        print("数据中未找到Fz通道")
        return

    fz_idx = results['ch_names'].index('Fz')

    # 创建图形
    fig, ax = plt.subplots(figsize=(10, 6))
    fig.suptitle('Fz通道在R波后250-450ms的心跳诱发电位(HEP)', fontsize=14)

    # 筛选感兴趣时间窗口的数据
    roi_mask = np.logical_and(times >= roi_time[0], times <= roi_time[1])

    # 存储统计比较结果
    stats_results = []

    # 绘制各阶段的Fz波形
    for stage in stages:
        stage_indices = [i for i, s in enumerate(results['stages']) if s == stage]
        if not stage_indices:
            continue

        # 提取该阶段在Fz通道的数据
        fz_data = []
        for idx in stage_indices:
            if fz_idx < results['hep_data'][idx].shape[0]:
                fz_data.append(results['hep_data'][idx][fz_idx])

        if not fz_data:
            continue

        # 计算平均值和标准误
        mean_data = np.mean(fz_data, axis=0)
        sem_data = stats.sem(fz_data, axis=0)

        # 绘制平均值线
        ax.plot(times, mean_data,
                 label=stage_display_names.get(stage, stage),
                 color=stage_colors.get(stage, 'black'),
                 linewidth=2.0)

        # 绘制标准误区域
        ax.fill_between(times, mean_data - sem_data, mean_data + sem_data,
                        color=stage_colors.get(stage, 'black'), alpha=0.15)

        # 计算ROI区域的平均值
        roi_mean = np.mean(mean_data[roi_mask])
        roi_sem = np.mean(sem_data[roi_mask])

        # 存储统计数据
        stats_results.append({
            'stage': stage,
            'display_name': stage_display_names.get(stage, stage),
            'mean': roi_mean,
            'sem': roi_sem,
            'color': stage_colors.get(stage, 'black')
        })

    # 添加垂直线标记感兴趣的时间窗口
    ax.axvline(x=roi_time[0], color='gray', linestyle='--', linewidth=1.0, alpha=0.7)
    ax.axvline(x=roi_time[1], color='gray', linestyle='--', linewidth=1.0, alpha=0.7)

    # 添加垂直线标记R峰（0点）
    ax.axvline(x=0, color='black', linestyle='-', linewidth=1.5)

    # 添加水平线标记零点
    ax.axhline(y=0, color='black', linestyle='-', linewidth=0.8, alpha=0.5)

    # 标记感兴趣的时间窗口
    ax.axvspan(roi_time[0], roi_time[1], alpha=0.1, color='yellow')

    # 添加文本标注说明该区域
    y_min, y_max = ax.get_ylim()
    y_text = y_min + (y_max - y_min) * 0.9
    ax.text(np.mean(roi_time), y_text,
            f'Schandry等人发现的显著诱发成分\n(250-450ms)',
            ha='center', va='center', fontsize=10,
            bbox=dict(facecolor='white', alpha=0.7, edgecolor='gray', boxstyle='round,pad=0.5'))

    # 设置标题和轴标签
    ax.set_xlabel('时间 (秒)', fontsize=12)
    ax.set_ylabel('振幅 (μV)', fontsize=12)
    ax.set_title('Fz通道的心跳诱发电位 (HEP)', fontsize=13)

    # 添加网格线使波形更易读
    ax.grid(True, linestyle=':', alpha=0.3)

    # 添加次要刻度
    ax.xaxis.set_minor_locator(AutoMinorLocator(2))
    ax.yaxis.set_minor_locator(AutoMinorLocator(2))

    # 添加图例
    ax.legend(loc='upper right', fontsize=10)

    # 调整布局
    plt.tight_layout()

    # 保存图像
    output_file = os.path.join(output_dir, 'HEP_Fz_roi_component.svg')
    plt.savefig(output_file, format='svg', bbox_inches='tight')
    print(f"保存Fz通道专题图: {output_file}")
    plt.close(fig)

    # 绘制ROI区域的条形图对比
    if len(stats_results) > 1:
        fig, ax = plt.subplots(figsize=(8, 5))

        # 准备数据
        stages = [s['display_name'] for s in stats_results]
        means = [s['mean'] for s in stats_results]
        sems = [s['sem'] for s in stats_results]
        colors = [s['color'] for s in stats_results]

        # 绘制条形图
        ax.bar(range(len(stages)), means, yerr=sems,
                     color=colors, edgecolor='black', linewidth=0.5,
                     capsize=5, error_kw={'elinewidth': 1, 'capthick': 1})

        # 设置x轴刻度标签
        ax.set_xticks(range(len(stages)))
        ax.set_xticklabels(stages, rotation=45, ha='right', fontsize=10)

        # 计算显著性差异 (仅比较相邻阶段)
        for i in range(len(stats_results) - 1):
            for j in range(i + 1, len(stats_results)):
                s1 = stats_results[i]['stage']
                s2 = stats_results[j]['stage']

                # 获取这两个阶段的数据
                stage1_indices = [idx for idx, s in enumerate(results['stages']) if s == s1]
                stage2_indices = [idx for idx, s in enumerate(results['stages']) if s == s2]

                if not stage1_indices or not stage2_indices:
                    continue

                # 提取ROI区域的均值数据
                values1 = []
                values2 = []

                for idx in stage1_indices:
                    if fz_idx < results['hep_data'][idx].shape[0]:
                        roi_value = np.mean(results['hep_data'][idx][fz_idx, roi_mask])
                        values1.append(roi_value)

                for idx in stage2_indices:
                    if fz_idx < results['hep_data'][idx].shape[0]:
                        roi_value = np.mean(results['hep_data'][idx][fz_idx, roi_mask])
                        values2.append(roi_value)

                if len(values1) < 3 or len(values2) < 3:
                    continue

                # 进行T检验
                _, p_value = stats.ttest_ind(values1, values2, equal_var=False)

                # 如果显著，添加标记
                if p_value < 0.05:
                    # 计算标记高度
                    y_start = max(means[i] + sems[i], means[j] + sems[j]) + 0.1

                    # 绘制显著性标记
                    ax.plot([i, j], [y_start, y_start], 'k-', linewidth=1.0)

                    # 标记星号表示显著性水平
                    if p_value < 0.001:
                        sig_symbol = '***'
                    elif p_value < 0.01:
                        sig_symbol = '**'
                    else:
                        sig_symbol = '*'

                    ax.text((i + j) / 2, y_start + 0.05, sig_symbol, ha='center', va='bottom', fontsize=12)

                    # 添加p值标签
                    p_text = f'p={p_value:.3f}' if p_value >= 0.001 else 'p<0.001'
                    ax.text((i + j) / 2, y_start - 0.1, p_text, ha='center', va='top', fontsize=8)

        # 设置标题和轴标签
        ax.set_title(f'Fz通道在{roi_time[0]}-{roi_time[1]}s的HEP振幅对比', fontsize=13)
        ax.set_ylabel('平均振幅 (μV)', fontsize=12)

        # 添加网格线
        ax.grid(True, axis='y', linestyle=':', alpha=0.3)

        # 调整Y轴范围，留出足够空间显示显著性标记
        y_min, y_max = ax.get_ylim()
        ax.set_ylim(y_min, y_max * 1.2)

        # 调整布局
        plt.tight_layout()

        # 保存图像
        output_file = os.path.join(output_dir, 'HEP_Fz_roi_comparison.svg')
        plt.savefig(output_file, format='svg', bbox_inches='tight')
        print(f"保存Fz通道ROI比较图: {output_file}")
        plt.close(fig)

def save_results(results, output_dir=OUTPUT_DIR):
    """
    保存分析结果到文件，以便后续可视化使用

    参数:
        results: 分析结果字典
        output_dir: 输出目录
    """
    # 创建保存结果的目录
    results_dir = os.path.join(output_dir, 'data')
    os.makedirs(results_dir, exist_ok=True)

    # 保存结果
    result_file = os.path.join(results_dir, 'hep_analysis_results.npz')

    # 将结果转换为可保存的格式
    save_dict = {
        'subjects': np.array(results['subjects']),
        'stages': np.array(results['stages']),
        'hep_data': np.array(results['hep_data']),
        'times': results['times'],
        'ch_names': np.array(results['ch_names'])
    }

    # 保存ROI值（需要特殊处理，因为是字典列表）
    roi_values_list = results['roi_values']
    roi_values_keys = set()
    for roi_dict in roi_values_list:
        roi_values_keys.update(roi_dict.keys())

    roi_values_keys = sorted(list(roi_values_keys))
    roi_values_array = np.zeros((len(roi_values_list), len(roi_values_keys)))

    for i, roi_dict in enumerate(roi_values_list):
        for j, key in enumerate(roi_values_keys):
            roi_values_array[i, j] = roi_dict.get(key, np.nan)

    save_dict['roi_values_array'] = roi_values_array
    save_dict['roi_values_keys'] = np.array(roi_values_keys)

    # 保存为npz文件
    np.savez(result_file, **save_dict)
    print(f"分析结果已保存到: {result_file}")

    return result_file

def load_results(file_path):
    """
    从文件加载分析结果

    参数:
        file_path: 结果文件路径

    返回:
        results: 分析结果字典
    """
    # 加载npz文件
    data = np.load(file_path, allow_pickle=True)

    # 重建结果字典
    results = {
        'subjects': data['subjects'].tolist(),
        'stages': data['stages'].tolist(),
        'hep_data': data['hep_data'].tolist(),
        'times': data['times'],
        'ch_names': data['ch_names'].tolist(),
        'roi_values': []
    }

    # 重建ROI值
    roi_values_array = data['roi_values_array']
    roi_values_keys = data['roi_values_keys']

    for i in range(roi_values_array.shape[0]):
        roi_dict = {}
        for j, key in enumerate(roi_values_keys):
            if not np.isnan(roi_values_array[i, j]):
                roi_dict[key] = roi_values_array[i, j]
        results['roi_values'].append(roi_dict)

    return results

def main():
    """主函数"""
    # 数据目录
    data_dir = DATA_DIR

    # 创建输出目录
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    print("开始处理心跳诱发电位(HEP)分析...")

    # 获取所有被试ID
    subject_ids = []
    if not os.path.exists(data_dir):
        print(f"错误: 数据目录不存在: {data_dir}")
        return

    for file in os.listdir(data_dir):
        if file.endswith('.fif') and '_prac.fif' in file:
            subject_id = file.split('_')[0]
            if subject_id not in subject_ids:
                subject_ids.append(subject_id)

    subject_ids.sort()
    print(f"找到 {len(subject_ids)} 个被试: {subject_ids}")

    # 定义实验阶段
    stages = ['prac', 'rest1', 'test1', 'rest2', 'test2', 'rest3', 'test3']

    # 分析HEP
    results = analyze_hep_by_stage(data_dir, subject_ids, stages)

    # 如果有数据被成功处理，保存结果
    if results['hep_data']:
        # 保存分析结果
        save_results(results)

        print("心跳诱发电位(HEP)分析完成！")
        print(f"结果保存在 {OUTPUT_DIR} 目录中。")
    else:
        print("没有数据被成功处理，无法生成结果。")

if __name__ == "__main__":
    main()