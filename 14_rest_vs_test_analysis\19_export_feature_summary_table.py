#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
19. 自动生成特征对比总表（论文用）
- 读取18_频段特征统计分阶段.xlsx
- 提取每个对比组下主要特征统计（如均值差异、p值、t值、效应量等）
- 合并为"特征为行、对比为列"的总表
- 导出为Excel，便于论文排版
"""
import pandas as pd

# 统计结果文件
file = r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/18_频段特征统计分阶段.xlsx'
xls = pd.ExcelFile(file)
compare_names = xls.sheet_names

# 关心的特征统计项（可根据实际表格调整）
stat_rows = [
    '均值差异(μV)(均值)', '均值差异(μV)(标准差)', 'p值(均值)', 'p值(最小值)', 't值(均值)', '效应量(均值)',
    '峰峰值变化(均值)', '峰峰值变化(标准差)', 'RMS变化(均值)', 'RMS变化(标准差)',
    '标准差变化(均值)', '标准差变化(标准差)', '最大差值(均值)', '最大差值(标准差)',
    # 可补充更多
]

# 合并所有对比组
all_tables = []
for compare in compare_names:
    df = pd.read_excel(xls, sheet_name=compare)
    df = df[df['特征统计'].isin(stat_rows)]
    df = df.set_index('特征统计')
    # 只保留脑区_频段列
    value_cols = [c for c in df.columns if c != '特征统计']
    df = df[value_cols]
    # 列名加对比组前缀
    df.columns = [f"{compare}_{c}" for c in df.columns]
    all_tables.append(df)

# 合并为总表
result = pd.concat(all_tables, axis=1)
result = result.loc[[r for r in stat_rows if r in result.index]]  # 保证顺序
result.reset_index(inplace=True)

# 导出
output_path = r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/19_特征对比总表.xlsx'
result.to_excel(output_path, index=False)
print(f"已导出特征对比总表：{output_path}") 