#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查HEP原始数据提取统计信息
"""

import pandas as pd
import os
import h5py

# 读取统计信息CSV文件
stats_dir = "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/raw_epochs"
stats_file = "extraction_stats_20250519_020416.csv"
stats_path = os.path.join(stats_dir, stats_file)

print(f"读取统计信息文件: {stats_path}")
stats_df = pd.read_csv(stats_path)

# 显示统计信息
print("\n===== 提取统计信息 =====")
print(stats_df)

# 只检查第一个HDF5文件的详细内容
if len(stats_df) > 0:
    h5_file = stats_df['file_path'].iloc[0]
    print(f"\n检查第一个HDF5文件: {h5_file}")

    with h5py.File(h5_file, 'r') as f:
        # 显示文件结构
        print("\nHDF5文件结构:")
        def print_attrs(name, obj):
            print(f"  {name}: {type(obj)}")
            if hasattr(obj, 'attrs'):
                for key, val in obj.attrs.items():
                    print(f"    - {key}: {val}")

        f.visititems(print_attrs)

        # 显示数据形状
        print("\n数据形状:")
        print(f"  data: {f['data'].shape}")
        print(f"  times: {f['times'].shape}")
        print(f"  ch_names: {f['ch_names'].shape}")
        print(f"  subject_ids: {f['subject_ids'].shape}")
        print(f"  stages: {f['stages'].shape}")

        # 显示部分数据
        print("\n数据样本(前5个时间点，前3个通道，第1个epoch):")
        print(f['data'][0, :3, :5])

        print("\n时间点样本(前10个):")
        print(f['times'][:10])

        print("\n通道名称样本(前10个):")
        ch_names = [f['ch_names'][i] for i in range(min(10, len(f['ch_names'])))]
        print(ch_names)

        print("\n被试ID样本(前10个):")
        subject_ids = [f['subject_ids'][i] for i in range(min(10, len(f['subject_ids'])))]
        print(subject_ids)

print("\n检查完成")
