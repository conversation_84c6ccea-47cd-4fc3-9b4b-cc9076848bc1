#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
心跳诱发电位(HEP)数据提取脚本
将提取的HEP数据保存为文件，以便后续可视化

功能：
- 从原始EEG/ECG数据中提取HEP成分
- 检测ECG中的R峰
- 以R峰为中心创建epochs
- 合并多个被试的数据
- 保存为.fif格式文件

用法：
python 01_extract_hep_data.py
"""

import os
import numpy as np
import mne
import neurokit2 as nk
import pickle

# 数据路径
DATA_DIR = r"D:\ecgeeg\19-eegecg手动预处理6-ICA3"
# 输出路径
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_data"
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 定义EEG通道
EEG_CHANNELS = ['Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2',
                'F7', 'F8', 'T7', 'T8', 'P7', 'P8', 'Fz', 'Cz', 'Pz', 'FC1',
                'FC2', 'CP1', 'CP2', 'FC5', 'FC6', 'CP5', 'CP6', 'FT9', 'FT10',
                'TP9', 'TP10', 'F1', 'F2', 'C1', 'C2', 'P1', 'P2', 'AF3', 'AF4',
                'FC3', 'FC4', 'CP3', 'CP4', 'PO3', 'PO4', 'F5', 'F6', 'C5', 'C6',
                'P5', 'P6', 'AF7', 'AF8', 'FT7', 'FT8', 'TP7', 'TP8', 'PO7', 'PO8',
                'Fpz', 'CPz', 'POz', 'Oz']

# 定义ECG通道
ECG_CHANNELS = [f'ECG{i}' for i in range(1, 59)]

# 定义阶段名称
STAGES = {
    'prac': '练习阶段',
    'rest1': '静息态1',
    'test1': '刺激态1',
    'rest2': '静息态2',
    'test2': '刺激态2',
    'rest3': '静息态3',
    'test3': '刺激态3'
}

# 定义HEP时间窗口
HEP_TMIN = -0.2  # R波前200ms
HEP_TMAX = 1.0   # R波后1000ms

def load_data(subject_id, stage):
    """
    加载指定被试和阶段的数据

    参数:
    subject_id (int): 被试ID
    stage (str): 实验阶段 ('prac', 'rest1', 'test1', 'rest2', 'test2', 'rest3', 'test3')

    返回:
    mne.Raw: 加载的数据
    """
    # 根据阶段确定文件名模式
    if stage == 'prac':
        # 练习阶段
        file_pattern = f"{subject_id:02d}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_prac.fif"
    elif stage == 'rest1':
        # 静息态1
        file_pattern = f"{subject_id:02d}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_rest.fif"
    elif stage == 'test1':
        # 刺激态1
        file_pattern = f"{subject_id:02d}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_test.fif"
    elif stage == 'rest2':
        # 静息态2
        file_pattern = f"{subject_id:02d}_02_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_rest.fif"
        # 如果上面的模式不存在，尝试另一种模式
        alt_pattern = f"{subject_id:02d}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_rest.fif"
    elif stage == 'test2':
        # 刺激态2
        file_pattern = f"{subject_id:02d}_02_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_test.fif"
        # 如果上面的模式不存在，尝试另一种模式
        alt_pattern = f"{subject_id:02d}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_test.fif"
    elif stage == 'rest3':
        # 静息态3
        file_pattern = f"{subject_id:02d}_03_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_rest.fif"
        # 如果上面的模式不存在，尝试另一种模式
        alt_pattern = f"{subject_id:02d}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_rest.fif"
    elif stage == 'test3':
        # 刺激态3
        file_pattern = f"{subject_id:02d}_03_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_test.fif"
        # 如果上面的模式不存在，尝试另一种模式
        alt_pattern = f"{subject_id:02d}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_test.fif"
    else:
        print(f"未知阶段: {stage}")
        return None

    # 构建文件路径
    file_path = os.path.join(DATA_DIR, file_pattern)

    # 如果文件不存在且有替代模式，尝试替代模式
    if not os.path.exists(file_path) and stage in ['rest2', 'test2', 'rest3', 'test3']:
        file_path = os.path.join(DATA_DIR, alt_pattern)

    # 加载数据
    if os.path.exists(file_path):
        raw = mne.io.read_raw_fif(file_path, preload=True)
        print(f"成功加载数据: {file_path}")
        return raw
    else:
        print(f"文件不存在: {file_path}")
        return None

def extract_r_peaks(raw, ecg_channel='ECG1'):
    """
    从ECG信号中提取R峰

    参数:
    raw (mne.Raw): 原始数据
    ecg_channel (str): ECG通道名称

    返回:
    numpy.ndarray: R峰的采样点索引
    """
    # 提取ECG信号
    ecg_data = raw.copy().pick_channels([ecg_channel]).get_data()
    ecg_data = ecg_data.flatten()

    # 使用NeuroKit2检测R峰
    sampling_rate = raw.info['sfreq']
    _, info = nk.ecg_process(ecg_data, sampling_rate=sampling_rate)
    r_peaks = info['ECG_R_Peaks']

    return r_peaks

def extract_hep(raw, r_peaks, tmin=HEP_TMIN, tmax=HEP_TMAX):
    """
    提取心跳诱发电位

    参数:
    raw (mne.Raw): 原始数据
    r_peaks (numpy.ndarray): R峰的采样点索引
    tmin (float): 相对于R峰的起始时间（秒）
    tmax (float): 相对于R峰的结束时间（秒）

    返回:
    mne.Epochs: 提取的HEP epochs
    """
    # 创建事件数组
    events = np.column_stack([r_peaks, np.zeros_like(r_peaks), np.ones_like(r_peaks)])

    # 提取EEG通道
    raw_eeg = raw.copy().pick_channels(EEG_CHANNELS)

    # 创建epochs
    epochs = mne.Epochs(raw_eeg, events, event_id=1, tmin=tmin, tmax=tmax,
                        baseline=None, preload=True, reject=None)

    return epochs

def analyze_hep(subjects, stages):
    """
    分析多个被试的HEP数据

    参数:
    subjects (list): 被试ID列表
    stages (list): 实验阶段列表

    返回:
    dict: 各阶段的HEP数据
    """
    all_hep_data = {}

    for stage in stages:
        all_hep_data[stage] = []

    for subject_id in subjects:
        print(f"\n处理被试 {subject_id}")

        for stage in stages:
            print(f"  处理阶段: {stage}")

            # 加载数据
            raw = load_data(subject_id, stage)
            if raw is None:
                continue

            # 提取R峰
            r_peaks = extract_r_peaks(raw)
            if len(r_peaks) == 0:
                print(f"  未检测到R峰，跳过")
                continue

            # 提取HEP
            epochs = extract_hep(raw, r_peaks)

            # 存储数据
            all_hep_data[stage].append(epochs)

    # 合并各被试的数据
    for stage in stages:
        if all_hep_data[stage]:
            all_hep_data[stage] = mne.concatenate_epochs(all_hep_data[stage])
        else:
            all_hep_data[stage] = None

    return all_hep_data

def save_hep_data(hep_data, output_dir):
    """
    保存HEP数据

    参数:
    hep_data (dict): 各阶段的HEP数据
    output_dir (str): 输出目录
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 保存每个阶段的数据
    for stage, data in hep_data.items():
        if data is not None:
            # 保存为fif格式
            output_path = os.path.join(output_dir, f'hep_{stage}.fif')
            data.save(output_path, overwrite=True)
            print(f"已保存 {stage} 阶段的HEP数据至: {output_path}")

def main():
    """主函数"""
    # 设置被试ID列表
    subjects = list(range(9, 33))  # 从09到32号被试

    # 设置实验阶段
    stages = ['prac', 'rest1', 'test1', 'rest2', 'test2', 'rest3', 'test3']

    # 分析HEP数据
    hep_data = analyze_hep(subjects, stages)

    # 保存HEP数据
    save_hep_data(hep_data, OUTPUT_DIR)

if __name__ == "__main__":
    main()
