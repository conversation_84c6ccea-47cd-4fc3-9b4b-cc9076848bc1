#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HEP分析实验 - 特征提取模块

功能：
- 从预处理好的HEP数据中提取特征
- 提取不同时间窗口(0.2-0.4s、0.4-0.6s、0.5-0.7s)的HEP特征
- 提取关键通道(Fz、Cz、Pz等)的数据
- 保存特征为标准格式，以便后续分析使用

作者：AI助手
日期：2024年
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import mne
import argparse
from datetime import datetime

# 定义常量
ROOT_DIR = r"D:/ecgeeg/30-数据分析/5-NeuroKit2/result"
DATA_DIR = r"D:/ecgeeg/19-eegecg手动预处理6-ICA3"
OUTPUT_DIR = os.path.join(ROOT_DIR, 'hep_analysis', 'features')

# 定义时间窗口
TIME_WINDOWS = [(0.2, 0.4), (0.4, 0.6), (0.5, 0.7)]

# 定义关键通道
KEY_CHANNELS = ['Fz', 'Cz', 'Pz', 'FCz', 'CPz']

# 定义通道组
CHANNEL_GROUPS = {
    'frontal': ['Fp1', 'Fp2', 'F3', 'F4', 'F7', 'F8', 'Fz'],
    'central': ['C3', 'C4', 'Cz', 'FCz', 'FC1', 'FC2', 'FC3', 'FC4'],
    'parietal': ['P3', 'P4', 'Pz', 'CPz', 'CP1', 'CP2', 'CP3', 'CP4'],
    'temporal': ['T7', 'T8'],
    'occipital': ['O1', 'O2', 'P7', 'P8'],
    'left': ['Fp1', 'F3', 'F7', 'C3', 'T7', 'P3', 'P7', 'O1', 'FC3', 'CP3'],
    'right': ['Fp2', 'F4', 'F8', 'C4', 'T8', 'P4', 'P8', 'O2', 'FC4', 'CP4'],
    'midline': ['Fz', 'FCz', 'Cz', 'CPz', 'Pz']
}

# 定义阶段
STAGES = ['prac', 'rest1', 'test1', 'rest2', 'test2', 'rest3', 'test3']

# 定义阶段映射
STAGE_MAPPING = {
    'prac': ('01', 'prac'),   # 练习阶段: 01_prac
    'rest1': ('01', 'rest'),  # 静息态1: 01_rest
    'test1': ('01', 'test'),  # 刺激态1: 01_test
    'rest2': ('02', 'rest'),  # 静息态2: 02_rest
    'test2': ('02', 'test'),  # 刺激态2: 02_test
    'rest3': ('03', 'rest'),  # 静息态3: 03_rest
    'test3': ('03', 'test')   # 刺激态3: 03_test
}

# 定义阶段显示名称
STAGE_DISPLAY = {
    'prac': '练习阶段',
    'rest1': '静息态1',
    'test1': '刺激态1',
    'rest2': '静息态2',
    'test2': '刺激态2',
    'rest3': '静息态3',
    'test3': '刺激态3'
}

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='HEP特征提取')
    parser.add_argument('--data_dir', type=str, default=DATA_DIR,
                        help='预处理数据目录')
    parser.add_argument('--output_dir', type=str, default=OUTPUT_DIR,
                        help='输出目录')
    parser.add_argument('--channels', type=str, default=','.join(KEY_CHANNELS),
                        help='关键通道列表，用逗号分隔')
    parser.add_argument('--all_channels', action='store_true',
                        help='是否提取所有通道的特征')
    parser.add_argument('--time_windows', type=str, default='0.2-0.4,0.4-0.6,0.5-0.7',
                        help='时间窗口列表，用逗号分隔，格式为"start-end"')
    parser.add_argument('--fast_mode', action='store_true',
                        help='快速模式，只处理少量数据用于测试')
    parser.add_argument('--max_subjects', type=int, default=2,
                        help='最大处理的被试数量，仅在快速模式下有效')
    parser.add_argument('--max_epochs', type=int, default=10,
                        help='每个被试最大处理的epoch数量，仅在快速模式下有效')
    return parser.parse_args()

def load_hep_data(data_dir, stage, fast_mode=False, max_subjects=2, max_epochs=10):
    """
    加载指定阶段的HEP数据

    参数:
    data_dir (str): 数据目录
    stage (str): 实验阶段
    fast_mode (bool): 是否使用快速模式
    max_subjects (int): 最大处理的被试数量，仅在快速模式下有效
    max_epochs (int): 每个被试最大处理的epoch数量，仅在快速模式下有效

    返回:
    mne.Epochs: 加载的HEP数据
    """
    print(f"加载{STAGE_DISPLAY[stage]}阶段的HEP数据...")

    # 获取轮次ID和状态
    round_id, state = STAGE_MAPPING[stage]

    # 查找所有符合条件的文件
    all_files = []
    all_subjects = []

    # 遍历数据目录
    for file in os.listdir(data_dir):
        # 检查文件是否符合条件
        if file.endswith(f'_{state}.fif') and f'_{round_id}_' in file:
            file_path = os.path.join(data_dir, file)

            # 从文件名中提取被试ID
            subject_id = file.split('_')[0]

            all_files.append(file_path)
            all_subjects.append(subject_id)

    if not all_files:
        print(f"错误: 未找到{stage}阶段的数据文件")
        exit(1)

    print(f"找到{len(all_files)}个{stage}阶段的数据文件")

    # 加载所有文件并合并
    all_epochs = []

    # 限制处理的被试数量
    if fast_mode:
        print(f"快速模式: 只处理前{max_subjects}个被试，每个被试最多{max_epochs}个epochs")
        subject_limit = max_subjects
    else:
        # 处理所有被试
        subject_limit = len(all_subjects)

    processed_subjects = 0

    for file_path, subject_id in zip(all_files, all_subjects):
        # 限制处理的被试数量
        if processed_subjects >= subject_limit:
            break

        # 加载数据
        raw = mne.io.read_raw_fif(file_path, preload=True)

        # 检测R峰
        ecg_channel = None
        for ch in raw.ch_names:
            if 'ECG' in ch.upper():
                ecg_channel = ch
                break

        if ecg_channel is None:
            print(f"错误: 文件 {file_path} 中未找到ECG通道")
            exit(1)

        # 使用MNE的find_ecg_events函数检测R峰
        events = mne.preprocessing.find_ecg_events(raw, ch_name=ecg_channel, verbose=False)

        # 提取HEP epochs
        epochs = mne.Epochs(raw, events[0], tmin=-0.2, tmax=0.8, baseline=None, preload=True)

        # 在快速模式下限制epoch数量
        if fast_mode and len(epochs) > max_epochs:
            print(f"快速模式: 限制被试 {subject_id} 的epochs数量为{max_epochs}")
            epochs = epochs[:max_epochs]

        # 添加metadata
        epochs.metadata = pd.DataFrame({
            'subject_id': [subject_id] * len(epochs),
            'stage': [stage] * len(epochs)
        })

        all_epochs.append(epochs)
        processed_subjects += 1

        print(f"成功加载被试 {subject_id} 的{stage}阶段数据，包含{len(epochs)}个epochs")

    if not all_epochs:
        print(f"错误: 未能成功加载任何{stage}阶段的数据")
        exit(1)

    # 合并所有epochs
    merged_epochs = mne.concatenate_epochs(all_epochs)

    print(f"成功加载{STAGE_DISPLAY[stage]}阶段的数据，包含{len(merged_epochs)}个epochs，来自{len(merged_epochs.metadata['subject_id'].unique())}个被试")
    return merged_epochs

def extract_features(epochs, time_windows, channels=None):
    """
    提取HEP特征

    参数:
    epochs (mne.Epochs): HEP数据
    time_windows (list): 时间窗口列表
    channels (list): 通道列表，如果为None则提取所有通道

    返回:
    pd.DataFrame: 提取的特征
    """
    print("提取HEP特征...")

    # 如果未指定通道，则使用所有通道
    if channels is None:
        channels = epochs.ch_names

    # 创建特征列表
    features = []

    # 获取所有被试ID
    subject_ids = epochs.metadata['subject_id'].unique()

    # 对每个被试进行处理
    for subject_id in subject_ids:
        print(f"处理被试 {subject_id}...")

        # 获取该被试的数据
        subj_epochs = epochs[epochs.metadata['subject_id'] == subject_id]

        # 对每个通道进行处理
        for channel in channels:
            if channel in subj_epochs.ch_names:
                # 获取通道索引
                ch_idx = subj_epochs.ch_names.index(channel)

                # 获取通道数据
                channel_data = subj_epochs.get_data()[:, ch_idx, :]

                # 对每个时间窗口进行处理
                for time_window in time_windows:
                    # 获取时间窗口索引
                    time_mask = (subj_epochs.times >= time_window[0]) & (subj_epochs.times <= time_window[1])

                    # 计算时间窗口内的平均值
                    window_mean = np.mean(channel_data[:, time_mask], axis=1)

                    # 计算每个epoch的平均值
                    for i, epoch_mean in enumerate(window_mean):
                        # 获取epoch的metadata
                        epoch_metadata = subj_epochs.metadata.iloc[i]

                        # 添加特征
                        features.append({
                            'subject_id': subject_id,
                            'channel': channel,
                            'window_start': time_window[0],
                            'window_end': time_window[1],
                            'epoch_index': i,
                            'mean_amplitude': epoch_mean,
                            'stage': epoch_metadata.get('stage', None)
                        })

    # 转换为DataFrame
    features_df = pd.DataFrame(features)

    print(f"成功提取特征，共{len(features_df)}条记录")
    return features_df

def extract_channel_group_features(epochs, time_windows, channel_groups=CHANNEL_GROUPS):
    """
    提取通道组的HEP特征

    参数:
    epochs (mne.Epochs): HEP数据
    time_windows (list): 时间窗口列表
    channel_groups (dict): 通道组字典

    返回:
    pd.DataFrame: 提取的特征
    """
    print("提取通道组HEP特征...")

    # 创建特征列表
    features = []

    # 获取所有被试ID
    subject_ids = epochs.metadata['subject_id'].unique()

    # 对每个被试进行处理
    for subject_id in subject_ids:
        print(f"处理被试 {subject_id}...")

        # 获取该被试的数据
        subj_epochs = epochs[epochs.metadata['subject_id'] == subject_id]

        # 对每个通道组进行处理
        for group_name, group_channels in channel_groups.items():
            # 筛选存在的通道
            available_channels = [ch for ch in group_channels if ch in subj_epochs.ch_names]

            if not available_channels:
                continue

            # 获取通道索引
            ch_indices = [subj_epochs.ch_names.index(ch) for ch in available_channels]

            # 对每个时间窗口进行处理
            for time_window in time_windows:
                # 获取时间窗口索引
                time_mask = (subj_epochs.times >= time_window[0]) & (subj_epochs.times <= time_window[1])

                # 计算每个epoch在该通道组和时间窗口内的平均值
                for i in range(len(subj_epochs)):
                    # 获取epoch的数据
                    epoch_data = subj_epochs.get_data()[i]

                    # 计算通道组在时间窗口内的平均值
                    group_mean = np.mean([np.mean(epoch_data[ch_idx, time_mask]) for ch_idx in ch_indices])

                    # 获取epoch的metadata
                    epoch_metadata = subj_epochs.metadata.iloc[i]

                    # 添加特征
                    features.append({
                        'subject_id': subject_id,
                        'channel_group': group_name,
                        'window_start': time_window[0],
                        'window_end': time_window[1],
                        'epoch_index': i,
                        'mean_amplitude': group_mean,
                        'stage': epoch_metadata.get('stage', None)
                    })

    # 转换为DataFrame
    features_df = pd.DataFrame(features)

    print(f"成功提取通道组特征，共{len(features_df)}条记录")
    return features_df

def aggregate_features(features_df):
    """
    聚合特征，计算每个被试在每个通道和时间窗口的平均值

    参数:
    features_df (pd.DataFrame): 原始特征

    返回:
    pd.DataFrame: 聚合后的特征
    """
    print("聚合特征...")

    # 按被试ID、通道、时间窗口和阶段分组，计算平均值
    if 'channel' in features_df.columns:
        agg_features = features_df.groupby(['subject_id', 'channel', 'window_start', 'window_end', 'stage']).agg({
            'mean_amplitude': ['mean', 'std', 'count']
        }).reset_index()
    else:
        agg_features = features_df.groupby(['subject_id', 'channel_group', 'window_start', 'window_end', 'stage']).agg({
            'mean_amplitude': ['mean', 'std', 'count']
        }).reset_index()

    # 重命名列
    agg_features.columns = ['_'.join(col).strip('_') for col in agg_features.columns.values]

    print(f"成功聚合特征，共{len(agg_features)}条记录")
    return agg_features

def save_features(features_df, output_dir, filename):
    """
    保存特征

    参数:
    features_df (pd.DataFrame): 特征
    output_dir (str): 输出目录
    filename (str): 文件名
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 构建文件路径
    file_path = os.path.join(output_dir, filename)

    # 保存为CSV
    features_df.to_csv(file_path, index=False)
    print(f"已保存特征至: {file_path}")

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 解析通道列表
    if args.all_channels:
        channels = None
    else:
        channels = args.channels.split(',')

    # 解析时间窗口列表
    time_windows = []
    for window_str in args.time_windows.split(','):
        start, end = map(float, window_str.split('-'))
        time_windows.append((start, end))

    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)

    # 如果是快速模式，只处理部分阶段
    if args.fast_mode:
        print("快速模式: 只处理静息态1和刺激态1")
        stages_to_process = ['rest1', 'test1']
    else:
        stages_to_process = STAGES

    # 处理每个阶段
    for stage in stages_to_process:
        # 加载数据
        epochs = load_hep_data(args.data_dir, stage, args.fast_mode, args.max_subjects, args.max_epochs)

        # 添加阶段信息到metadata
        if 'stage' not in epochs.metadata.columns:
            epochs.metadata['stage'] = stage

        # 提取特征
        features = extract_features(epochs, time_windows, channels)

        # 提取通道组特征
        group_features = extract_channel_group_features(epochs, time_windows)

        # 聚合特征
        agg_features = aggregate_features(features)
        agg_group_features = aggregate_features(group_features)

        # 保存特征
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        save_features(features, args.output_dir, f'{stage}_features_{timestamp}.csv')
        save_features(group_features, args.output_dir, f'{stage}_group_features_{timestamp}.csv')
        save_features(agg_features, args.output_dir, f'{stage}_agg_features_{timestamp}.csv')
        save_features(agg_group_features, args.output_dir, f'{stage}_agg_group_features_{timestamp}.csv')

    print("特征提取完成")

if __name__ == "__main__":
    main()
