#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HEP原始数据分析脚本

功能：
- 加载HEP原始数据文件(.h5格式)
- 分析数据结构和内容
- 可视化HEP波形

作者：AI助手
日期：2024年
"""

import numpy as np
import pandas as pd
import os
import h5py
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
import matplotlib as mpl
import argparse
from tqdm import tqdm

# 设置中文字体
try:
    font = FontProperties(fname=r"C:\Windows\Fonts\LXGW WenKai Mono.ttf")
    plt.rcParams['font.family'] = ['sans-serif']
    plt.rcParams['font.sans-serif'] = ['LXGW WenKai']
    plt.rcParams['axes.unicode_minus'] = False
    mpl.rcParams['font.size'] = 10
except:
    print("警告: 未找到LXGW WenKai字体，使用系统默认字体")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='HEP原始数据分析')
    parser.add_argument('--data_dir', type=str,
                        default="D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/raw_epochs",
                        help='数据目录')
    parser.add_argument('--output_dir', type=str,
                        default="D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/analysis_results",
                        help='输出目录')
    parser.add_argument('--stage', type=str, default="rest1",
                        help='要分析的阶段')
    parser.add_argument('--channels', type=str, default="Fz,Cz,Pz",
                        help='要分析的通道，用逗号分隔')
    return parser.parse_args()

def load_h5_file(file_path):
    """
    加载HDF5文件

    参数:
    file_path (str): 文件路径

    返回:
    dict: 包含数据的字典
    """
    print(f"加载HDF5文件: {file_path}")

    with h5py.File(file_path, 'r') as f:
        # 获取数据
        data = f['data'][:]
        times = f['times'][:]
        ch_names = [ch.decode('utf-8') for ch in f['ch_names'][:]]
        subject_ids = [subj.decode('utf-8') for subj in f['subject_ids'][:]]
        stages = [stg.decode('utf-8') for stg in f['stages'][:]]

        # 获取属性
        attrs = dict(f.attrs)

    print(f"成功加载数据: {data.shape[0]}个epochs, {data.shape[1]}个通道, {data.shape[2]}个时间点")

    return {
        'data': data,
        'times': times,
        'ch_names': ch_names,
        'subject_ids': subject_ids,
        'stages': stages,
        'attrs': attrs
    }

def plot_average_hep(data_dict, channels, output_dir, stage):
    """
    绘制平均HEP波形

    参数:
    data_dict (dict): 包含数据的字典
    channels (list): 要分析的通道列表
    output_dir (str): 输出目录
    stage (str): 实验阶段
    """
    print(f"绘制{stage}阶段的平均HEP波形...")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 获取数据
    data = data_dict['data']
    times = data_dict['times']
    ch_names = data_dict['ch_names']
    subject_ids = data_dict['subject_ids']

    # 获取通道索引
    ch_indices = [ch_names.index(ch) for ch in channels if ch in ch_names]

    if not ch_indices:
        print(f"错误: 未找到指定的通道: {channels}")
        return

    # 计算每个被试的平均HEP
    unique_subjects = np.unique(subject_ids)

    # 创建一个字典，用于存储每个被试的数据索引
    subject_indices = {subj: [] for subj in unique_subjects}
    for i, subj in enumerate(subject_ids):
        subject_indices[subj].append(i)

    # 计算每个被试在每个通道上的平均HEP
    subject_means = np.zeros((len(unique_subjects), len(ch_indices), len(times)))

    for i, subj in enumerate(unique_subjects):
        indices = subject_indices[subj]
        subject_data = data[indices]
        subject_means[i] = np.mean(subject_data[:, ch_indices, :], axis=0)

    # 计算所有被试的总平均HEP
    grand_mean = np.mean(subject_means, axis=0)

    # 计算标准误差
    sem = np.std(subject_means, axis=0) / np.sqrt(len(unique_subjects))

    # 绘制平均HEP波形
    plt.figure(figsize=(12, 8))

    for i, ch_idx in enumerate(ch_indices):
        ch_name = ch_names[ch_idx]
        plt.subplot(len(ch_indices), 1, i+1)

        # 绘制平均波形
        plt.plot(times, grand_mean[i], 'b-', linewidth=2, label=f'{ch_name} 平均')

        # 绘制标准误差区间
        plt.fill_between(times,
                         grand_mean[i] - sem[i],
                         grand_mean[i] + sem[i],
                         color='b', alpha=0.2)

        # 添加零线
        plt.axhline(y=0, color='k', linestyle='-', alpha=0.2)

        # 添加R波时刻的垂直线
        plt.axvline(x=0, color='r', linestyle='--', alpha=0.5, label='R波')

        # 添加时间窗口
        for start, end in [(0.2, 0.4), (0.4, 0.6), (0.5, 0.7)]:
            plt.axvspan(start, end, color='g', alpha=0.1)
            plt.text((start+end)/2, plt.ylim()[0]*0.9, f'{start}-{end}s',
                     horizontalalignment='center', color='g')

        plt.title(f'{ch_name} 通道的平均HEP波形 ({stage}阶段)')
        plt.xlabel('时间 (秒)')
        plt.ylabel('振幅 (μV)')
        plt.legend()
        plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'{stage}_average_hep.png'))
    plt.close()

    print(f"已保存平均HEP波形图至: {os.path.join(output_dir, f'{stage}_average_hep.png')}")

    # 绘制所有通道的热图
    plt.figure(figsize=(12, 8))
    plt.imshow(grand_mean, aspect='auto',
               extent=[times[0], times[-1], len(ch_indices)-0.5, -0.5],
               cmap='RdBu_r', origin='upper')
    plt.colorbar(label='振幅 (μV)')
    plt.yticks(range(len(ch_indices)), [ch_names[idx] for idx in ch_indices])
    plt.axvline(x=0, color='k', linestyle='--', alpha=0.5, label='R波')

    # 添加时间窗口
    for start, end in [(0.2, 0.4), (0.4, 0.6), (0.5, 0.7)]:
        plt.axvspan(start, end, color='g', alpha=0.1)
        plt.text((start+end)/2, -0.4, f'{start}-{end}s',
                 horizontalalignment='center', color='g')

    plt.title(f'{stage}阶段的HEP热图')
    plt.xlabel('时间 (秒)')
    plt.ylabel('通道')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'{stage}_hep_heatmap.png'))
    plt.close()

    print(f"已保存HEP热图至: {os.path.join(output_dir, f'{stage}_hep_heatmap.png')}")

    return grand_mean, sem

def analyze_time_windows(data_dict, channels, output_dir, stage):
    """
    分析不同时间窗口的HEP振幅

    参数:
    data_dict (dict): 包含数据的字典
    channels (list): 要分析的通道列表
    output_dir (str): 输出目录
    stage (str): 实验阶段
    """
    print(f"分析{stage}阶段的不同时间窗口...")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 获取数据
    data = data_dict['data']
    times = data_dict['times']
    ch_names = data_dict['ch_names']
    subject_ids = data_dict['subject_ids']

    # 获取通道索引
    ch_indices = [ch_names.index(ch) for ch in channels if ch in ch_names]

    if not ch_indices:
        print(f"错误: 未找到指定的通道: {channels}")
        return

    # 定义时间窗口
    time_windows = [(0.2, 0.4), (0.4, 0.6), (0.5, 0.7)]

    # 创建一个字典，用于存储每个被试的数据索引
    unique_subjects = np.unique(subject_ids)
    subject_indices = {subj: [] for subj in unique_subjects}
    for i, subj in enumerate(subject_ids):
        subject_indices[subj].append(i)

    # 计算每个被试在每个通道和每个时间窗口的平均振幅
    results = []

    for subj in unique_subjects:
        indices = subject_indices[subj]
        subject_data = data[indices]

        for ch_idx in ch_indices:
            ch_name = ch_names[ch_idx]

            for start, end in time_windows:
                # 获取时间窗口索引
                time_mask = (times >= start) & (times <= end)

                # 计算平均振幅
                mean_amp = np.mean(subject_data[:, ch_idx, :][:, time_mask], axis=(0, 1))

                results.append({
                    'subject_id': subj,
                    'channel': ch_name,
                    'window_start': start,
                    'window_end': end,
                    'mean_amplitude': mean_amp,
                    'stage': stage
                })

    # 转换为DataFrame
    results_df = pd.DataFrame(results)

    # 保存结果
    results_path = os.path.join(output_dir, f'{stage}_time_window_analysis.csv')
    results_df.to_csv(results_path, index=False)

    print(f"已保存时间窗口分析结果至: {results_path}")

    # 绘制不同时间窗口的平均振幅条形图
    plt.figure(figsize=(12, 8))

    for i, ch_name in enumerate(channels):
        if ch_name not in ch_names:
            continue

        plt.subplot(len(channels), 1, i+1)

        # 筛选该通道的数据
        ch_data = results_df[results_df['channel'] == ch_name]

        # 按时间窗口分组计算平均值和标准误差
        window_means = ch_data.groupby(['window_start', 'window_end'])['mean_amplitude'].mean()
        window_sems = ch_data.groupby(['window_start', 'window_end'])['mean_amplitude'].sem()

        # 绘制条形图
        window_labels = [f"{start}-{end}s" for start, end in time_windows]
        x = np.arange(len(window_labels))

        plt.bar(x, window_means, yerr=window_sems, capsize=5)
        plt.xticks(x, window_labels)
        plt.title(f'{ch_name} 通道在不同时间窗口的平均HEP振幅 ({stage}阶段)')
        plt.xlabel('时间窗口')
        plt.ylabel('平均振幅 (μV)')
        plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'{stage}_time_window_comparison.png'))
    plt.close()

    print(f"已保存时间窗口比较图至: {os.path.join(output_dir, f'{stage}_time_window_comparison.png')}")

    return results_df

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    print(f"分析{args.stage}阶段的HEP数据...")
    print(f"数据目录: {args.data_dir}")
    print(f"输出目录: {args.output_dir}")
    print(f"分析通道: {args.channels}")

    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    print(f"已创建输出目录: {args.output_dir}")

    # 查找指定阶段的HDF5文件
    h5_files = [f for f in os.listdir(args.data_dir) if f.startswith(f"{args.stage}_raw_epochs") and f.endswith('.h5')]

    print(f"找到{len(h5_files)}个{args.stage}阶段的HDF5文件:")
    for f in h5_files:
        print(f"  - {f}")

    if not h5_files:
        print(f"错误: 未找到{args.stage}阶段的HDF5文件")
        return

    # 选择最新的文件
    latest_file = max([os.path.join(args.data_dir, f) for f in h5_files], key=os.path.getmtime)
    print(f"选择最新的文件: {os.path.basename(latest_file)}")

    # 加载数据
    data_dict = load_h5_file(latest_file)

    # 解析通道列表
    channels = args.channels.split(',')
    print(f"解析通道列表: {channels}")

    # 检查通道是否存在
    available_channels = data_dict['ch_names']
    found_channels = [ch for ch in channels if ch in available_channels]
    missing_channels = [ch for ch in channels if ch not in available_channels]

    print(f"找到的通道: {found_channels}")
    if missing_channels:
        print(f"警告: 未找到以下通道: {missing_channels}")
        print(f"可用的通道: {available_channels[:10]}...")

    # 绘制平均HEP波形
    print("\n绘制平均HEP波形...")
    grand_mean, sem = plot_average_hep(data_dict, found_channels, args.output_dir, args.stage)

    # 分析不同时间窗口
    print("\n分析不同时间窗口...")
    results_df = analyze_time_windows(data_dict, found_channels, args.output_dir, args.stage)

    print(f"\n{args.stage}阶段的HEP数据分析完成")
    print(f"结果已保存至: {args.output_dir}")

if __name__ == "__main__":
    main()
