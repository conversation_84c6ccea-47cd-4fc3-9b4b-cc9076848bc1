#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HEP最佳时间窗口扩展范围总结可视化

本脚本创建扩展时间范围(-200到1000ms)的HEP最佳时间窗口综合总结图
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
import csv

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-NeuroKit2'
RESULT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\14_rest_vs_test_analysis"

def load_extended_results():
    """加载扩展分析结果"""
    results = []
    csv_path = os.path.join(RESULT_DIR, 'optimal_windows_results_extended.csv')
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            # 转换数值类型
            for key in ['window_id', 'peak_time_ms', 'peak_value_uv', 'window_start_ms', 
                       'window_end_ms', 'window_length_ms', 'freq_diff_hz', 
                       'window_mean_diff_uv', 'rest1_mean_uv', 'rest3_mean_uv']:
                row[key] = float(row[key])
            results.append(row)
    
    return results

def create_extended_timeline_visualization():
    """创建扩展时间线可视化"""
    results = load_extended_results()
    
    # 创建大图
    fig, axes = plt.subplots(2, 1, figsize=(20, 12))
    
    # 获取电极列表
    electrodes = sorted(list(set(r['electrode'] for r in results)))
    colors = plt.cm.tab10(np.linspace(0, 1, len(electrodes)))
    
    # 上图：完整时间线上的窗口分布
    ax1 = axes[0]
    
    # 绘制时间轴背景
    time_range = np.arange(-200, 1001, 50)
    ax1.plot(time_range, np.zeros_like(time_range), 'k-', alpha=0.2, linewidth=1)
    
    # 标记重要时间点
    ax1.axvline(x=0, color='red', linestyle='--', alpha=0.8, linewidth=3, label='R波')
    ax1.axvspan(200, 500, alpha=0.1, color='yellow', label='分析窗口 (200-500ms)')
    
    # 绘制每个窗口
    y_positions = {}
    y_offset = 0
    
    for i, result in enumerate(results):
        electrode = result['electrode']
        if electrode not in y_positions:
            y_positions[electrode] = y_offset
            y_offset += 1
        
        y_pos = y_positions[electrode]
        window_start = result['window_start_ms']
        window_end = result['window_end_ms']
        peak_time = result['peak_time_ms']
        peak_value = result['peak_value_uv']
        window_id = result['window_id']
        
        # 窗口区间
        color_idx = electrodes.index(electrode)
        alpha = 0.9 if window_id == 1 else 0.6
        linewidth = 10 if window_id == 1 else 6
        
        ax1.plot([window_start, window_end], [y_pos, y_pos], 
                color=colors[color_idx], linewidth=linewidth, alpha=alpha)
        
        # 峰值点
        marker = 'o' if window_id == 1 else 's'
        marker_size = 120 if window_id == 1 else 80
        ax1.scatter(peak_time, y_pos, color=colors[color_idx], s=marker_size, 
                   marker=marker, zorder=5, edgecolor='black', linewidth=1)
        
        # 添加数值标注
        offset = 0.2 if window_id == 1 else -0.2
        ax1.text(peak_time, y_pos + offset, f'{peak_value:.2f}', 
                ha='center', va='center', fontsize=8, fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
    
    # 设置y轴标签
    ax1.set_yticks(list(y_positions.values()))
    ax1.set_yticklabels(list(y_positions.keys()))
    ax1.set_xlabel('时间 (ms, R波为0)')
    ax1.set_ylabel('电极')
    ax1.set_title('前额叶电极HEP最佳时间窗口分布 (扩展范围: -200 to 1000ms)', 
                 fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(-200, 1000)
    ax1.set_ylim(-0.5, len(electrodes) - 0.5)
    ax1.legend(loc='upper right')
    
    # 下图：峰值时间和差值的散点图
    ax2 = axes[1]
    
    # 按窗口类型分组
    window1_data = [r for r in results if r['window_id'] == 1]
    window2_data = [r for r in results if r['window_id'] == 2]
    
    # 绘制散点图
    for data, label, marker, size in [(window1_data, '窗口1', 'o', 100), 
                                     (window2_data, '窗口2', 's', 80)]:
        peak_times = [r['peak_time_ms'] for r in data]
        peak_values = [r['peak_value_uv'] for r in data]
        electrode_colors = [colors[electrodes.index(r['electrode'])] for r in data]
        
        scatter = ax2.scatter(peak_times, peak_values, c=electrode_colors, 
                            s=size, marker=marker, alpha=0.8, 
                            edgecolors='black', linewidth=1, label=label)
        
        # 添加电极标签
        for r in data:
            ax2.annotate(r['electrode'], 
                        (r['peak_time_ms'], r['peak_value_uv']),
                        xytext=(5, 5), textcoords='offset points',
                        fontsize=8, alpha=0.7)
    
    # 标记重要时间区域
    ax2.axvspan(200, 500, alpha=0.1, color='yellow', label='分析窗口')
    ax2.axvline(x=0, color='red', linestyle='--', alpha=0.5, label='R波')
    
    ax2.set_xlabel('峰值时间 (ms)')
    ax2.set_ylabel('峰值差值 (μV)')
    ax2.set_title('峰值时间 vs 差值强度分布', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    ax2.set_xlim(-200, 1000)
    
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(RESULT_DIR, 'extended_timeline_summary.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.savefig(save_path.replace('.png', '.svg'), bbox_inches='tight')
    print(f"扩展时间线总结图已保存: {save_path}")
    plt.close()

def create_electrode_comparison_matrix():
    """创建电极对比矩阵"""
    results = load_extended_results()
    
    # 获取电极列表
    electrodes = sorted(list(set(r['electrode'] for r in results)))
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 准备数据矩阵
    window1_times = np.zeros(len(electrodes))
    window1_values = np.zeros(len(electrodes))
    window2_times = np.zeros(len(electrodes))
    window2_values = np.zeros(len(electrodes))
    
    for i, electrode in enumerate(electrodes):
        electrode_results = [r for r in results if r['electrode'] == electrode]
        window1 = [r for r in electrode_results if r['window_id'] == 1][0]
        window2 = [r for r in electrode_results if r['window_id'] == 2][0]
        
        window1_times[i] = window1['peak_time_ms']
        window1_values[i] = window1['peak_value_uv']
        window2_times[i] = window2['peak_time_ms']
        window2_values[i] = window2['peak_value_uv']
    
    # 1. 窗口1峰值时间
    ax1 = axes[0, 0]
    bars1 = ax1.bar(range(len(electrodes)), window1_times, color='steelblue', alpha=0.7)
    ax1.set_xticks(range(len(electrodes)))
    ax1.set_xticklabels(electrodes, rotation=45)
    ax1.set_ylabel('峰值时间 (ms)')
    ax1.set_title('窗口1 - 峰值时间分布')
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标注
    for bar, time in zip(bars1, window1_times):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'{time:.0f}', ha='center', va='bottom', fontsize=9)
    
    # 2. 窗口1峰值差值
    ax2 = axes[0, 1]
    bars2 = ax2.bar(range(len(electrodes)), window1_values, color='orange', alpha=0.7)
    ax2.set_xticks(range(len(electrodes)))
    ax2.set_xticklabels(electrodes, rotation=45)
    ax2.set_ylabel('峰值差值 (μV)')
    ax2.set_title('窗口1 - 峰值差值分布')
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标注
    for bar, value in zip(bars2, window1_values):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{value:.3f}', ha='center', va='bottom', fontsize=9)
    
    # 3. 窗口2峰值时间
    ax3 = axes[1, 0]
    bars3 = ax3.bar(range(len(electrodes)), window2_times, color='green', alpha=0.7)
    ax3.set_xticks(range(len(electrodes)))
    ax3.set_xticklabels(electrodes, rotation=45)
    ax3.set_ylabel('峰值时间 (ms)')
    ax3.set_title('窗口2 - 峰值时间分布')
    ax3.grid(True, alpha=0.3)
    
    # 添加数值标注
    for bar, time in zip(bars3, window2_times):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'{time:.0f}', ha='center', va='bottom', fontsize=9)
    
    # 4. 窗口2峰值差值
    ax4 = axes[1, 1]
    bars4 = ax4.bar(range(len(electrodes)), window2_values, color='red', alpha=0.7)
    ax4.set_xticks(range(len(electrodes)))
    ax4.set_xticklabels(electrodes, rotation=45)
    ax4.set_ylabel('峰值差值 (μV)')
    ax4.set_title('窗口2 - 峰值差值分布')
    ax4.grid(True, alpha=0.3)
    
    # 添加数值标注
    for bar, value in zip(bars4, window2_values):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{value:.3f}', ha='center', va='bottom', fontsize=9)
    
    plt.suptitle('前额叶电极HEP最佳窗口对比分析', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(RESULT_DIR, 'electrode_comparison_matrix.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.savefig(save_path.replace('.png', '.svg'), bbox_inches='tight')
    print(f"电极对比矩阵已保存: {save_path}")
    plt.close()

def create_time_distribution_analysis():
    """创建时间分布分析图"""
    results = load_extended_results()
    
    # 提取数据
    peak_times = [r['peak_time_ms'] for r in results]
    peak_values = [r['peak_value_uv'] for r in results]
    window_ids = [r['window_id'] for r in results]
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(16, 10))
    
    # 1. 峰值时间直方图
    ax1 = axes[0, 0]
    ax1.hist(peak_times, bins=15, alpha=0.7, color='steelblue', edgecolor='black')
    ax1.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='R波')
    ax1.axvspan(200, 500, alpha=0.2, color='yellow', label='分析窗口')
    ax1.set_xlabel('峰值时间 (ms)')
    ax1.set_ylabel('频次')
    ax1.set_title('峰值时间分布直方图')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 峰值差值直方图
    ax2 = axes[0, 1]
    ax2.hist(peak_values, bins=15, alpha=0.7, color='orange', edgecolor='black')
    ax2.set_xlabel('峰值差值 (μV)')
    ax2.set_ylabel('频次')
    ax2.set_title('峰值差值分布直方图')
    ax2.grid(True, alpha=0.3)
    
    # 3. 按窗口类型分组的时间分布
    ax3 = axes[1, 0]
    window1_times = [r['peak_time_ms'] for r in results if r['window_id'] == 1]
    window2_times = [r['peak_time_ms'] for r in results if r['window_id'] == 2]
    
    ax3.hist([window1_times, window2_times], bins=12, alpha=0.7, 
             color=['blue', 'red'], label=['窗口1', '窗口2'], edgecolor='black')
    ax3.axvline(x=0, color='black', linestyle='--', alpha=0.7, label='R波')
    ax3.axvspan(200, 500, alpha=0.2, color='yellow', label='分析窗口')
    ax3.set_xlabel('峰值时间 (ms)')
    ax3.set_ylabel('频次')
    ax3.set_title('按窗口类型分组的时间分布')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 时间vs差值的相关性分析
    ax4 = axes[1, 1]
    window1_data = [(r['peak_time_ms'], r['peak_value_uv']) for r in results if r['window_id'] == 1]
    window2_data = [(r['peak_time_ms'], r['peak_value_uv']) for r in results if r['window_id'] == 2]
    
    if window1_data:
        w1_times, w1_values = zip(*window1_data)
        ax4.scatter(w1_times, w1_values, color='blue', alpha=0.7, s=80, label='窗口1')
    
    if window2_data:
        w2_times, w2_values = zip(*window2_data)
        ax4.scatter(w2_times, w2_values, color='red', alpha=0.7, s=80, label='窗口2')
    
    ax4.axvline(x=0, color='black', linestyle='--', alpha=0.5, label='R波')
    ax4.axvspan(200, 500, alpha=0.1, color='yellow', label='分析窗口')
    ax4.set_xlabel('峰值时间 (ms)')
    ax4.set_ylabel('峰值差值 (μV)')
    ax4.set_title('时间 vs 差值相关性')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(RESULT_DIR, 'time_distribution_analysis.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.savefig(save_path.replace('.png', '.svg'), bbox_inches='tight')
    print(f"时间分布分析图已保存: {save_path}")
    plt.close()

def main():
    """主函数"""
    print("="*60)
    print("HEP最佳时间窗口扩展范围总结可视化")
    print("="*60)
    
    print("1. 创建扩展时间线可视化...")
    create_extended_timeline_visualization()
    
    print("2. 创建电极对比矩阵...")
    create_electrode_comparison_matrix()
    
    print("3. 创建时间分布分析...")
    create_time_distribution_analysis()
    
    print("\n扩展范围总结可视化完成！")
    print(f"结果保存在: {RESULT_DIR}")

if __name__ == "__main__":
    main()
