#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HEP最佳时间窗口分析总结可视化

本脚本创建HEP最佳时间窗口分析的综合总结图
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
import csv
import pandas as pd

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-NeuroKit2'
RESULT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\14_rest_vs_test_analysis"

def load_results():
    """加载分析结果"""
    results = []
    csv_path = os.path.join(RESULT_DIR, 'optimal_windows_results.csv')
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            # 转换数值类型
            for key in ['window_id', 'peak_time_ms', 'peak_value_uv', 'window_start_ms', 
                       'window_end_ms', 'window_length_ms', 'freq_diff_hz', 
                       'window_mean_diff_uv', 'rest1_mean_uv', 'rest3_mean_uv']:
                row[key] = float(row[key])
            results.append(row)
    
    return results

def create_summary_visualization():
    """创建综合总结可视化"""
    results = load_results()
    
    # 创建大图
    fig = plt.figure(figsize=(20, 16))
    
    # 创建网格布局
    gs = fig.add_gridspec(4, 4, hspace=0.3, wspace=0.3)
    
    # 1. 峰值时间分布 (左上)
    ax1 = fig.add_subplot(gs[0, 0])
    peak_times = [r['peak_time_ms'] for r in results]
    electrodes = [r['electrode'] for r in results]
    window_ids = [r['window_id'] for r in results]
    
    # 按电极分组
    electrode_names = sorted(list(set(electrodes)))
    colors = plt.cm.tab10(np.linspace(0, 1, len(electrode_names)))
    
    for i, electrode in enumerate(electrode_names):
        electrode_times = [r['peak_time_ms'] for r in results if r['electrode'] == electrode]
        electrode_windows = [r['window_id'] for r in results if r['electrode'] == electrode]
        
        for j, (time, window) in enumerate(zip(electrode_times, electrode_windows)):
            marker = 'o' if window == 1 else 's'
            ax1.scatter(time, i, color=colors[i], s=100, marker=marker, alpha=0.7)
    
    ax1.set_xlabel('峰值时间 (ms)')
    ax1.set_ylabel('电极')
    ax1.set_yticks(range(len(electrode_names)))
    ax1.set_yticklabels(electrode_names)
    ax1.set_title('各电极峰值时间分布')
    ax1.grid(True, alpha=0.3)
    ax1.legend(['窗口1', '窗口2'], loc='upper right')
    
    # 2. 峰值差值分布 (右上)
    ax2 = fig.add_subplot(gs[0, 1])
    peak_values = [r['peak_value_uv'] for r in results]
    
    for i, electrode in enumerate(electrode_names):
        electrode_values = [r['peak_value_uv'] for r in results if r['electrode'] == electrode]
        electrode_windows = [r['window_id'] for r in results if r['electrode'] == electrode]
        
        for j, (value, window) in enumerate(zip(electrode_values, electrode_windows)):
            marker = 'o' if window == 1 else 's'
            ax2.scatter(value, i, color=colors[i], s=100, marker=marker, alpha=0.7)
    
    ax2.set_xlabel('峰值差值 (μV)')
    ax2.set_ylabel('电极')
    ax2.set_yticks(range(len(electrode_names)))
    ax2.set_yticklabels(electrode_names)
    ax2.set_title('各电极峰值差值分布')
    ax2.grid(True, alpha=0.3)
    
    # 3. 时间窗口长度分布 (左中)
    ax3 = fig.add_subplot(gs[0, 2])
    window_lengths = [r['window_length_ms'] for r in results]
    ax3.hist(window_lengths, bins=10, alpha=0.7, color='steelblue', edgecolor='black')
    ax3.set_xlabel('窗口长度 (ms)')
    ax3.set_ylabel('频次')
    ax3.set_title('时间窗口长度分布')
    ax3.grid(True, alpha=0.3)
    
    # 4. 最佳窗口推荐 (右中)
    ax4 = fig.add_subplot(gs[0, 3])
    
    # 找到差值最大的窗口
    max_idx = np.argmax(peak_values)
    best_result = results[max_idx]
    
    # 创建推荐窗口的可视化
    ax4.text(0.1, 0.9, '推荐最佳时间窗口', fontsize=14, fontweight='bold', transform=ax4.transAxes)
    ax4.text(0.1, 0.8, f'电极: {best_result["electrode"]}', fontsize=12, transform=ax4.transAxes)
    ax4.text(0.1, 0.7, f'时间: {best_result["window_start_ms"]:.1f}-{best_result["window_end_ms"]:.1f}ms', 
             fontsize=12, transform=ax4.transAxes)
    ax4.text(0.1, 0.6, f'长度: {best_result["window_length_ms"]:.1f}ms', fontsize=12, transform=ax4.transAxes)
    ax4.text(0.1, 0.5, f'峰值差值: {best_result["peak_value_uv"]:.3f}μV', fontsize=12, transform=ax4.transAxes)
    ax4.text(0.1, 0.4, f'Rest1: {best_result["rest1_mean_uv"]:.3f}μV', fontsize=12, transform=ax4.transAxes)
    ax4.text(0.1, 0.3, f'Rest3: {best_result["rest3_mean_uv"]:.3f}μV', fontsize=12, transform=ax4.transAxes)
    
    # 添加框架
    ax4.add_patch(plt.Rectangle((0.05, 0.25), 0.9, 0.7, fill=False, edgecolor='red', linewidth=2, transform=ax4.transAxes))
    ax4.set_xlim(0, 1)
    ax4.set_ylim(0, 1)
    ax4.axis('off')
    
    # 5. 时间轴上的窗口分布 (下方大图)
    ax5 = fig.add_subplot(gs[1:3, :])
    
    # 绘制时间轴
    time_range = np.arange(200, 501, 10)
    ax5.plot(time_range, np.zeros_like(time_range), 'k-', alpha=0.3, linewidth=1)
    
    # 绘制每个窗口
    y_positions = {}
    y_offset = 0
    
    for i, result in enumerate(results):
        electrode = result['electrode']
        if electrode not in y_positions:
            y_positions[electrode] = y_offset
            y_offset += 1
        
        y_pos = y_positions[electrode]
        window_start = result['window_start_ms']
        window_end = result['window_end_ms']
        peak_time = result['peak_time_ms']
        peak_value = result['peak_value_uv']
        window_id = result['window_id']
        
        # 窗口区间
        color_idx = electrode_names.index(electrode)
        alpha = 0.8 if window_id == 1 else 0.5
        ax5.plot([window_start, window_end], [y_pos, y_pos], 
                color=colors[color_idx], linewidth=8, alpha=alpha)
        
        # 峰值点
        marker = 'o' if window_id == 1 else 's'
        ax5.scatter(peak_time, y_pos, color=colors[color_idx], s=100, 
                   marker=marker, zorder=5, edgecolor='black', linewidth=1)
        
        # 添加数值标注
        ax5.text(peak_time, y_pos + 0.15, f'{peak_value:.2f}', 
                ha='center', va='bottom', fontsize=8, fontweight='bold')
    
    # 设置y轴标签
    ax5.set_yticks(list(y_positions.values()))
    ax5.set_yticklabels(list(y_positions.keys()))
    ax5.set_xlabel('时间 (ms, R波后)')
    ax5.set_ylabel('电极')
    ax5.set_title('所有电极的最佳时间窗口分布 (数值为峰值差值μV)')
    ax5.grid(True, alpha=0.3)
    ax5.set_xlim(200, 500)
    ax5.set_ylim(-0.5, len(electrode_names) - 0.5)
    
    # 添加图例
    legend_elements = []
    for i, electrode in enumerate(electrode_names):
        legend_elements.append(plt.Line2D([0], [0], color=colors[i], lw=4, label=electrode))
    
    ax5.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1.02, 1), ncol=1)
    
    # 添加窗口类型图例
    window_legend = [
        plt.Line2D([0], [0], marker='o', color='gray', linestyle='None', markersize=8, label='窗口1'),
        plt.Line2D([0], [0], marker='s', color='gray', linestyle='None', markersize=8, label='窗口2')
    ]
    ax5.legend(handles=window_legend, loc='upper right', bbox_to_anchor=(1.02, 0.3))
    
    # 6. 新增：统计特征分布对比（rest1_vs_test2）
    ax6 = fig.add_subplot(gs[3, :])
    feature_csv = os.path.join(
        'result', 'hep_analysis', 'prefrontal_region_analysis',
        'literature_features_detailed_rest1_vs_test2.csv')
    if os.path.exists(feature_csv):
        df = pd.read_csv(feature_csv)
        # 只保留rest2_value（即test2）
        feature_names = df['feature_name'].unique()
        regions = df['region'].unique()
        # 只展示均值，按区域分组
        width = 0.8 / len(feature_names)
        x = np.arange(len(regions))
        for i, feat in enumerate(feature_names):
            vals = [df[(df['region']==reg)&(df['feature_name']==feat)]['rest2_value'].mean() for reg in regions]
            ax6.bar(x+i*width, vals, width=width, label=feat)
        ax6.set_xticks(x + width*len(feature_names)/2)
        ax6.set_xticklabels(regions, rotation=30, fontsize=10)
        ax6.set_ylabel('特征均值 (test2)', fontsize=12)
        ax6.set_title('rest1_vs_test2各区域所有HEP特征分布对比', fontsize=14, fontweight='bold')
        ax6.legend(fontsize=8, ncol=4, bbox_to_anchor=(1.0, 1.0))
        ax6.grid(True, alpha=0.2)
    else:
        ax6.text(0.5, 0.5, '未找到特征统计csv文件', ha='center', va='center', fontsize=14, color='red')
        ax6.axis('off')
    
    # 添加总标题
    fig.suptitle('HEP最佳时间窗口分析总结\n(基于Rest1 vs Rest3前额叶电极差值分析)', 
                fontsize=16, fontweight='bold', y=0.98)
    
    # 保存图片
    save_path = os.path.join(RESULT_DIR, 'optimal_windows_summary.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.savefig(save_path.replace('.png', '.svg'), bbox_inches='tight')
    print(f"综合总结图已保存: {save_path}")
    plt.close()

def create_statistics_table():
    """创建统计表格可视化"""
    results = load_results()
    
    # 计算统计数据
    peak_times = [r['peak_time_ms'] for r in results]
    peak_values = [r['peak_value_uv'] for r in results]
    window_lengths = [r['window_length_ms'] for r in results]
    
    # 按电极分组统计
    electrodes = sorted(list(set(r['electrode'] for r in results)))
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 创建表格数据
    table_data = []
    table_data.append(['指标', '平均值', '标准差', '最小值', '最大值', '范围'])
    table_data.append(['峰值时间 (ms)', f'{np.mean(peak_times):.1f}', f'{np.std(peak_times):.1f}', 
                      f'{np.min(peak_times):.1f}', f'{np.max(peak_times):.1f}', 
                      f'{np.max(peak_times)-np.min(peak_times):.1f}'])
    table_data.append(['峰值差值 (μV)', f'{np.mean(peak_values):.3f}', f'{np.std(peak_values):.3f}', 
                      f'{np.min(peak_values):.3f}', f'{np.max(peak_values):.3f}', 
                      f'{np.max(peak_values)-np.min(peak_values):.3f}'])
    table_data.append(['窗口长度 (ms)', f'{np.mean(window_lengths):.1f}', f'{np.std(window_lengths):.1f}', 
                      f'{np.min(window_lengths):.1f}', f'{np.max(window_lengths):.1f}', 
                      f'{np.max(window_lengths)-np.min(window_lengths):.1f}'])
    
    # 绘制表格
    table = ax.table(cellText=table_data[1:], colLabels=table_data[0], 
                    cellLoc='center', loc='center', bbox=[0, 0.5, 1, 0.4])
    table.auto_set_font_size(False)
    table.set_fontsize(12)
    table.scale(1, 2)
    
    # 设置表格样式
    for i in range(len(table_data[0])):
        table[(0, i)].set_facecolor('#4CAF50')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    # 添加各电极详细信息
    electrode_data = []
    electrode_data.append(['电极', '窗口1时间(ms)', '窗口1差值(μV)', '窗口2时间(ms)', '窗口2差值(μV)'])
    
    for electrode in electrodes:
        electrode_results = [r for r in results if r['electrode'] == electrode]
        window1 = [r for r in electrode_results if r['window_id'] == 1][0]
        window2 = [r for r in electrode_results if r['window_id'] == 2][0]
        
        electrode_data.append([
            electrode,
            f'{window1["peak_time_ms"]:.0f}',
            f'{window1["peak_value_uv"]:.3f}',
            f'{window2["peak_time_ms"]:.0f}',
            f'{window2["peak_value_uv"]:.3f}'
        ])
    
    # 绘制电极表格
    electrode_table = ax.table(cellText=electrode_data[1:], colLabels=electrode_data[0], 
                              cellLoc='center', loc='center', bbox=[0, 0, 1, 0.45])
    electrode_table.auto_set_font_size(False)
    electrode_table.set_fontsize(10)
    electrode_table.scale(1, 1.5)
    
    # 设置电极表格样式
    for i in range(len(electrode_data[0])):
        electrode_table[(0, i)].set_facecolor('#2196F3')
        electrode_table[(0, i)].set_text_props(weight='bold', color='white')
    
    ax.set_title('HEP最佳时间窗口统计表格', fontsize=16, fontweight='bold', pad=20)
    ax.axis('off')
    
    # 保存图片
    save_path = os.path.join(RESULT_DIR, 'optimal_windows_statistics.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.savefig(save_path.replace('.png', '.svg'), bbox_inches='tight')
    print(f"统计表格已保存: {save_path}")
    plt.close()

def main():
    """主函数"""
    print("="*50)
    print("HEP最佳时间窗口分析总结")
    print("="*50)
    
    print("1. 创建综合总结可视化...")
    create_summary_visualization()
    
    print("2. 创建统计表格...")
    create_statistics_table()
    
    print("\n总结可视化完成！")
    print(f"结果保存在: {RESULT_DIR}")

if __name__ == "__main__":
    main()
