import os
import mne
import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
from mpl_toolkits.axes_grid1.inset_locator import inset_axes, mark_inset
from matplotlib import gridspec
from scipy.signal import savgol_filter
from scipy.stats import ttest_rel
import glob

# 设置中文字体
font_manager.fontManager.addfont(r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf')
plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10

# 前额叶电极标准列表
PREFRONTAL_ELECTRODES = ['Fp1', 'Fp2', 'Fpz', 'AF3', 'AF4', 'AF7', 'AF8']

# 新增：左、右额中央和顶叶区电极
LATERAL_ELECTRODES = ['FC3', 'FC4', 'P3', 'P4']
ANALYSIS_ELECTRODES = PREFRONTAL_ELECTRODES + LATERAL_ELECTRODES

# HEP分析时间窗（R波后250~400ms）
HEP_TMIN = 0.25  # 单位：秒
HEP_TMAX = 0.4   # 单位：秒

# 数据路径
REST1_PATH = r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/13_raw_epochs/rest1_raw_epochs_20250523_204957.h5'
REST2_PATH = r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/13_raw_epochs/rest2_raw_epochs_20250523_205148.h5'

# 结果保存路径
RESULT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\14_rest_vs_test_analysis"
os.makedirs(RESULT_DIR, exist_ok=True)

# 读取h5文件，提取指定电极的HEP波形（伪代码，后续补充具体实现）
def load_hep_epochs(h5_path, electrodes):
    with h5py.File(h5_path, 'r') as f:
        data = f['data'][:]  # (n_epochs, n_channels, n_times)
        ch_names = [ch.decode('utf-8').strip() if isinstance(ch, bytes) else str(ch).strip() for ch in f['ch_names'][:]]
        print(f'文件 {h5_path} 的实际通道名列表:')
        print(ch_names)
        # 标准63脑电通道列表
        eeg_63 = [
            'Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2',
            'F7', 'F8', 'T7', 'T8', 'P7', 'P8', 'Fz', 'Cz', 'Pz',
            'FC1', 'FC2', 'CP1', 'CP2', 'FC5', 'FC6', 'CP5', 'CP6',
            'FT9', 'FT10', 'TP9', 'TP10', 'F1', 'F2', 'C1', 'C2', 'P1', 'P2',
            'AF3', 'AF4', 'FC3', 'FC4', 'CP3', 'CP4', 'PO3', 'PO4',
            'F5', 'F6', 'C5', 'C6', 'P5', 'P6', 'AF7', 'AF8', 'FT7', 'FT8',
            'TP7', 'TP8', 'PO7', 'PO8', 'Fpz', 'CPz', 'POz', 'Oz'
        ]
        eeg_channels = [ch for ch in ch_names if ch in eeg_63]
        print(f'实际脑电通道数: {len(eeg_channels)}')
        print('脑电通道:', eeg_channels)
        times = f['times'][:]
        if 'subject_ids' in f:
            subject_ids = f['subject_ids'][:]
            subject_ids = [s.decode('utf-8') if isinstance(s, bytes) else str(s) for s in subject_ids]
        else:
            subject_ids = ['all'] * data.shape[0]
    # 只保留前额叶区域实际存在的电极
    valid_electrodes = [e for e in electrodes if e in ch_names]
    ch_indices = [ch_names.index(e) for e in valid_electrodes]
    # 按被试分组
    subj_dict = {}
    for subj in set(subject_ids):
        idx = [i for i, s in enumerate(subject_ids) if s == subj]
        if not idx:
            continue
        avg_dict = {}
        for e, ch_idx in zip(valid_electrodes, ch_indices):
            avg_dict[e] = data[idx, ch_idx, :].mean(axis=0)
        subj_dict[subj] = avg_dict
    # 新增：打印每个被试实际包含的电极名
    print(f'文件 {h5_path} 每个被试实际包含的电极:')
    for subj in subj_dict:
        print(f'  被试{subj}: {list(subj_dict[subj].keys())}')
    return subj_dict, times

# 读取数据
subj_rest1, times1 = load_hep_epochs(REST1_PATH, ANALYSIS_ELECTRODES)
subj_rest2, times2 = load_hep_epochs(REST2_PATH, ANALYSIS_ELECTRODES)

# 取分析时间窗索引
hep_mask1 = (times1 >= HEP_TMIN) & (times1 <= HEP_TMAX)
hep_mask2 = (times2 >= HEP_TMIN) & (times2 <= HEP_TMAX)

# 只分析前额叶所有电极
all_subjects = set(subj_rest1.keys()) & set(subj_rest2.keys())

for elec in ANALYSIS_ELECTRODES:
    # 检查所有被试在两个阶段都包含该电极
    valid_subjects = [subj for subj in all_subjects if elec in subj_rest1[subj] and elec in subj_rest2[subj]]
    if len(valid_subjects) != len(all_subjects):
        missing_subjects = [subj for subj in all_subjects if elec not in subj_rest1[subj] or elec not in subj_rest2[subj]]
        print(f'电极{elec}有被试缺失数据，跳过分析。缺失该电极的被试ID如下：')
        for subj in missing_subjects:
            if elec not in subj_rest1[subj]:
                print(f'  被试{subj} 在rest1阶段缺失{elec}')
            if elec not in subj_rest2[subj]:
                print(f'  被试{subj} 在rest2阶段缺失{elec}')
        continue
    y1_list = [subj_rest1[subj][elec] for subj in valid_subjects]
    y2_list = [subj_rest2[subj][elec] for subj in valid_subjects]
    y1_mean = np.mean(y1_list, axis=0)
    y2_mean = np.mean(y2_list, axis=0)
    fig = plt.figure(figsize=(18, 10))
    gs = gridspec.GridSpec(2, 3, height_ratios=[2, 2.2], width_ratios=[1, 1, 1], hspace=0.25, wspace=0.3)
    # 主图跨2格
    ax_main = fig.add_subplot(gs[0, 0:2])
    x = times1 * 1000  # ms
    x2 = times2 * 1000
    ax_main.plot(x, y1_mean, label='刺激前静息态', color='#1f77b4', alpha=0.8)
    ax_main.plot(x2, y2_mean, label='刺激后静息态', color='#d62728', alpha=0.8)
    # 计算差值曲线（后-前，取绝对值）
    diff_curve = np.abs(y2_mean - y1_mean)
    ax_main.plot(x, diff_curve, label='|后-前| 绝对差值', color='#2ca02c', alpha=0.8, linestyle='-')
    # 找全时程极值点
    peak_idx = np.argmax(diff_curve)
    peak_time = x[peak_idx]
    peak_value = diff_curve[peak_idx]
    # 标注极值点
    ax_main.axvline(peak_time, color='#2ca02c', linestyle=':', linewidth=1.5, alpha=0.8)
    ax_main.scatter([peak_time], [peak_value], color='#2ca02c', s=60, zorder=5)
    ax_main.text(peak_time, peak_value, f'{peak_time:.0f}ms\n{peak_value:.3f}μV', color='#2ca02c', fontsize=10, ha='left', va='bottom', fontweight='bold')
    # 计算幅值绝对值≥0.5μV的区间
    thresh = 0.5
    above_idx = np.where(diff_curve >= thresh)[0]
    if len(above_idx) > 0:
        start_time = x[above_idx[0]]
        end_time = x[above_idx[-1]]
        duration = end_time - start_time
        # 在图上用横线标注该区间
        ax_main.hlines(y=thresh, xmin=start_time, xmax=end_time, color='#9467bd', linewidth=2, linestyle='-', alpha=0.8, label='|绝对差值|≥0.5μV区间')
        ax_main.text((start_time+end_time)/2, thresh+0.05, f'{start_time:.0f}~{end_time:.0f}ms\n跨度{duration:.0f}ms', color='#9467bd', fontsize=10, ha='center', va='bottom', fontweight='bold')
        print(f'{elec}电极：绝对差值曲线≥0.5μV的区间：{start_time:.1f}ms ~ {end_time:.1f}ms，跨度：{duration:.1f}ms')
    else:
        print(f'{elec}电极：绝对差值曲线未达到0.5μV')
    ax_main.set_xlim(-300, 1000)
    ax_main.set_xlabel('时间 (ms)')
    ax_main.set_ylabel('HEP电位 (μV)')
    ax_main.set_title(f'{elec}电极 HEP波形对比')
    ax_main.legend()
    ax_main.axvline(250, color='gray', linestyle='--', linewidth=1.2, alpha=0.7)
    ax_main.axvline(400, color='gray', linestyle='--', linewidth=1.2, alpha=0.7)
    plt.tight_layout()
    save_path = os.path.join(RESULT_DIR, f'{elec}_2行3列复合统计.png')
    print(f'准备保存图片到: {save_path}')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f'图片已保存: {save_path}')
    plt.close()

def check_all_h5_integrity(folder):
    h5_files = sorted(glob.glob(os.path.join(folder, '*.h5')))
    print(f'共检测到{len(h5_files)}个h5文件：')
    for h5_path in h5_files:
        print(f'\n==== 检查文件: {h5_path} ====')
        # 只检查通道和被试-电极结构，不做分析
        _ = load_hep_epochs(h5_path, eeg_63)  # eeg_63为标准63脑电通道

# 标准63脑电通道列表（供批量检查用）
eeg_63 = [
    'Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2',
    'F7', 'F8', 'T7', 'T8', 'P7', 'P8', 'Fz', 'Cz', 'Pz',
    'FC1', 'FC2', 'CP1', 'CP2', 'FC5', 'FC6', 'CP5', 'CP6',
    'FT9', 'FT10', 'TP9', 'TP10', 'F1', 'F2', 'C1', 'C2', 'P1', 'P2',
    'AF3', 'AF4', 'FC3', 'FC4', 'CP3', 'CP4', 'PO3', 'PO4',
    'F5', 'F6', 'C5', 'C6', 'P5', 'P6', 'AF7', 'AF8', 'FT7', 'FT8',
    'TP7', 'TP8', 'PO7', 'PO8', 'Fpz', 'CPz', 'POz', 'Oz'
]

# 批量检查所有h5文件完整性
check_all_h5_integrity('result/hep_analysis/13_raw_epochs') 