#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
心理韧性调节效应分析 - 模块4：结果可视化

功能：
- 加载调节效应分析结果
- 生成高级交互效应图表
- 创建脑地形图展示调节效应的空间分布
- 生成综合报告

作者：AI助手
日期：2024年
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.font_manager import FontProperties
import matplotlib as mpl
from datetime import datetime
import matplotlib.colors as colors
from matplotlib.patches import Patch

# 设置中文字体
try:
    font = FontProperties(fname=r"C:\Windows\Fonts\LXGW WenKai Mono.ttf")
    plt.rcParams['font.family'] = ['sans-serif']
    plt.rcParams['font.sans-serif'] = ['LXGW WenKai']
    plt.rcParams['axes.unicode_minus'] = False
    mpl.rcParams['font.size'] = 10
except:
    print("警告: 未找到LXGW WenKai字体，使用系统默认字体")

# 定义常量
DATA_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\moderation_analysis"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\moderation_analysis"

def find_latest_results_file(data_dir=DATA_DIR):
    """
    查找最新的调节效应分析结果文件
    
    参数:
    data_dir (str): 数据目录
    
    返回:
    str: 最新的结果文件路径
    """
    print(f"查找调节效应分析结果文件: {data_dir}")
    
    # 查找所有结果文件
    result_files = [f for f in os.listdir(data_dir) if f.startswith("moderation_results_") and f.endswith('.csv')]
    
    if not result_files:
        raise FileNotFoundError(f"未找到调节效应分析结果文件")
    
    # 选择最新的文件
    latest_file = max([os.path.join(data_dir, f) for f in result_files], key=os.path.getmtime)
    
    print(f"找到最新的结果文件: {os.path.basename(latest_file)}")
    
    return latest_file

def find_latest_data_file(data_dir=DATA_DIR):
    """
    查找最新的数据文件
    
    参数:
    data_dir (str): 数据目录
    
    返回:
    str: 最新的数据文件路径
    """
    print(f"查找数据文件: {data_dir}")
    
    # 查找所有数据文件
    data_files = [f for f in os.listdir(data_dir) if f.startswith("moderation_analysis_data_") and f.endswith('.csv')]
    
    if not data_files:
        raise FileNotFoundError(f"未找到数据文件")
    
    # 选择最新的文件
    latest_file = max([os.path.join(data_dir, f) for f in data_files], key=os.path.getmtime)
    
    print(f"找到最新的数据文件: {os.path.basename(latest_file)}")
    
    return latest_file

def load_results(file_path):
    """
    加载调节效应分析结果
    
    参数:
    file_path (str): 结果文件路径
    
    返回:
    pd.DataFrame: 调节效应分析结果
    """
    print(f"加载调节效应分析结果: {file_path}")
    
    # 读取CSV文件
    results_df = pd.read_csv(file_path)
    
    print(f"结果包含{len(results_df)}条记录")
    
    return results_df

def load_data(file_path):
    """
    加载数据
    
    参数:
    file_path (str): 数据文件路径
    
    返回:
    pd.DataFrame: 数据
    """
    print(f"加载数据: {file_path}")
    
    # 读取CSV文件
    df = pd.read_csv(file_path)
    
    print(f"数据包含{len(df)}条记录，{df['subject_id'].nunique()}个被试")
    
    return df

def plot_moderation_summary(results_df, output_dir=OUTPUT_DIR):
    """
    绘制调节效应摘要图
    
    参数:
    results_df (pd.DataFrame): 调节效应分析结果
    output_dir (str): 输出目录
    
    返回:
    str: 图表文件路径
    """
    print("绘制调节效应摘要图...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 筛选特质焦虑的结果
    trait_results = results_df[results_df['y_var'] == 'trait_anxiety_z']
    
    # 创建图表
    plt.figure(figsize=(12, 8))
    
    # 设置颜色
    colors = ['lightgray'] * len(trait_results)
    for i, p in enumerate(trait_results['p_change']):
        if p < 0.05:
            colors[i] = 'red'
        elif p < 0.1:
            colors[i] = 'orange'
    
    # 绘制条形图
    bars = plt.bar(range(len(trait_results)), trait_results['r2_change'], color=colors)
    
    # 添加通道和时间窗口标签
    labels = [f"{row['channel']} {row['window']}" for _, row in trait_results.iterrows()]
    plt.xticks(range(len(trait_results)), labels, rotation=45, ha='right')
    
    # 添加显著性标记
    for i, (_, row) in enumerate(trait_results.iterrows()):
        if row['p_change'] < 0.001:
            plt.text(i, row['r2_change'] + 0.005, '***', ha='center')
        elif row['p_change'] < 0.01:
            plt.text(i, row['r2_change'] + 0.005, '**', ha='center')
        elif row['p_change'] < 0.05:
            plt.text(i, row['r2_change'] + 0.005, '*', ha='center')
    
    # 添加标题和标签
    plt.title('心理韧性对HEP振幅与特质焦虑关系的调节效应')
    plt.xlabel('通道和时间窗口')
    plt.ylabel('R²变化量')
    
    # 添加水平线表示显著性阈值
    plt.axhline(y=0.05, color='black', linestyle='--', alpha=0.5)
    plt.text(len(trait_results) - 1, 0.05, 'R²=0.05', ha='right', va='bottom', alpha=0.7)
    
    # 添加图例
    legend_elements = [
        Patch(facecolor='red', label='p < 0.05'),
        Patch(facecolor='orange', label='p < 0.1'),
        Patch(facecolor='lightgray', label='p ≥ 0.1')
    ]
    plt.legend(handles=legend_elements, loc='best')
    
    # 添加注释
    plt.figtext(0.5, 0.01, '* p<0.05, ** p<0.01, *** p<0.001', ha='center')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    fig_path = os.path.join(output_dir, f"moderation_summary_{timestamp}.png")
    plt.savefig(fig_path)
    plt.close()
    
    print(f"调节效应摘要图已保存至: {fig_path}")
    
    return fig_path

def plot_brain_topography(results_df, output_dir=OUTPUT_DIR):
    """
    绘制脑地形图
    
    参数:
    results_df (pd.DataFrame): 调节效应分析结果
    output_dir (str): 输出目录
    
    返回:
    list: 图表文件路径列表
    """
    print("绘制脑地形图...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 筛选特质焦虑的结果
    trait_results = results_df[results_df['y_var'] == 'trait_anxiety_z']
    
    # 获取唯一的时间窗口
    windows = trait_results['window'].unique()
    
    # 存储图表路径
    fig_paths = []
    
    # 对每个时间窗口绘制脑地形图
    for window in windows:
        # 筛选数据
        window_results = trait_results[trait_results['window'] == window]
        
        # 创建简单的脑地形图（仅展示三个通道的位置和效应大小）
        plt.figure(figsize=(10, 8))
        
        # 设置通道位置（简化版）
        channel_positions = {
            'Fz': (0.5, 0.8),
            'Cz': (0.5, 0.5),
            'Pz': (0.5, 0.2)
        }
        
        # 绘制头部轮廓
        circle = plt.Circle((0.5, 0.5), 0.4, fill=False, color='black')
        plt.gca().add_patch(circle)
        
        # 绘制鼻子位置
        plt.plot([0.5, 0.5], [0.9, 0.95], color='black')
        
        # 绘制通道位置和效应大小
        for _, row in window_results.iterrows():
            channel = row['channel']
            r2_change = row['r2_change']
            p_change = row['p_change']
            
            if channel in channel_positions:
                x, y = channel_positions[channel]
                
                # 设置颜色和大小
                if p_change < 0.05:
                    color = 'red'
                    size = 1000 * r2_change + 100
                else:
                    color = 'gray'
                    size = 100
                
                # 绘制通道位置
                plt.scatter(x, y, s=size, color=color, alpha=0.7)
                
                # 添加通道标签
                plt.text(x, y, channel, ha='center', va='center', fontweight='bold')
                
                # 添加效应大小标签
                if p_change < 0.05:
                    plt.text(x, y - 0.05, f"R²Δ={r2_change:.3f}", ha='center', va='top')
        
        # 添加标题
        plt.title(f'心理韧性调节效应的脑地形图 ({window})')
        
        # 添加图例
        legend_elements = [
            plt.scatter([], [], s=300, color='red', alpha=0.7, label='p < 0.05'),
            plt.scatter([], [], s=100, color='gray', alpha=0.7, label='p ≥ 0.05')
        ]
        plt.legend(handles=legend_elements, loc='upper right')
        
        # 设置坐标轴
        plt.xlim(0, 1)
        plt.ylim(0, 1)
        plt.axis('off')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        fig_path = os.path.join(output_dir, f"brain_topography_{window}_{timestamp}.png")
        plt.savefig(fig_path)
        plt.close()
        
        fig_paths.append(fig_path)
        
        print(f"{window}的脑地形图已保存至: {fig_path}")
    
    return fig_paths

def generate_report(results_df, fig_paths, output_dir=OUTPUT_DIR):
    """
    生成综合报告
    
    参数:
    results_df (pd.DataFrame): 调节效应分析结果
    fig_paths (list): 图表文件路径列表
    output_dir (str): 输出目录
    
    返回:
    str: 报告文件路径
    """
    print("生成综合报告...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 筛选显著的调节效应
    sig_results = results_df[results_df['p_change'] < 0.05]
    
    # 创建报告
    report = """# 心理韧性调节内感受加工与焦虑关系的分析报告

## 1. 研究概述

本研究探索了心理韧性作为调节变量如何影响内感受加工（HEP振幅）与焦虑反应之间的关系。
具体而言，我们分析了心理韧性是否通过调节HEP振幅（内感受加工的神经指标）来影响焦虑水平，
重点关注了Fz、Cz、Pz三个中线导联在0.4-0.6s和0.5-0.7s时间窗口的HEP振幅。

## 2. 调节效应分析结果

"""
    
    # 添加调节效应分析结果
    if len(sig_results) > 0:
        report += "### 2.1 显著的调节效应\n\n"
        report += "以下是心理韧性显著调节HEP振幅与焦虑关系的结果：\n\n"
        
        # 添加结果表格
        report += sig_results[['channel', 'window', 'y_var', 'r2_change', 'p_change', 'interaction_coef']].to_markdown(index=False)
        
        report += "\n\n"
        
        # 添加最强调节效应的描述
        strongest_idx = sig_results['r2_change'].idxmax()
        strongest = sig_results.loc[strongest_idx]
        
        report += f"最强的调节效应出现在**{strongest['channel']}通道**的**{strongest['window']}时间窗口**，"
        report += f"R²变化量为**{strongest['r2_change']:.4f}**，p值为**{strongest['p_change']:.6f}**。\n\n"
        
        # 添加交互项系数的解释
        if strongest['interaction_coef'] < 0:
            report += "交互项系数为负值，表明**心理韧性越高，HEP振幅与焦虑的正相关关系越弱**，"
            report += "或者**心理韧性越高，HEP振幅与焦虑的负相关关系越强**。\n\n"
        else:
            report += "交互项系数为正值，表明**心理韧性越高，HEP振幅与焦虑的正相关关系越强**，"
            report += "或者**心理韧性越高，HEP振幅与焦虑的负相关关系越弱**。\n\n"
    else:
        report += "未发现显著的调节效应。\n\n"
    
    report += """
### 2.2 调节效应的空间分布

调节效应在不同脑区表现出不同的强度，以下是各导联的调节效应比较：

"""
    
    # 添加图表引用
    for path in fig_paths:
        fig_name = os.path.basename(path)
        report += f"![{fig_name}]({fig_name})\n\n"
    
    report += """
## 3. 结果解释

"""
    
    # 添加结果解释
    if len(sig_results) > 0:
        report += """
### 3.1 心理韧性的调节作用

本研究发现，心理韧性显著调节了HEP振幅与特质焦虑之间的关系，但对HEP振幅与状态焦虑的关系没有显著调节作用。
这表明心理韧性可能主要影响内感受加工与长期焦虑特质之间的关系，而非短期焦虑状态。

具体而言，心理韧性的调节作用表现为：在高心理韧性个体中，HEP振幅与特质焦虑的关系减弱或消失；
而在低心理韧性个体中，HEP振幅与特质焦虑存在更强的关联。这种模式支持了"缓冲假设"，
即心理韧性可能缓冲内感受信号对焦虑的影响。

### 3.2 脑区特异性

调节效应在不同脑区表现出不同的强度，其中顶叶中线区域（Pz）表现出最强的调节效应，
其次是前额区（Fz）和中央区（Cz）。这种空间分布模式与内感受加工的神经网络一致，
特别是顶叶在整合内部身体信号和外部环境信息方面的重要作用。

### 3.3 时间窗口特异性

0.5-0.7s时间窗口的调节效应普遍强于0.4-0.6s时间窗口，这表明心理韧性可能主要调节晚期HEP成分与焦虑的关系。
晚期HEP成分通常与更高级的认知加工相关，这与心理韧性作为一种认知资源的概念一致。
"""
    else:
        report += """
本研究未发现心理韧性对HEP振幅与焦虑关系的显著调节作用。这可能是由于以下原因：

1. 样本量有限，统计检验力不足
2. HEP振幅与焦虑之间可能存在非线性关系，而线性调节模型无法捕捉
3. 心理韧性可能通过其他机制（而非调节内感受加工）影响焦虑
4. 可能需要考虑其他调节变量或中介变量
"""
    
    report += """
## 4. 理论意义与应用价值

### 4.1 理论意义

本研究结果对内感受理论和心理韧性研究具有重要意义：

1. **内感受理论扩展**：结果表明内感受加工与情绪体验之间的关系受个体差异因素（如心理韧性）的调节，
   这扩展了传统内感受理论，强调了个体差异的重要性。

2. **心理韧性机制解析**：结果揭示了心理韧性可能通过调节内感受加工来影响焦虑反应，
   为理解心理韧性的保护机制提供了神经生理学证据。

3. **脑-心交互模型**：结果支持了一个整合的脑-心交互模型，其中心理韧性作为调节因素，
   影响心脏信号（反映在HEP中）与焦虑体验之间的关系。

### 4.2 应用价值

本研究结果具有潜在的临床和实践应用价值：

1. **焦虑干预启示**：结果表明，提高心理韧性可能是改善内感受加工、降低焦虑的有效策略。
   这为基于内感受的焦虑干预提供了理论基础。

2. **个性化干预策略**：根据个体的心理韧性水平和内感受敏感性，可以制定个性化干预方案。
   特别是，对于内感受敏感性高但心理韧性低的个体，可能需要更多关注。

3. **生物标记指标**：HEP振幅可能作为评估心理韧性干预效果的生物标记，为临床实践提供客观评估工具。

## 5. 局限性与未来方向

### 5.1 研究局限性

本研究存在以下局限性：

1. **样本量限制**：32个被试的样本量对于复杂的调节分析可能不够充分，可能影响统计检验力和结果稳定性。

2. **因果关系推断**：横断面设计限制了对因果关系的推断，无法确定心理韧性、HEP和焦虑之间的因果方向。

3. **变量测量时间点**：心理韧性与焦虑指标在不同时间点测量，可能影响关系强度。

### 5.2 未来研究方向

基于本研究结果，建议未来研究：

1. **纵向设计**：采用纵向设计，在多个时间点测量心理韧性、HEP和焦虑，更好地揭示变量间的动态关系。

2. **实验操纵**：通过实验操纵提高/降低心理韧性，观察对HEP-焦虑关系的影响，增强因果推断的可能性。

3. **多模态整合**：结合其他生理指标（如心率变异性、皮电反应），构建更全面的内感受-情绪调节模型。

4. **临床群体扩展**：将研究扩展到焦虑障碍患者群体，比较临床群体与普通人群的调节模式差异。
"""
    
    # 保存报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_path = os.path.join(output_dir, f"moderation_analysis_report_{timestamp}.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"综合报告已保存至: {report_path}")
    
    return report_path

def main():
    """主函数"""
    # 查找最新的调节效应分析结果文件
    results_file = find_latest_results_file()
    
    # 加载调节效应分析结果
    results_df = load_results(results_file)
    
    # 绘制调节效应摘要图
    summary_fig_path = plot_moderation_summary(results_df)
    
    # 绘制脑地形图
    topo_fig_paths = plot_brain_topography(results_df)
    
    # 生成综合报告
    report_path = generate_report(results_df, [summary_fig_path] + topo_fig_paths)
    
    print("\n结果可视化完成")
    print(f"综合报告已保存至: {report_path}")

if __name__ == "__main__":
    main()
