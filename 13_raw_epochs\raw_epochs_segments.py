#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
心跳诱发电位(HEP)数据提取脚本 - 基于双侧乳突参考
将提取的HEP数据保存为.h5格式文件，以便后续分析和可视化

功能：
- 从双侧乳突参考的EEG/ECG数据中提取HEP成分
- 检测ECG中的R峰，并以R峰为中心创建epochs
- 处理所有实验阶段(prac, rest1-3, test1-3)的数据
- 为每个被试单独保存HEP数据，保持与现有全脑平均参考数据相同的结构
- 保存为.h5格式文件，便于后续分析

数据处理流程：
1. 加载双侧乳突参考的预处理数据
2. 提取ECG信号并检测R峰
3. 以R峰为中心创建epochs (-800ms到1000ms)
4. 应用基线校正 (-300ms到0ms)
5. 为每个被试和阶段单独保存HEP数据

用法：
python raw_epochs_segments.py
"""

import os
import mne
import numpy as np
from neurokit2 import ecg_peaks
from tqdm import tqdm
import h5py
import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hep_analysis_tp9tp10.log'),
        logging.StreamHandler()
    ]
)

# 数据路径配置
DATA_DIR = r"D:\ecgeeg\18-eegecg_manual_preprocess5_reref2_tp9tp10"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\13_raw_epochs\tp9tp10"

# 确保输出目录存在
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 通道设置 - 标准64通道EEG电极
EEG_CHANNELS = ['Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2', 'F7', 'F8', 'T7', 'T8', 'P7', 'P8',
                'Fz', 'Cz', 'Pz', 'FC1', 'FC2', 'CP1', 'CP2', 'FC5', 'FC6', 'CP5', 'CP6', 'FT9', 'FT10', 'F1', 'F2',
                'C1', 'C2', 'P1', 'P2', 'AF3', 'AF4', 'FC3', 'FC4', 'CP3', 'CP4', 'PO3', 'PO4', 'F5', 'F6', 'C5',
                'C6', 'P5', 'P6', 'AF7', 'AF8', 'FT7', 'FT8', 'TP7', 'TP8', 'PO7', 'PO8', 'Fpz', 'CPz', 'POz', 'Oz']

# ECG通道设置 - 使用ECG11通道进行R峰检测
ECG_CHANNEL = 'ECG11'

# 实验阶段定义
STAGES = ['prac', 'rest1', 'test1', 'rest2', 'test2', 'rest3', 'test3']

# 被试ID范围 (排除已知的问题被试)
SUBJECT_IDS = [i for i in range(1, 33) if i not in [3, 4, 14]]  # 排除3, 4, 14号被试

# HEP参数设置 - 按照用户要求设置
HEP_TMIN = -0.5  # R峰前500ms
HEP_TMAX = 1.0   # R峰后1000ms
BASELINE_TMIN = -0.2  # 基线校正起始时间
BASELINE_TMAX = 0.0   # 基线校正结束时间

def load_data(file_path):
    """
    加载双侧乳突参考的EEG/ECG数据

    参数:
        file_path (str): 数据文件路径

    返回:
        mne.Raw: 加载的原始数据

    异常:
        FileNotFoundError: 当文件不存在时抛出
        Exception: 当数据加载失败时抛出
    """
    logging.info(f"正在加载数据: {os.path.basename(file_path)}")

    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")

    try:
        raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
        logging.info(f"成功加载数据，采样率: {raw.info['sfreq']} Hz，通道数: {len(raw.ch_names)}")
        return raw
    except Exception as e:
        logging.error(f"加载数据失败: {str(e)}")
        raise

def extract_r_peaks(raw):
    """
    从ECG信号中提取R峰位置

    参数:
        raw (mne.Raw): 包含ECG通道的原始数据

    返回:
        numpy.ndarray: R峰的采样点索引

    异常:
        ValueError: 当未找到ECG通道或未检测到R峰时抛出
    """
    logging.info("正在提取R波...")

    # 检查ECG通道是否存在
    if ECG_CHANNEL not in raw.ch_names:
        available_ecg = [ch for ch in raw.ch_names if ch.startswith('ECG')]
        if available_ecg:
            logging.warning(f"未找到{ECG_CHANNEL}通道，可用ECG通道: {available_ecg}")
            # 尝试使用第一个可用的ECG通道
            ecg_channel = available_ecg[0]
            logging.info(f"使用ECG通道: {ecg_channel}")
        else:
            raise ValueError(f"未找到任何ECG通道")
    else:
        ecg_channel = ECG_CHANNEL

    # 提取ECG数据
    ecg_data = raw.get_data(picks=ecg_channel)[0]
    sampling_rate = raw.info['sfreq']

    # 使用neurokit2检测R波
    try:
        _, rpeaks = ecg_peaks(ecg_data, sampling_rate=sampling_rate)
        rpeaks = rpeaks['ECG_R_Peaks']

        if len(rpeaks) == 0:
            raise ValueError("未检测到R波")

        logging.info(f"检测到 {len(rpeaks)} 个R波")
        return rpeaks

    except Exception as e:
        logging.error(f"R波检测失败: {str(e)}")
        raise ValueError(f"R波检测失败: {str(e)}")

def extract_hep_epochs(raw, rpeaks, stage_name):
    """
    提取心跳诱发电位(HEP) epochs数据

    参数:
        raw (mne.Raw): 原始数据
        rpeaks (numpy.ndarray): R峰的采样点索引
        stage_name (str): 实验阶段名称

    返回:
        tuple: (epochs_data, ch_names, times)
               epochs_data: numpy.ndarray (n_epochs, n_channels, n_times)
               ch_names: list of str
               times: numpy.ndarray

    异常:
        ValueError: 当未找到足够的通道或无法创建epochs时抛出
    """
    logging.info(f"正在提取{stage_name}阶段的HEP epochs...")

    # 获取可用的EEG通道
    available_channels = raw.ch_names
    eeg_channels = [ch for ch in available_channels if ch in EEG_CHANNELS]

    # 检查ECG通道
    if ECG_CHANNEL in available_channels:
        ecg_channel = ECG_CHANNEL
    else:
        ecg_channels = [ch for ch in available_channels if ch.startswith('ECG')]
        if ecg_channels:
            ecg_channel = ecg_channels[0]
            logging.warning(f"使用ECG通道: {ecg_channel}")
        else:
            raise ValueError("未找到ECG通道")

    if not eeg_channels:
        raise ValueError("在数据中未找到EEG通道")

    logging.info(f"使用 {len(eeg_channels)} 个EEG通道和1个ECG通道")

    # 创建事件数组
    events = np.column_stack((rpeaks, np.zeros_like(rpeaks), np.ones_like(rpeaks)))

    # 选择要分析的通道
    picks = eeg_channels + [ecg_channel]

    try:
        # 创建epochs
        epochs = mne.Epochs(
            raw,
            events,
            tmin=HEP_TMIN,
            tmax=HEP_TMAX,
            picks=picks,
            baseline=(BASELINE_TMIN, BASELINE_TMAX),
            preload=True,
            verbose=False,
            reject=None,  # 不进行自动伪迹拒绝
            flat=None
        )

        if len(epochs) == 0:
            raise ValueError("未创建有效的epochs")

        logging.info(f"成功创建 {len(epochs)} 个epochs")

        # 获取epochs数据 (保持原始单位，不转换为mV)
        epochs_data = epochs.get_data()  # (n_epochs, n_channels, n_times)
        ch_names = epochs.ch_names
        times = epochs.times

        logging.info(f"Epochs数据形状: {epochs_data.shape}")
        logging.info(f"时间范围: {times[0]:.3f}s 到 {times[-1]:.3f}s")

        return epochs_data, ch_names, times

    except Exception as e:
        logging.error(f"提取HEP epochs失败: {str(e)}")
        raise ValueError(f"提取HEP epochs失败: {str(e)}")

def get_file_pattern(subject_id_str, stage):
    """
    根据被试ID和实验阶段获取对应的文件名

    参数:
        subject_id_str (str): 被试ID字符串 (如 "01", "02")
        stage (str): 实验阶段 ('prac', 'rest1', 'test1', 'rest2', 'test2', 'rest3', 'test3')

    返回:
        str: 对应的文件名
    """
    stage_patterns = {
        'prac':  f"{subject_id_str}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_prac_TP9TP10Ref.fif",
        'rest1': f"{subject_id_str}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_rest_TP9TP10Ref.fif",
        'test1': f"{subject_id_str}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_test_TP9TP10Ref.fif",
        'rest2': f"{subject_id_str}_02_reto2_combined_baseline_corrected_chanloc_bad_interp_rest_TP9TP10Ref.fif",
        'test2': f"{subject_id_str}_02_reto2_combined_baseline_corrected_chanloc_bad_interp_test_TP9TP10Ref.fif",
        'rest3': f"{subject_id_str}_03_reto2_combined_baseline_corrected_chanloc_bad_interp_rest_TP9TP10Ref.fif",
        'test3': f"{subject_id_str}_03_reto2_combined_baseline_corrected_chanloc_bad_interp_test_TP9TP10Ref.fif"
    }
    return stage_patterns.get(stage)

def save_stage_hep_h5(stage_epochs_data, stage, output_dir):
    """
    保存单个阶段所有被试的HEP数据为HDF5格式，与现有全脑平均参考格式完全一致

    参数:
        stage_epochs_data (list): 包含该阶段所有被试epochs数据的列表
                                 每个元素为 (epochs_data, ch_names, times, subject_id)
        stage (str): 实验阶段
        output_dir (str): 输出目录

    返回:
        str: 保存的文件路径
    """
    if not stage_epochs_data:
        logging.warning(f"阶段 {stage} 没有有效的epochs数据")
        return None

    # 生成时间戳
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

    # 文件名格式: {stage}_raw_epochs_{timestamp}.h5 (与现有格式一致)
    output_file = os.path.join(output_dir, f"{stage}_raw_epochs_{timestamp}.h5")

    try:
        # 收集所有epochs数据
        all_epochs = []
        all_stages = []
        all_subject_ids = []
        ch_names = None
        times = None
        sfreq = None

        for epochs_data, epoch_ch_names, epoch_times, subject_id in stage_epochs_data:
            # 设置通道名称和时间（使用第一个有效数据）
            if ch_names is None:
                ch_names = epoch_ch_names
                times = epoch_times
                sfreq = 500.0  # 固定采样率

            # epochs_data 形状应该是 (n_epochs, n_channels, n_times)
            n_epochs = epochs_data.shape[0]

            # 添加到总列表
            all_epochs.append(epochs_data)
            all_stages.extend([stage] * n_epochs)
            all_subject_ids.extend([f"{subject_id:02d}"] * n_epochs)

        # 合并所有epochs数据
        combined_data = np.concatenate(all_epochs, axis=0)  # (total_epochs, n_channels, n_times)

        logging.info(f"合并 {stage} 阶段数据: {combined_data.shape}")

        with h5py.File(output_file, 'w') as f:
            # 保存数据集 - 与现有格式完全一致
            f.create_dataset('data', data=combined_data, compression='gzip')

            # 将字符串转换为bytes，与现有格式一致
            ch_names_bytes = [ch.encode('utf-8') for ch in ch_names]
            stages_bytes = [stage.encode('utf-8') for stage in all_stages]
            subject_ids_bytes = [sid.encode('utf-8') for sid in all_subject_ids]

            f.create_dataset('ch_names', data=np.array(ch_names_bytes, dtype=object))
            f.create_dataset('times', data=times)
            f.create_dataset('stages', data=np.array(stages_bytes, dtype=object))
            f.create_dataset('subject_ids', data=np.array(subject_ids_bytes, dtype=object))

            # 保存根级别属性 - 与现有格式完全一致
            f.attrs['n_channels'] = np.int32(len(ch_names))
            f.attrs['n_epochs'] = np.int32(combined_data.shape[0])
            f.attrs['n_subjects'] = np.int32(len(SUBJECT_IDS))
            f.attrs['n_times'] = np.int32(len(times))
            f.attrs['sfreq'] = np.float64(sfreq)
            f.attrs['stage'] = stage
            f.attrs['timestamp'] = timestamp
            f.attrs['tmax'] = np.float64(HEP_TMAX)
            f.attrs['tmin'] = np.float64(HEP_TMIN)

        logging.info(f"已保存 {stage} 阶段的HEP数据到: {os.path.basename(output_file)}")
        logging.info(f"  - 总epochs数: {combined_data.shape[0]}")
        logging.info(f"  - 通道数: {combined_data.shape[1]}")
        logging.info(f"  - 时间点数: {combined_data.shape[2]}")
        logging.info(f"  - 被试数: {len(set(all_subject_ids))}")

        return output_file

    except Exception as e:
        logging.error(f"保存 {stage} 阶段HEP数据失败: {str(e)}")
        raise

def analyze_hep():
    """
    分析所有被试的HEP数据 - 双侧乳突参考版本
    与现有全脑平均参考格式完全一致

    处理流程：
    1. 遍历所有被试和实验阶段，收集epochs数据
    2. 按阶段组织数据，每个阶段包含所有被试的epochs
    3. 保存为与现有格式完全一致的.h5文件
    """
    logging.info("=" * 60)
    logging.info("开始双侧乳突参考HEP数据提取...")
    logging.info("=" * 60)
    logging.info(f"数据目录: {DATA_DIR}")
    logging.info(f"输出目录: {OUTPUT_DIR}")
    logging.info(f"处理被试数量: {len(SUBJECT_IDS)}")
    logging.info(f"被试ID: {SUBJECT_IDS}")
    logging.info(f"实验阶段: {STAGES}")
    logging.info(f"HEP时间窗口: {HEP_TMIN}s 到 {HEP_TMAX}s")
    logging.info(f"基线校正窗口: {BASELINE_TMIN}s 到 {BASELINE_TMAX}s")

    # 按阶段组织数据
    stage_data = {stage: [] for stage in STAGES}

    # 统计变量
    total_files = len(SUBJECT_IDS) * len(STAGES)
    processed_files = 0
    failed_files = 0

    # 处理每个被试
    for subject_id in tqdm(SUBJECT_IDS, desc="处理被试"):
        subject_id_str = f"{subject_id:02d}"
        logging.info(f"\n处理被试 {subject_id_str}")

        # 处理每个阶段
        for stage in STAGES:
            try:
                logging.info(f"  处理阶段: {stage}")

                # 获取文件名和路径
                file_pattern = get_file_pattern(subject_id_str, stage)
                if not file_pattern:
                    logging.error(f"  未知阶段: {stage}")
                    failed_files += 1
                    continue

                file_path = os.path.join(DATA_DIR, file_pattern)

                if not os.path.exists(file_path):
                    logging.warning(f"  文件不存在: {os.path.basename(file_path)}")
                    failed_files += 1
                    continue

                # 加载数据
                raw = load_data(file_path)

                # 提取R波
                rpeaks = extract_r_peaks(raw)

                # 提取HEP epochs
                epochs_data, ch_names, times = extract_hep_epochs(raw, rpeaks, stage)

                # 添加到对应阶段的数据列表
                stage_data[stage].append((epochs_data, ch_names, times, subject_id))

                processed_files += 1
                logging.info(f"  ✓ 成功处理被试 {subject_id_str} 的 {stage} 阶段 ({epochs_data.shape[0]} epochs)")

            except Exception as e:
                logging.error(f"  ✗ 处理被试 {subject_id_str} 的 {stage} 阶段时出错: {str(e)}")
                failed_files += 1
                continue

    # 保存每个阶段的数据
    saved_files = []
    for stage in STAGES:
        if stage_data[stage]:
            try:
                output_file = save_stage_hep_h5(stage_data[stage], stage, OUTPUT_DIR)
                if output_file:
                    saved_files.append(output_file)
            except Exception as e:
                logging.error(f"保存 {stage} 阶段数据失败: {str(e)}")
        else:
            logging.warning(f"阶段 {stage} 没有有效数据")

    # 输出处理结果统计
    logging.info("\n" + "=" * 60)
    logging.info("HEP数据提取完成！")
    logging.info("=" * 60)
    logging.info(f"总文件数: {total_files}")
    logging.info(f"成功处理: {processed_files}")
    logging.info(f"处理失败: {failed_files}")
    logging.info(f"成功率: {processed_files/total_files*100:.1f}%")
    logging.info(f"保存阶段文件数: {len(saved_files)}")
    logging.info(f"输出目录: {OUTPUT_DIR}")

    if saved_files:
        logging.info("\n保存的文件列表:")
        for file_path in saved_files:
            logging.info(f"  - {os.path.basename(file_path)}")

    return processed_files, failed_files

def main():
    """
    主函数 - 执行双侧乳突参考HEP数据提取
    """
    try:
        # 检查数据目录是否存在
        if not os.path.exists(DATA_DIR):
            logging.error(f"数据目录不存在: {DATA_DIR}")
            return False

        # 检查输出目录
        if not os.path.exists(OUTPUT_DIR):
            logging.info(f"创建输出目录: {OUTPUT_DIR}")
            os.makedirs(OUTPUT_DIR, exist_ok=True)

        # 执行HEP分析
        processed, failed = analyze_hep()

        # 检查结果
        if processed > 0:
            logging.info(f"\n✓ 成功完成双侧乳突参考HEP数据提取")
            logging.info(f"  处理成功: {processed} 个文件")
            if failed > 0:
                logging.warning(f"  处理失败: {failed} 个文件")
            return True
        else:
            logging.error("✗ 没有成功处理任何文件")
            return False

    except KeyboardInterrupt:
        logging.info("\n用户中断程序执行")
        return False
    except Exception as e:
        logging.error(f"程序执行出错: {str(e)}")
        return False

if __name__ == "__main__":
    # 设置MNE日志级别
    mne.set_log_level('WARNING')

    # 执行主函数
    success = main()

    # 退出程序
    if success:
        logging.info("程序执行完成")
    else:
        logging.error("程序执行失败")
        exit(1)
