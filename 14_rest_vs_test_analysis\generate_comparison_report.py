#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成双侧乳突参考 vs 全脑平均参考的详细对比报告

功能：
1. 加载对比结果数据
2. 生成详细的统计分析
3. 创建可视化图表
4. 生成完整的对比报告
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
import seaborn as sns

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 12
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\14_rest_vs_test_analysis'
COMPARISON_DIR = os.path.join(BASE_DIR, 'comparison')
AVGREF_DIR = BASE_DIR
TP9TP10_DIR = os.path.join(BASE_DIR, 'tp9tp10')

def load_all_data():
    """加载所有对比数据"""
    
    data = {}
    
    # 加载窗口对比结果
    window_comparison_file = os.path.join(COMPARISON_DIR, 'window_comparison.csv')
    if os.path.exists(window_comparison_file):
        data['window_comparison'] = pd.read_csv(window_comparison_file, encoding='utf-8')
        print(f"窗口对比数据: {len(data['window_comparison'])} 行")
    
    # 加载全脑平均参考数据
    avgref_file = os.path.join(AVGREF_DIR, 'prefrontal_region_results_rest1_vs_rest3.csv')
    if os.path.exists(avgref_file):
        data['avgref_windows'] = pd.read_csv(avgref_file, encoding='utf-8')
        print(f"全脑平均参考数据: {len(data['avgref_windows'])} 行")
    
    # 加载双侧乳突参考数据
    tp9tp10_windows_file = os.path.join(TP9TP10_DIR, 'optimal_windows_rest1_vs_rest3_tp9tp10.csv')
    if os.path.exists(tp9tp10_windows_file):
        data['tp9tp10_windows'] = pd.read_csv(tp9tp10_windows_file, encoding='utf-8')
        print(f"双侧乳突参考窗口数据: {len(data['tp9tp10_windows'])} 行")
    
    tp9tp10_stats_file = os.path.join(TP9TP10_DIR, 'region_statistics_rest1_vs_rest3_tp9tp10.csv')
    if os.path.exists(tp9tp10_stats_file):
        data['tp9tp10_stats'] = pd.read_csv(tp9tp10_stats_file, encoding='utf-8')
        print(f"双侧乳突参考统计数据: {len(data['tp9tp10_stats'])} 行")
    
    return data

def create_comparison_visualization(data):
    """创建对比可视化图表"""
    
    if 'window_comparison' not in data:
        print("缺少窗口对比数据，无法创建可视化")
        return
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('双侧乳突参考 vs 全脑平均参考 HEP分析对比', fontsize=16, fontweight='bold')
    
    comparison_df = data['window_comparison']
    
    # 1. 时间差异分布 (左上)
    ax1 = axes[0, 0]
    regions = comparison_df['区域'].unique()
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    
    for i, region in enumerate(regions):
        region_data = comparison_df[comparison_df['区域'] == region]
        ax1.scatter(region_data['全脑平均参考时间(ms)'], region_data['双侧乳突参考时间(ms)'], 
                   label=region, color=colors[i % len(colors)], s=100, alpha=0.7)
    
    # 添加对角线（完全一致线）
    min_time = min(comparison_df['全脑平均参考时间(ms)'].min(), comparison_df['双侧乳突参考时间(ms)'].min())
    max_time = max(comparison_df['全脑平均参考时间(ms)'].max(), comparison_df['双侧乳突参考时间(ms)'].max())
    ax1.plot([min_time, max_time], [min_time, max_time], 'k--', alpha=0.5, label='完全一致线')
    
    ax1.set_xlabel('全脑平均参考时间 (ms)')
    ax1.set_ylabel('双侧乳突参考时间 (ms)')
    ax1.set_title('峰值时间对比散点图')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 时间差异柱状图 (右上)
    ax2 = axes[0, 1]
    region_means = comparison_df.groupby('区域')['绝对时间差异(ms)'].mean()
    bars = ax2.bar(range(len(region_means)), region_means.values, color=colors[:len(region_means)])
    ax2.set_xlabel('脑区')
    ax2.set_ylabel('平均绝对时间差异 (ms)')
    ax2.set_title('各脑区平均时间差异')
    ax2.set_xticks(range(len(region_means)))
    ax2.set_xticklabels(region_means.index, rotation=45)
    
    # 添加数值标签
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}ms', ha='center', va='bottom')
    
    # 3. 时间差异分布直方图 (左下)
    ax3 = axes[1, 0]
    ax3.hist(comparison_df['时间差异(ms)'], bins=10, alpha=0.7, color='skyblue', edgecolor='black')
    ax3.axvline(0, color='red', linestyle='--', alpha=0.7, label='零差异线')
    ax3.set_xlabel('时间差异 (ms)')
    ax3.set_ylabel('频次')
    ax3.set_title('时间差异分布直方图')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 经典窗口覆盖对比 (右下)
    ax4 = axes[1, 1]
    
    # 统计经典窗口覆盖情况
    if 'avgref_windows' in data and 'tp9tp10_windows' in data:
        avgref_classic = data['avgref_windows']['in_classic_window'].sum()
        avgref_total = len(data['avgref_windows'])
        tp9tp10_classic = data['tp9tp10_windows']['在经典窗口内'].sum()
        tp9tp10_total = len(data['tp9tp10_windows'])
        
        categories = ['全脑平均参考', '双侧乳突参考']
        classic_counts = [avgref_classic, tp9tp10_classic]
        total_counts = [avgref_total, tp9tp10_total]
        classic_ratios = [c/t*100 for c, t in zip(classic_counts, total_counts)]
        
        bars = ax4.bar(categories, classic_ratios, color=['orange', 'lightgreen'])
        ax4.set_ylabel('经典窗口覆盖率 (%)')
        ax4.set_title('经典窗口(200-300ms)覆盖率对比')
        ax4.set_ylim(0, 100)
        
        # 添加数值标签
        for i, (bar, count, total) in enumerate(zip(bars, classic_counts, total_counts)):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 2,
                    f'{height:.1f}%\n({count}/{total})', ha='center', va='bottom')
    
    plt.tight_layout()
    
    # 保存图片
    output_file = os.path.join(COMPARISON_DIR, 'reference_methods_comparison.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"对比可视化图表已保存: {output_file}")
    
    plt.show()
    
    return fig

def generate_detailed_report(data):
    """生成详细的对比报告"""
    
    print("\n" + "=" * 80)
    print("双侧乳突参考 vs 全脑平均参考 HEP分析详细对比报告")
    print("=" * 80)
    
    if 'window_comparison' not in data:
        print("缺少对比数据")
        return
    
    comparison_df = data['window_comparison']
    
    # 1. 总体统计
    print("\n1. 总体时间差异统计")
    print("-" * 40)
    print(f"总窗口数: {len(comparison_df)}")
    print(f"平均时间差异: {comparison_df['时间差异(ms)'].mean():.1f} ms")
    print(f"平均绝对时间差异: {comparison_df['绝对时间差异(ms)'].mean():.1f} ms")
    print(f"时间差异标准差: {comparison_df['时间差异(ms)'].std():.1f} ms")
    print(f"最大正向差异: {comparison_df['时间差异(ms)'].max():.1f} ms")
    print(f"最大负向差异: {comparison_df['时间差异(ms)'].min():.1f} ms")
    print(f"最大绝对差异: {comparison_df['绝对时间差异(ms)'].max():.1f} ms")
    
    # 2. 按脑区分析
    print("\n2. 各脑区时间差异分析")
    print("-" * 40)
    for region in comparison_df['区域'].unique():
        region_data = comparison_df[comparison_df['区域'] == region]
        print(f"\n{region}:")
        print(f"  窗口数: {len(region_data)}")
        print(f"  平均时间差异: {region_data['时间差异(ms)'].mean():.1f} ms")
        print(f"  平均绝对差异: {region_data['绝对时间差异(ms)'].mean():.1f} ms")
        print(f"  差异范围: {region_data['时间差异(ms)'].min():.1f} 到 {region_data['时间差异(ms)'].max():.1f} ms")
    
    # 3. 时间窗口分析
    print("\n3. 时间窗口分布分析")
    print("-" * 40)
    
    if 'avgref_windows' in data and 'tp9tp10_windows' in data:
        avgref_times = data['avgref_windows']['peak_time_ms'].values
        tp9tp10_times = data['tp9tp10_windows']['峰值时间(ms)'].values
        
        print(f"全脑平均参考峰值时间范围: {avgref_times.min():.1f} - {avgref_times.max():.1f} ms")
        print(f"双侧乳突参考峰值时间范围: {tp9tp10_times.min():.1f} - {tp9tp10_times.max():.1f} ms")
        print(f"全脑平均参考平均峰值时间: {avgref_times.mean():.1f} ms")
        print(f"双侧乳突参考平均峰值时间: {tp9tp10_times.mean():.1f} ms")
        
        # 经典窗口分析
        classic_window = (200, 300)
        avgref_classic = ((avgref_times >= classic_window[0]) & (avgref_times <= classic_window[1])).sum()
        tp9tp10_classic = ((tp9tp10_times >= classic_window[0]) & (tp9tp10_times <= classic_window[1])).sum()
        
        print(f"\n经典窗口(200-300ms)覆盖情况:")
        print(f"  全脑平均参考: {avgref_classic}/{len(avgref_times)} ({avgref_classic/len(avgref_times)*100:.1f}%)")
        print(f"  双侧乳突参考: {tp9tp10_classic}/{len(tp9tp10_times)} ({tp9tp10_classic/len(tp9tp10_times)*100:.1f}%)")
    
    # 4. 差异模式分析
    print("\n4. 差异模式分析")
    print("-" * 40)
    
    positive_diffs = comparison_df[comparison_df['时间差异(ms)'] > 0]
    negative_diffs = comparison_df[comparison_df['时间差异(ms)'] < 0]
    
    print(f"双侧乳突参考较晚的窗口: {len(positive_diffs)} 个 ({len(positive_diffs)/len(comparison_df)*100:.1f}%)")
    print(f"双侧乳突参考较早的窗口: {len(negative_diffs)} 个 ({len(negative_diffs)/len(comparison_df)*100:.1f}%)")
    
    if len(positive_diffs) > 0:
        print(f"较晚窗口平均差异: {positive_diffs['时间差异(ms)'].mean():.1f} ms")
    if len(negative_diffs) > 0:
        print(f"较早窗口平均差异: {negative_diffs['时间差异(ms)'].mean():.1f} ms")
    
    # 5. 结论和建议
    print("\n5. 主要发现和结论")
    print("-" * 40)
    
    mean_abs_diff = comparison_df['绝对时间差异(ms)'].mean()
    
    if mean_abs_diff < 30:
        consistency = "高度一致"
    elif mean_abs_diff < 60:
        consistency = "中等一致"
    else:
        consistency = "差异较大"
    
    print(f"• 两种参考方式的HEP峰值时间{consistency} (平均绝对差异: {mean_abs_diff:.1f}ms)")
    
    if comparison_df['时间差异(ms)'].mean() < 0:
        print("• 双侧乳突参考总体上产生较早的HEP峰值")
    else:
        print("• 双侧乳突参考总体上产生较晚的HEP峰值")
    
    # 找出差异最大的区域
    region_diffs = comparison_df.groupby('区域')['绝对时间差异(ms)'].mean()
    max_diff_region = region_diffs.idxmax()
    min_diff_region = region_diffs.idxmin()
    
    print(f"• {max_diff_region}显示最大差异 (平均{region_diffs[max_diff_region]:.1f}ms)")
    print(f"• {min_diff_region}显示最小差异 (平均{region_diffs[min_diff_region]:.1f}ms)")
    
    print(f"\n报告生成完成！详细数据保存在: {COMPARISON_DIR}")

def main():
    """主函数"""
    print("生成双侧乳突参考 vs 全脑平均参考详细对比报告")
    print("=" * 60)
    
    try:
        # 加载数据
        data = load_all_data()
        
        # 创建可视化
        print("\n创建对比可视化图表...")
        create_comparison_visualization(data)
        
        # 生成详细报告
        generate_detailed_report(data)
        
    except Exception as e:
        print(f"报告生成过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
