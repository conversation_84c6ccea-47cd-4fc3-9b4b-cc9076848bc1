import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import pearsonr
import matplotlib as mpl

# 设置全局字体和字号
mpl.rcParams['font.sans-serif'] = ['LXGW Wenkai']
mpl.rcParams['axes.unicode_minus'] = False
mpl.rcParams['font.size'] = 10
mpl.rcParams['figure.facecolor'] = 'white'
mpl.rcParams['axes.facecolor'] = 'white'

# 读取HEP特征表
feature_dir = r"D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/features"
files = [f for f in os.listdir(feature_dir) if f.startswith('central_eeg_features_0.5_0.6') and f.endswith('.xlsx')]
files.sort(key=lambda x: os.path.getmtime(os.path.join(feature_dir, x)), reverse=True)
file = os.path.join(feature_dir, files[0])
hep_df = pd.read_excel(file)

# 只保留rest1和test3
features = [c for c in hep_df.columns if c not in ['subject_id', 'stage', 'channel']]
subjects = set(hep_df[hep_df['stage']=='rest1']['subject_id']) & set(hep_df[hep_df['stage']=='test3']['subject_id'])

# 针对每个被试-阶段，中央区所有通道特征取均值
agg_rows = []
for subject in subjects:
    for stage in ['rest1', 'test3']:
        subdf = hep_df[(hep_df['subject_id']==subject) & (hep_df['stage']==stage)]
        if subdf.empty:
            continue
        row = {'subject_id': subject, 'stage': stage}
        for feat in features:
            row[feat] = subdf[feat].mean()
        agg_rows.append(row)
agg_df = pd.DataFrame(agg_rows)
rest1 = agg_df[agg_df['stage']=='rest1'].sort_values('subject_id')
test3 = agg_df[agg_df['stage']=='test3'].sort_values('subject_id')
assert all(rest1['subject_id'].values == test3['subject_id'].values)

# 计算特征变化（test3-rest1）
delta_df = test3.copy()
delta_df[features] = test3[features].values - rest1[features].values
delta_df['subject_id'] = rest1['subject_id'].values

# 读取心理韧性和分组
scale_file = r'C:/Users/<USER>/Desktop/stress0422.xlsx'
scale_df = pd.read_excel(scale_file)
if '心理韧性' not in scale_df.columns:
    raise ValueError('stress0422.xlsx中未找到"心理韧性"列')
subj_col = '编号' if '编号' in scale_df.columns else 'subject_id'
# 分组：前测特质焦虑≥40为高焦虑，<40为低焦虑
if '特质焦虑1' in scale_df.columns:
    scale_df['焦虑分组'] = np.where(scale_df['特质焦虑1'] >= 40, '高焦虑', '低焦虑')
else:
    scale_df['焦虑分组'] = '未知'
scale_df[subj_col] = scale_df[subj_col].astype(str)
# === 新增：心理韧性为0的用特质焦虑1最近邻填补 ===
if (scale_df['心理韧性'] == 0).any():
    if '特质焦虑1' not in scale_df.columns:
        raise ValueError('量表表格中未找到"特质焦虑1"列，无法用特质焦虑最近邻填补心理韧性！')
    zero_idx = scale_df[scale_df['心理韧性'] == 0].index
    nonzero_df = scale_df[scale_df['心理韧性'] != 0].copy()
    for idx in zero_idx:
        cur_anx = scale_df.loc[idx, '特质焦虑1']
        # 找到特质焦虑分数最近的非零被试
        nonzero_anx = nonzero_df['特质焦虑1'].values
        nearest_idx = np.argmin(np.abs(nonzero_anx - cur_anx))
        nearest_val = nonzero_df.iloc[nearest_idx]['心理韧性']
        scale_df.at[idx, '心理韧性'] = nearest_val
delta_df['subject_id'] = delta_df['subject_id'].astype(str)
merged = pd.merge(delta_df, scale_df[[subj_col, '心理韧性', '焦虑分组']], left_on='subject_id', right_on=subj_col)

# 相关性分析，筛选P值显著的特征
cor_results = []
all_results = []
out_dir = r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/hep_feature_resilience_correlation'
os.makedirs(out_dir, exist_ok=True)

# 英文特征名与中文对照
feature_name_map = {
    'mean': '均值',
    'std': '标准差',
    'max': '最大值',
    'min': '最小值',
    'pos_peak': '正峰值',
    'neg_peak': '负峰值',
    'pos_peak_latency': '正峰潜伏期',
    'neg_peak_latency': '负峰潜伏期',
    'peak2peak': '峰-峰值',
    'rms': '均方根',
    'energy': '能量',
    'slope': '斜率',
    'skew': '偏度',
    'kurtosis': '峰度',
    'main_freq': '主频率',
    'spec_centroid': '谱质心',
    'hjorth_activity': 'Hjorth活动性',
    'hjorth_mobility': 'Hjorth灵活性',
    'hjorth_complexity': 'Hjorth复杂性'
}

for feat in features:
    x = merged[feat]
    y = merged['心理韧性']
    r, p = pearsonr(x, y)
    all_results.append({'feat': feat, 'r': r, 'p': p})
    if p < 0.05:
        cor_results.append({'feat': feat, 'x': x, 'y': y, 'r': r, 'p': p, 'group': merged['焦虑分组']})

# 输出所有特征的相关性和P值
all_results_sorted = sorted(all_results, key=lambda d: d['p'])
print('所有特征的相关性和P值（按P值升序）：')
for item in all_results_sorted:
    print(f"{feature_name_map.get(item['feat'], item['feat'])}: r={item['r']:.2f}, p={item['p']:.4f}")

# 按P值从小到大排序
cor_results = sorted(cor_results, key=lambda d: d['p'])

n = len(cor_results)
print(f"显著特征数量: {n}, 分别为: {[item['feat'] for item in cor_results]}")

if n == 0:
    print('无P值显著的HEP特征与心理韧性相关！')
else:
    if n == 1:
        fig, ax = plt.subplots(figsize=(5, 4.5), constrained_layout=True)
        axes = [ax]
else:
    cols = 2 if n <= 2 else 3
    rows = int(np.ceil(n/cols))
    fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4.5*rows), constrained_layout=True)
    if isinstance(axes, np.ndarray):
        axes = axes.flatten()
    else:
        axes = np.array([axes])
    for i, item in enumerate(cor_results):
        print(f"正在绘制第{i+1}个子图，特征: {item['feat']}")
        ax = axes[i]
        x, y, group = item['x'], item['y'], item['group']
        for g, color in [('高焦虑', '#d62728'), ('低焦虑', '#1f77b4')]:
            mask = (group == g)
            ax.scatter(x[mask], y[mask], color=color, alpha=0.6, s=80, edgecolor='black', linewidth=0.8, label=g)
        coef = np.polyfit(x, y, 1)
        poly1d_fn = np.poly1d(coef)
        x_sorted = np.sort(x)
        ax.plot(x_sorted, poly1d_fn(x_sorted), color='#b2182b', linewidth=2, linestyle='-')
        feat_cn = feature_name_map.get(item["feat"], item["feat"])
        ax.set_xlabel(f'{feat_cn}变化', fontsize=14)
        ax.set_ylabel('心理韧性', fontsize=14)
        ax.set_title(f'{feat_cn}变化 vs 心理韧性', fontsize=14, fontweight='bold')
        y_margin = (y.max() - y.min()) * 0.15
        ax.set_ylim(y.min() - y_margin, y.max() + y_margin)
        for spine in ax.spines.values():
            spine.set_linewidth(1.2)
        ax.grid(False)
        ax.text(0.98, 0.98, f"r={item['r']:.2f}\np={item['p']:.4f}", fontsize=14, color='black', ha='right', va='top',
                transform=ax.transAxes, bbox=dict(facecolor='white', alpha=0.8, edgecolor='none', boxstyle='round,pad=0.2'))
    for j in range(i+1, len(axes)):
        axes[j].set_visible(False)
    from matplotlib.lines import Line2D
    legend_handles = [
        Line2D([0], [0], marker='o', color='w', label='高焦虑', markerfacecolor='#d62728', markersize=14, markeredgecolor='black', alpha=0.7),
        Line2D([0], [0], marker='o', color='w', label='低焦虑', markerfacecolor='#1f77b4', markersize=14, markeredgecolor='black', alpha=0.7)
    ]
    fig.suptitle('P值显著的HEP特征变化与心理韧性的相关性趋势图', fontsize=18, fontweight='bold', fontname='LXGW Wenkai', y=1.08)
    fig.legend(handles=legend_handles, labels=['高焦虑', '低焦虑'], fontsize=14, loc='upper center', bbox_to_anchor=(0.5, 1.04), ncol=2, frameon=False)
    fig.savefig(os.path.join(out_dir, 'hep_feature_resilience_correlation_sig.png'), dpi=600, bbox_inches='tight')
    fig.savefig(os.path.join(out_dir, 'hep_feature_resilience_correlation_sig.svg'), format='svg', bbox_inches='tight')
    plt.close(fig) 