#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HEP焦虑分组分析脚本

功能：
- 加载修复后的HEP数据(带subject_id的metadata)
- 加载焦虑量表数据
- 按焦虑水平将被试分为高低两组
- 分析两组在不同阶段的HEP差异
- 分析两组在多次刺激下的神经适应性差异
- 生成分析图表和统计结果

用法：
python 04_analyze_hep_anxiety_groups.py
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import mne
from scipy import stats
import seaborn as sns
from mpl_toolkits.axes_grid1 import make_axes_locatable
from matplotlib import cm
import matplotlib.colors as colors
from mne.viz import plot_topomap

# 设置matplotlib参数
plt.rcParams.update({
    'font.family': 'LXGW WenKai',  # 使用LXGW WenKai字体显示中文
    'font.size': 10,
    'axes.titlesize': 12,
    'axes.labelsize': 11,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 9,
    'figure.titlesize': 14,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'axes.grid': True,
    'grid.alpha': 0.3,
    'grid.linestyle': '--',
    'axes.axisbelow': True
})

# 定义根目录
ROOT_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'result')

# 定义路径
DATA_DIR = os.path.join(ROOT_DIR, 'hep_data', 'fixed')
PSYCH_DATA_PATH = r'D:\ecgeeg\30-数据分析\5-NeuroKit2\data\psychological_scales.csv'
OUTPUT_DIR = os.path.join(ROOT_DIR, 'hep_analysis', 'anxiety_groups')

# 确保输出目录存在
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 定义阶段名称
STAGES = {
    'prac': '练习阶段',
    'rest1': '静息态1',
    'test1': '刺激态1',
    'rest2': '静息态2',
    'test2': '刺激态2',
    'rest3': '静息态3',
    'test3': '刺激态3'
}

# 定义文件名映射
FILE_MAPPING = {
    'prac': ('01', 'prac'),   # 练习阶段: 01_prac
    'test1': ('01', 'test'),  # 刺激态1: 01_test
    'rest1': ('01', 'rest'),  # 静息态1: 01_rest
    'test2': ('02', 'test'),  # 刺激态2: 02_test
    'rest2': ('02', 'rest'),  # 静息态2: 02_rest
    'test3': ('03', 'test'),  # 刺激态3: 03_test
    'rest3': ('03', 'rest')   # 静息态3: 03_rest
}

# 定义关键通道和时间窗口
KEY_CHANNELS = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'F7', 'F8', 'T7', 'T8', 'P7', 'P8', 'O1', 'O2', 'Fp1', 'Fp2', 'FCz', 'CPz', 'FC1', 'FC2', 'CP1', 'CP2', 'FC3', 'FC4', 'CP3', 'CP4']
TIME_WINDOWS = [(0.2, 0.4), (0.4, 0.6), (0.5, 0.7)]

# 定义通道分组
CHANNEL_GROUPS = {
    'frontal': ['Fp1', 'Fp2', 'F3', 'F4', 'F7', 'F8', 'Fz'],
    'central': ['C3', 'C4', 'Cz', 'FCz', 'FC1', 'FC2', 'FC3', 'FC4'],
    'parietal': ['P3', 'P4', 'Pz', 'CPz', 'CP1', 'CP2', 'CP3', 'CP4'],
    'temporal': ['T7', 'T8'],
    'occipital': ['O1', 'O2', 'P7', 'P8'],
    'left': ['Fp1', 'F3', 'F7', 'C3', 'T7', 'P3', 'P7', 'O1', 'FC3', 'CP3'],
    'right': ['Fp2', 'F4', 'F8', 'C4', 'T8', 'P4', 'P8', 'O2', 'FC4', 'CP4'],
    'midline': ['Fz', 'FCz', 'Cz', 'CPz', 'Pz']
}

def load_psychological_data(file_path=PSYCH_DATA_PATH):
    """
    加载心理量表数据

    参数:
    file_path (str): 数据文件路径

    返回:
    pandas.DataFrame: 心理量表数据
    """
    print("加载心理量表数据...")

    try:
        # 根据文件扩展名选择加载方法
        if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
            # 加载Excel文件
            df = pd.read_excel(file_path)
        elif file_path.endswith('.csv'):
            # 加载CSV文件
            df = pd.read_csv(file_path)
        else:
            print(f"不支持的文件格式: {file_path}")
            return None

        print(f"成功加载心理量表数据，共{len(df)}名被试")

        # 确保'编号'列存在
        if '编号' not in df.columns:
            # 尝试查找其他可能的ID列
            potential_id_cols = [col for col in df.columns if '编号' in col or 'ID' in col.upper()]
            if potential_id_cols:
                id_col = potential_id_cols[0]
                df['编号'] = df[id_col]
                print(f"使用'{id_col}'列作为被试ID")

        # 转换编号为字符串，并确保两位数字
        df['编号'] = df['编号'].apply(lambda x: str(int(x)).zfill(2) if pd.notnull(x) else None)

        return df
    except Exception as e:
        print(f"加载心理量表数据失败: {e}")
        return None

def group_subjects_by_anxiety(psych_data, threshold=45):
    """
    根据特质焦虑水平将被试分为高焦虑组和低焦虑组

    参数:
    psych_data (pandas.DataFrame): 心理量表数据
    threshold (int): 高焦虑的阈值分数，默认为45分（STAI标准）

    返回:
    tuple: (高焦虑组被试ID列表, 低焦虑组被试ID列表)
    """
    print(f"根据特质焦虑水平将被试分组，高焦虑阈值: {threshold}分")

    # 确认包含焦虑数据的列
    anxiety_cols = [col for col in psych_data.columns if '特质焦虑' in col]

    if not anxiety_cols:
        print("警告: 未找到特质焦虑相关列")
        return [], []

    # 如果有多个特质焦虑列，优先使用'特质焦虑1'（实验前的测量）
    anxiety_col = '特质焦虑1' if '特质焦虑1' in anxiety_cols else anxiety_cols[0]
    print(f"使用'{anxiety_col}'列进行分组")

    # 分组 - 使用固定阈值45分
    high_anxiety_subjects = psych_data[psych_data[anxiety_col] >= threshold]['编号'].tolist()
    low_anxiety_subjects = psych_data[psych_data[anxiety_col] < threshold]['编号'].tolist()

    print(f"高焦虑组: {len(high_anxiety_subjects)}名被试，特质焦虑分数 >= {threshold}")
    print(f"低焦虑组: {len(low_anxiety_subjects)}名被试，特质焦虑分数 < {threshold}")

    return high_anxiety_subjects, low_anxiety_subjects

def load_hep_data():
    """
    加载修复后的HEP数据

    返回:
    dict: 各阶段的HEP数据
    """
    print("加载HEP数据...")

    # 存储各阶段的数据
    hep_data = {}

    # 加载每个阶段的数据
    for stage, (round_id, state) in FILE_MAPPING.items():
        # 尝试加载固定metadata的文件
        fixed_file_path = os.path.join(DATA_DIR, f'all_subjects_{round_id}_{state}_fixed.fif')
        if os.path.exists(fixed_file_path):
            try:
                epochs = mne.read_epochs(fixed_file_path, preload=True)

                # 确认metadata是否存在
                if not hasattr(epochs, 'metadata') or epochs.metadata is None:
                    print(f"警告: {stage}阶段的数据没有metadata")
                    hep_data[stage] = None
                    continue

                # 确认subject_id列是否存在
                if 'subject_id' not in epochs.metadata.columns:
                    print(f"警告: {stage}阶段的metadata中没有subject_id列")
                    hep_data[stage] = None
                    continue

                hep_data[stage] = epochs
                print(f"成功加载{STAGES[stage]}阶段的数据，包含{len(epochs)}个epochs，来自{len(epochs.metadata['subject_id'].unique())}个被试")
                continue
            except Exception as e:
                print(f"加载{STAGES[stage]}阶段固定metadata数据时出错: {e}")

        # 尝试加载合并的数据文件
        file_path = os.path.join(os.path.dirname(DATA_DIR), f'all_subjects_{round_id}_{state}.fif')
        if os.path.exists(file_path):
            try:
                epochs = mne.read_epochs(file_path, preload=True)

                # 确认metadata是否存在
                if not hasattr(epochs, 'metadata') or epochs.metadata is None:
                    print(f"警告: {stage}阶段的数据没有metadata")
                    hep_data[stage] = None
                    continue

                # 确认subject_id列是否存在
                if 'subject_id' not in epochs.metadata.columns:
                    print(f"警告: {stage}阶段的metadata中没有subject_id列")
                    hep_data[stage] = None
                    continue

                hep_data[stage] = epochs
                print(f"成功加载{STAGES[stage]}阶段的数据，包含{len(epochs)}个epochs，来自{len(epochs.metadata['subject_id'].unique())}个被试")
                continue
            except Exception as e:
                print(f"加载{STAGES[stage]}阶段合并数据时出错: {e}")

        # 如果以上都失败，标记为None
        print(f"未找到{STAGES[stage]}阶段的数据文件")
        hep_data[stage] = None

    return hep_data

def extract_hep_features_by_subject(hep_data, channels, time_window):
    """
    提取各被试的HEP特征

    参数:
    hep_data (dict): 各阶段的HEP数据
    channels (list): 要分析的通道列表
    time_window (tuple): 时间窗口 (start, end)，单位为秒

    返回:
    pandas.DataFrame: 各被试的HEP特征
    """
    print(f"提取HEP特征 (时间窗口: {time_window[0]}-{time_window[1]}s)...")

    # 创建结果DataFrame
    features = []

    # 对每个阶段进行分析
    for stage, epochs in hep_data.items():
        if epochs is None:
            continue

        # 获取被试ID列表
        subject_ids = epochs.metadata['subject_id'].unique()

        # 对每个通道进行分析
        for channel in channels:
            if channel in epochs.ch_names:
                # 获取通道索引
                ch_idx = epochs.ch_names.index(channel)

                # 获取数据
                data = epochs.get_data()[:, ch_idx, :] * 1e6  # 转换为微伏
                times = epochs.times

                # 找到时间窗口内的索引
                time_mask = (times >= time_window[0]) & (times <= time_window[1])

                # 按被试计算平均值
                for subj_id in subject_ids:
                    # 找到该被试的trials
                    subj_mask = epochs.metadata['subject_id'] == subj_id

                    # 如果该被试在该阶段有足够的trials
                    if np.sum(subj_mask) >= 5:  # 至少5个trials才计算
                        # 计算该被试在该通道、该时间窗口的平均振幅
                        subj_data = data[subj_mask]
                        subj_mean = np.mean(subj_data[:, time_mask], axis=(0, 1))

                        # 添加到特征中
                        features.append({
                            'subject_id': subj_id,
                            'stage': stage,
                            'channel': channel,
                            'hep_amplitude': subj_mean,
                            'time_window': f"{time_window[0]}-{time_window[1]}s"
                        })

    # 转换为DataFrame
    features_df = pd.DataFrame(features)

    # 保存结果
    output_path = os.path.join(OUTPUT_DIR, f'hep_features_{time_window[0]}_{time_window[1]}.csv')
    features_df.to_csv(output_path, index=False)
    print(f"已保存HEP特征至: {output_path}")

    return features_df

def compare_anxiety_groups(hep_features, high_anxiety_ids, low_anxiety_ids, channels, stages_to_analyze):
    """
    比较高低焦虑组的HEP差异

    参数:
    hep_features (pandas.DataFrame): HEP特征
    high_anxiety_ids (list): 高焦虑组被试ID列表
    low_anxiety_ids (list): 低焦虑组被试ID列表
    channels (list): 要分析的通道列表
    stages_to_analyze (list): 要分析的阶段列表

    返回:
    pandas.DataFrame: 组间比较结果
    """
    print("比较高低焦虑组的HEP差异...")

    # 创建结果DataFrame
    results = []

    # 对每个通道和阶段进行分析
    for channel in channels:
        for stage in stages_to_analyze:
            # 提取高焦虑组数据
            high_anxiety_data = hep_features[
                (hep_features['subject_id'].isin(high_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            # 提取低焦虑组数据
            low_anxiety_data = hep_features[
                (hep_features['subject_id'].isin(low_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            # 如果两组数据都不为空
            if len(high_anxiety_data) > 0 and len(low_anxiety_data) > 0:
                # 进行t检验
                t_stat, p_value = stats.ttest_ind(high_anxiety_data, low_anxiety_data)

                # 计算效应量 (Cohen's d)
                n1, n2 = len(high_anxiety_data), len(low_anxiety_data)
                s1, s2 = np.std(high_anxiety_data, ddof=1), np.std(low_anxiety_data, ddof=1)
                s_pooled = np.sqrt(((n1 - 1) * s1**2 + (n2 - 1) * s2**2) / (n1 + n2 - 2))
                cohen_d = (np.mean(high_anxiety_data) - np.mean(low_anxiety_data)) / s_pooled

                # 获取时间窗口信息
                time_window = hep_features['time_window'].iloc[0] if 'time_window' in hep_features.columns else 'unknown'

                # 添加到结果中
                results.append({
                    'channel': channel,
                    'stage': stage,
                    'stage_name': STAGES[stage],
                    'high_anxiety_mean': np.mean(high_anxiety_data),
                    'high_anxiety_sem': np.std(high_anxiety_data) / np.sqrt(len(high_anxiety_data)),
                    'low_anxiety_mean': np.mean(low_anxiety_data),
                    'low_anxiety_sem': np.std(low_anxiety_data) / np.sqrt(len(low_anxiety_data)),
                    'mean_diff': np.mean(high_anxiety_data) - np.mean(low_anxiety_data),
                    't_stat': t_stat,
                    'p_value': p_value,
                    'cohen_d': cohen_d,
                    'significant': p_value < 0.05,
                    'time_window': time_window  # 添加时间窗口信息
                })

    # 转换为DataFrame
    results_df = pd.DataFrame(results)

    # 保存结果
    time_window = hep_features['time_window'].iloc[0] if len(hep_features) > 0 else 'unknown'
    output_path = os.path.join(OUTPUT_DIR, f'anxiety_groups_comparison_{time_window}.csv')
    results_df.to_csv(output_path, index=False)
    print(f"已保存焦虑组比较结果至: {output_path}")

    return results_df

def analyze_adaptation_by_anxiety(hep_features, high_anxiety_ids, low_anxiety_ids, channels):
    """
    分析高低焦虑组的神经适应性差异

    参数:
    hep_features (pandas.DataFrame): HEP特征
    high_anxiety_ids (list): 高焦虑组被试ID列表
    low_anxiety_ids (list): 低焦虑组被试ID列表
    channels (list): 要分析的通道列表

    返回:
    pandas.DataFrame: 适应性分析结果
    """
    print("分析高低焦虑组的神经适应性差异...")

    # 定义要比较的阶段对
    stage_pairs = [('test1', 'test2'), ('test1', 'test3'), ('test2', 'test3')]

    # 创建结果DataFrame
    results = []

    # 获取时间窗口信息
    time_window = hep_features['time_window'].iloc[0] if len(hep_features) > 0 else 'unknown'

    # 对每个通道和阶段对进行分析
    for channel in channels:
        for stage1, stage2 in stage_pairs:
            # 高焦虑组在两个阶段的数据
            high_stage1 = hep_features[
                (hep_features['subject_id'].isin(high_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage1)
            ]

            high_stage2 = hep_features[
                (hep_features['subject_id'].isin(high_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage2)
            ]

            # 低焦虑组在两个阶段的数据
            low_stage1 = hep_features[
                (hep_features['subject_id'].isin(low_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage1)
            ]

            low_stage2 = hep_features[
                (hep_features['subject_id'].isin(low_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage2)
            ]

            # 计算配对的被试
            high_paired_subjects = set(high_stage1['subject_id']).intersection(set(high_stage2['subject_id']))
            low_paired_subjects = set(low_stage1['subject_id']).intersection(set(low_stage2['subject_id']))

            # 如果有足够的配对被试
            if len(high_paired_subjects) >= 5 and len(low_paired_subjects) >= 5:
                # 计算高焦虑组的适应性变化
                high_adaptation = []
                for subj in high_paired_subjects:
                    amp1 = high_stage1[high_stage1['subject_id'] == subj]['hep_amplitude'].values[0]
                    amp2 = high_stage2[high_stage2['subject_id'] == subj]['hep_amplitude'].values[0]
                    high_adaptation.append(amp2 - amp1)  # 计算变化量

                # 计算低焦虑组的适应性变化
                low_adaptation = []
                for subj in low_paired_subjects:
                    amp1 = low_stage1[low_stage1['subject_id'] == subj]['hep_amplitude'].values[0]
                    amp2 = low_stage2[low_stage2['subject_id'] == subj]['hep_amplitude'].values[0]
                    low_adaptation.append(amp2 - amp1)  # 计算变化量

                # 进行组间t检验
                t_stat, p_value = stats.ttest_ind(high_adaptation, low_adaptation)

                # 计算效应量 (Cohen's d)
                n1, n2 = len(high_adaptation), len(low_adaptation)
                s1, s2 = np.std(high_adaptation, ddof=1), np.std(low_adaptation, ddof=1)
                s_pooled = np.sqrt(((n1 - 1) * s1**2 + (n2 - 1) * s2**2) / (n1 + n2 - 2))
                cohen_d = (np.mean(high_adaptation) - np.mean(low_adaptation)) / s_pooled

                # 添加到结果中
                results.append({
                    'channel': channel,
                    'stage_pair': f"{stage1}-{stage2}",
                    'stage_pair_name': f"{STAGES[stage1]}-{STAGES[stage2]}",
                    'high_anxiety_mean': np.mean(high_adaptation),
                    'high_anxiety_sem': np.std(high_adaptation) / np.sqrt(len(high_adaptation)),
                    'low_anxiety_mean': np.mean(low_adaptation),
                    'low_anxiety_sem': np.std(low_adaptation) / np.sqrt(len(low_adaptation)),
                    'mean_diff': np.mean(high_adaptation) - np.mean(low_adaptation),
                    't_stat': t_stat,
                    'p_value': p_value,
                    'cohen_d': cohen_d,
                    'significant': p_value < 0.05,
                    'n_high': len(high_adaptation),
                    'n_low': len(low_adaptation),
                    'time_window': time_window  # 添加时间窗口信息
                })

    # 转换为DataFrame
    results_df = pd.DataFrame(results)

    # 保存结果
    time_window = hep_features['time_window'].iloc[0] if len(hep_features) > 0 else 'unknown'
    output_path = os.path.join(OUTPUT_DIR, f'adaptation_analysis_{time_window}.csv')
    results_df.to_csv(output_path, index=False)
    print(f"已保存适应性分析结果至: {output_path}")

    return results_df

def plot_anxiety_group_comparison(comparison_results, channels_to_plot, output_dir=OUTPUT_DIR):
    """
    绘制高低焦虑组HEP比较图

    参数:
    comparison_results (pandas.DataFrame): 组间比较结果
    channels_to_plot (list): 要绘制的通道列表
    output_dir (str): 输出目录
    """
    print("绘制高低焦虑组HEP比较图...")

    # 获取时间窗口信息
    if 'time_window' in comparison_results.columns:
        time_window = comparison_results['time_window'].iloc[0]
        # 确保时间窗口格式为 "0.X-0.Ys"
        if isinstance(time_window, str) and '-' in time_window:
            start, end = time_window.replace('s', '').split('-')
            time_str = f"{start.replace('.', '')}to{end.replace('.', '')}"
        else:
            time_str = f"{time_window}"
    else:
        # 尝试从文件名中提取时间窗口信息
        for col in comparison_results.columns:
            if 'time_window' in col.lower():
                time_window = comparison_results[col].iloc[0]
                if isinstance(time_window, str) and '-' in time_window:
                    start, end = time_window.replace('s', '').split('-')
                    time_str = f"{start.replace('.', '')}to{end.replace('.', '')}"
                    break
        else:
            time_str = "unknown"

    # 筛选要绘制的通道数据
    plot_data = comparison_results[comparison_results['channel'].isin(channels_to_plot)]

    # 按通道分组绘图
    for channel in channels_to_plot:
        channel_data = plot_data[plot_data['channel'] == channel]

        if len(channel_data) == 0:
            continue

        fig, ax = plt.subplots(figsize=(10, 6))

        # 设置x轴位置
        stages = channel_data['stage'].unique()
        x = np.arange(len(stages))
        width = 0.35

        # 准备数据
        high_means = channel_data['high_anxiety_mean'].values
        high_sems = channel_data['high_anxiety_sem'].values
        low_means = channel_data['low_anxiety_mean'].values
        low_sems = channel_data['low_anxiety_sem'].values

        # 绘制条形图
        rects1 = ax.bar(x - width/2, high_means, width, yerr=high_sems,
                        label='高焦虑组', color='#E63946', alpha=0.8, capsize=5)
        rects2 = ax.bar(x + width/2, low_means, width, yerr=low_sems,
                        label='低焦虑组', color='#457B9D', alpha=0.8, capsize=5)

        # 添加p值标记
        for i, p in enumerate(channel_data['p_value'].values):
            if p < 0.05:
                stars = '*' if p < 0.05 else '**' if p < 0.01 else '***' if p < 0.001 else ''
                y_pos = max(high_means[i] + high_sems[i], low_means[i] + low_sems[i]) + 0.5
                ax.text(x[i], y_pos, stars, ha='center', va='bottom', fontsize=12)

        # 设置图表元素
        ax.set_xlabel('实验阶段')
        ax.set_ylabel('HEP振幅 (μV)')
        ax.set_title(f'高低焦虑组在{channel}通道的HEP比较 ({time_str})')
        ax.set_xticks(x)
        ax.set_xticklabels([STAGES[stage] for stage in stages])
        ax.legend()

        # 调整y轴范围，确保所有标记都可见
        ymin, ymax = ax.get_ylim()
        ax.set_ylim(ymin, ymax*1.2)

        output_path = os.path.join(output_dir, f'anxiety_comparison_{channel}_{time_str}.png')
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()

        print(f"已保存{channel}通道的焦虑组比较图至: {output_path}")

def plot_adaptation_comparison(adaptation_results, channels_to_plot=None, output_dir=OUTPUT_DIR):
    """
    绘制高低焦虑组神经适应性比较图

    参数:
    adaptation_results (pandas.DataFrame): 适应性分析结果
    channels_to_plot (list): 要绘制的通道列表，如果为None则自动选择显著差异的通道
    output_dir (str): 输出目录
    """
    print("绘制高低焦虑组神经适应性比较图...")

    # 检查adaptation_results是否为空
    if adaptation_results is None or len(adaptation_results) == 0:
        print("警告: 适应性分析结果为空，无法绘制图表")
        return

    # 打印adaptation_results的列名，以便调试
    print(f"适应性分析结果的列名: {adaptation_results.columns.tolist()}")

    # 如果没有'channel'列，则跳过筛选步骤
    if 'channel' not in adaptation_results.columns:
        print("警告: 适应性分析结果中没有'channel'列，无法按通道筛选数据")
        return

    # 如果没有指定通道，则自动选择显著差异的通道
    if channels_to_plot is None:
        # 找出所有显著差异的通道
        sig_channels = adaptation_results[adaptation_results['p_value'] < 0.05]['channel'].unique()

        # 如果没有显著差异的通道，则选择效应量最大的前3个通道
        if len(sig_channels) == 0:
            # 按效应量绝对值排序
            sorted_channels = adaptation_results.groupby('channel')['cohen_d'].apply(
                lambda x: np.mean(np.abs(x))).sort_values(ascending=False)
            channels_to_plot = sorted_channels.index[:3].tolist()
        else:
            channels_to_plot = sig_channels.tolist()

    print(f"选择的通道: {channels_to_plot}")

    # 筛选要绘制的通道数据
    plot_data = adaptation_results[adaptation_results['channel'].isin(channels_to_plot)]

    # 如果筛选后的数据为空，则返回
    if len(plot_data) == 0:
        print(f"警告: 筛选后的适应性分析结果为空，无法绘制图表")
        return

    # 按通道分组绘图
    for channel in channels_to_plot:
        channel_data = plot_data[plot_data['channel'] == channel]

        if len(channel_data) == 0:
            continue

        fig, ax = plt.subplots(figsize=(10, 6))

        # 设置x轴位置
        stage_pairs = channel_data['stage_pair'].unique()
        x = np.arange(len(stage_pairs))
        width = 0.35

        # 准备数据
        high_means = channel_data['high_anxiety_mean'].values
        high_sems = channel_data['high_anxiety_sem'].values
        low_means = channel_data['low_anxiety_mean'].values
        low_sems = channel_data['low_anxiety_sem'].values
        p_values = channel_data['p_value'].values
        cohen_ds = channel_data['cohen_d'].values

        # 绘制条形图
        rects1 = ax.bar(x - width/2, high_means, width, yerr=high_sems,
                        label='高焦虑组', color='#E63946', alpha=0.8, capsize=5)
        rects2 = ax.bar(x + width/2, low_means, width, yerr=low_sems,
                        label='低焦虑组', color='#457B9D', alpha=0.8, capsize=5)

        # 添加数值标记和p值标记
        for i in range(len(stage_pairs)):
            # 高焦虑组数值
            ax.text(x[i] - width/2, high_means[i] + high_sems[i] + 0.1,
                   f"{high_means[i]:.2f}", ha='center', va='bottom', fontsize=8, color='#E63946')

            # 低焦虑组数值
            ax.text(x[i] + width/2, low_means[i] + low_sems[i] + 0.1,
                   f"{low_means[i]:.2f}", ha='center', va='bottom', fontsize=8, color='#457B9D')

            # p值和效应量
            p_text = f"p={p_values[i]:.3f}\nd={cohen_ds[i]:.2f}"

            # 添加p值标记
            if p_values[i] < 0.05:
                stars = '*' if p_values[i] < 0.05 else '**' if p_values[i] < 0.01 else '***' if p_values[i] < 0.001 else ''
                y_pos = max(abs(high_means[i]) + high_sems[i], abs(low_means[i]) + low_sems[i]) + 0.7
                if y_pos < 0:
                    y_pos = 0.7
                ax.text(x[i], y_pos, stars, ha='center', va='bottom', fontsize=12)

                # 在星号下方添加p值和效应量
                ax.text(x[i], y_pos - 0.3, p_text, ha='center', va='top', fontsize=8)
            else:
                # 如果不显著，只显示p值和效应量
                y_pos = max(abs(high_means[i]) + high_sems[i], abs(low_means[i]) + low_sems[i]) + 0.5
                if y_pos < 0:
                    y_pos = 0.5
                ax.text(x[i], y_pos, p_text, ha='center', va='bottom', fontsize=8)

        # 设置图表元素
        ax.set_xlabel('阶段对比')
        ax.set_ylabel('HEP振幅变化 (μV)')
        ax.set_title(f'高低焦虑组在{channel}通道的神经适应性比较')
        ax.set_xticks(x)
        ax.set_xticklabels([pair.replace('-', ' → ') for pair in channel_data['stage_pair'].values])
        ax.legend()

        # 添加零线
        ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)

        # 添加网格线
        ax.grid(True, linestyle='--', alpha=0.3)

        # 调整y轴范围，确保所有标记都可见
        ymin, ymax = ax.get_ylim()
        if ymin > -0.5:
            ymin = -0.5
        if ymax < 0.5:
            ymax = 0.5
        ax.set_ylim(ymin*1.2, ymax*1.5)  # 增加上边界以容纳标记

        # 保存图像
        time_window = channel_data['time_window'].iloc[0] if 'time_window' in channel_data.columns else 'unknown'
        if isinstance(time_window, str) and '-' in time_window:
            time_str = time_window.replace('-', 'to').replace('.', '')
        else:
            time_str = 'unknown'

        output_path = os.path.join(output_dir, f'adaptation_comparison_{channel}_{time_str}.png')
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()

        print(f"已保存{channel}通道的适应性比较图至: {output_path}")

def analyze_channel_groups(hep_features, high_anxiety_ids, low_anxiety_ids, channel_groups, stages):
    """
    按通道分组分析高低焦虑组的HEP差异

    参数:
    hep_features (pandas.DataFrame): HEP特征
    high_anxiety_ids (list): 高焦虑组被试ID列表
    low_anxiety_ids (list): 低焦虑组被试ID列表
    channel_groups (dict): 通道分组
    stages (list): 要分析的阶段列表

    返回:
    pandas.DataFrame: 分组分析结果
    """
    print("按通道分组分析高低焦虑组的HEP差异...")

    # 创建结果DataFrame
    results = []

    # 对每个通道组和阶段进行分析
    for group_name, channels in channel_groups.items():
        for stage in stages:
            # 提取高焦虑组在该通道组的数据
            high_anxiety_data = []
            for subject_id in high_anxiety_ids:
                # 获取该被试在该阶段、该通道组的所有通道的平均振幅
                subject_data = hep_features[
                    (hep_features['subject_id'] == subject_id) &
                    (hep_features['channel'].isin(channels)) &
                    (hep_features['stage'] == stage)
                ]

                if len(subject_data) > 0:
                    # 计算该被试在该通道组的平均振幅
                    mean_amp = subject_data['hep_amplitude'].mean()
                    high_anxiety_data.append(mean_amp)

            # 提取低焦虑组在该通道组的数据
            low_anxiety_data = []
            for subject_id in low_anxiety_ids:
                # 获取该被试在该阶段、该通道组的所有通道的平均振幅
                subject_data = hep_features[
                    (hep_features['subject_id'] == subject_id) &
                    (hep_features['channel'].isin(channels)) &
                    (hep_features['stage'] == stage)
                ]

                if len(subject_data) > 0:
                    # 计算该被试在该通道组的平均振幅
                    mean_amp = subject_data['hep_amplitude'].mean()
                    low_anxiety_data.append(mean_amp)

            # 如果两组数据都不为空
            if len(high_anxiety_data) > 0 and len(low_anxiety_data) > 0:
                # 进行t检验
                t_stat, p_value = stats.ttest_ind(high_anxiety_data, low_anxiety_data)

                # 计算效应量 (Cohen's d)
                n1, n2 = len(high_anxiety_data), len(low_anxiety_data)
                s1, s2 = np.std(high_anxiety_data, ddof=1), np.std(low_anxiety_data, ddof=1)
                s_pooled = np.sqrt(((n1 - 1) * s1**2 + (n2 - 1) * s2**2) / (n1 + n2 - 2))
                cohen_d = (np.mean(high_anxiety_data) - np.mean(low_anxiety_data)) / s_pooled

                # 获取时间窗口信息
                time_window = hep_features['time_window'].iloc[0] if 'time_window' in hep_features.columns else 'unknown'

                # 添加到结果中
                results.append({
                    'channel_group': group_name,
                    'stage': stage,
                    'stage_name': STAGES[stage],
                    'high_anxiety_mean': np.mean(high_anxiety_data),
                    'high_anxiety_sem': np.std(high_anxiety_data) / np.sqrt(len(high_anxiety_data)),
                    'low_anxiety_mean': np.mean(low_anxiety_data),
                    'low_anxiety_sem': np.std(low_anxiety_data) / np.sqrt(len(low_anxiety_data)),
                    'mean_diff': np.mean(high_anxiety_data) - np.mean(low_anxiety_data),
                    't_stat': t_stat,
                    'p_value': p_value,
                    'cohen_d': cohen_d,
                    'significant': p_value < 0.05,
                    'n_high': len(high_anxiety_data),
                    'n_low': len(low_anxiety_data),
                    'time_window': time_window  # 添加时间窗口信息
                })

    # 转换为DataFrame
    results_df = pd.DataFrame(results)

    # 保存结果
    time_window = hep_features['time_window'].iloc[0] if len(hep_features) > 0 else 'unknown'
    output_path = os.path.join(OUTPUT_DIR, f'channel_groups_analysis_{time_window}.csv')
    results_df.to_csv(output_path, index=False)
    print(f"已保存通道分组分析结果至: {output_path}")

    return results_df

def plot_channel_groups_comparison(group_results, output_dir=OUTPUT_DIR):
    """
    绘制通道分组的高低焦虑组比较图

    参数:
    group_results (pandas.DataFrame): 通道分组分析结果
    output_dir (str): 输出目录
    """
    print("绘制通道分组的高低焦虑组比较图...")

    # 检查group_results是否为空
    if group_results is None or len(group_results) == 0:
        print("警告: 通道分组分析结果为空，无法绘制图表")
        return

    # 获取时间窗口信息
    time_window = group_results['time_window'].iloc[0] if 'time_window' in group_results.columns else 'unknown'
    if isinstance(time_window, str):
        time_str = time_window.replace('-', 'to').replace('.', '')
    else:
        time_str = 'unknown'

    # 对每个阶段绘制一张图
    for stage in group_results['stage'].unique():
        stage_data = group_results[group_results['stage'] == stage]

        if len(stage_data) == 0:
            continue

        fig, ax = plt.subplots(figsize=(12, 8))

        # 设置x轴位置
        groups = stage_data['channel_group'].unique()
        x = np.arange(len(groups))
        width = 0.35

        # 准备数据
        high_means = []
        high_sems = []
        low_means = []
        low_sems = []
        p_values = []
        cohen_ds = []

        for group in groups:
            group_data = stage_data[stage_data['channel_group'] == group]
            high_means.append(group_data['high_anxiety_mean'].values[0])
            high_sems.append(group_data['high_anxiety_sem'].values[0])
            low_means.append(group_data['low_anxiety_mean'].values[0])
            low_sems.append(group_data['low_anxiety_sem'].values[0])
            p_values.append(group_data['p_value'].values[0])
            cohen_ds.append(group_data['cohen_d'].values[0])

        # 绘制条形图
        rects1 = ax.bar(x - width/2, high_means, width, yerr=high_sems,
                        label='高焦虑组', color='#E63946', alpha=0.8, capsize=5)
        rects2 = ax.bar(x + width/2, low_means, width, yerr=low_sems,
                        label='低焦虑组', color='#457B9D', alpha=0.8, capsize=5)

        # 添加数值标记和p值标记
        for i, group in enumerate(groups):
            # 高焦虑组数值
            ax.text(x[i] - width/2, high_means[i] + high_sems[i] + 0.1,
                   f"{high_means[i]:.2f}", ha='center', va='bottom', fontsize=8, color='#E63946')

            # 低焦虑组数值
            ax.text(x[i] + width/2, low_means[i] + low_sems[i] + 0.1,
                   f"{low_means[i]:.2f}", ha='center', va='bottom', fontsize=8, color='#457B9D')

            # p值和效应量
            p_text = f"p={p_values[i]:.3f}\nd={cohen_ds[i]:.2f}"

            # 添加p值标记
            if p_values[i] < 0.05:
                stars = '*' if p_values[i] < 0.05 else '**' if p_values[i] < 0.01 else '***' if p_values[i] < 0.001 else ''
                y_pos = max(high_means[i] + high_sems[i], low_means[i] + low_sems[i]) + 0.7
                ax.text(x[i], y_pos, stars, ha='center', va='bottom', fontsize=12)

                # 在星号下方添加p值和效应量
                ax.text(x[i], y_pos - 0.3, p_text, ha='center', va='top', fontsize=8)
            else:
                # 如果不显著，只显示p值和效应量
                y_pos = max(high_means[i] + high_sems[i], low_means[i] + low_sems[i]) + 0.5
                ax.text(x[i], y_pos, p_text, ha='center', va='bottom', fontsize=8)

        # 设置图表标题和标签
        ax.set_title(f"{STAGES[stage]}阶段各通道组的HEP振幅比较 ({time_window})")
        ax.set_ylabel('HEP振幅 (μV)')
        ax.set_xticks(x)
        ax.set_xticklabels(groups)
        ax.legend()

        # 添加网格线
        ax.grid(True, linestyle='--', alpha=0.3)

        # 调整y轴范围，确保所有标记都可见
        ymin, ymax = ax.get_ylim()
        ax.set_ylim(ymin, ymax*1.2)  # 增加上边界以容纳标记

        # 保存图表
        output_path = os.path.join(output_dir, f'channel_groups_{stage}_{time_str}.png')
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()

        print(f"已保存{STAGES[stage]}阶段的通道分组比较图至: {output_path}")

def plot_comprehensive_comparison(comparison_results, output_dir=OUTPUT_DIR):
    """
    绘制综合比较图 - 在一张图上展示多个通道和阶段的数据

    参数:
    comparison_results (pandas.DataFrame): 组间比较结果
    output_dir (str): 输出目录
    """
    print("绘制综合比较图...")

    # 检查comparison_results是否为空
    if comparison_results is None or len(comparison_results) == 0:
        print("警告: 比较结果为空，无法绘制综合图表")
        return

    # 获取时间窗口信息
    if 'time_window' in comparison_results.columns:
        time_window = comparison_results['time_window'].iloc[0]
        # 确保时间窗口格式为 "0.X-0.Ys"
        if isinstance(time_window, str) and '-' in time_window:
            start, end = time_window.replace('s', '').split('-')
            time_str = f"{start.replace('.', '')}to{end.replace('.', '')}"
        else:
            time_str = f"{time_window}"
    else:
        # 尝试从文件名中提取时间窗口信息
        for col in comparison_results.columns:
            if 'time_window' in col.lower():
                time_window = comparison_results[col].iloc[0]
                if isinstance(time_window, str) and '-' in time_window:
                    start, end = time_window.replace('s', '').split('-')
                    time_str = f"{start.replace('.', '')}to{end.replace('.', '')}"
                    break
        else:
            time_str = "unknown"

    # 1. 绘制热图 - 展示所有通道和阶段的差异
    # 创建差异矩阵
    channels = comparison_results['channel'].unique()
    stages = comparison_results['stage'].unique()

    diff_matrix = np.zeros((len(channels), len(stages)))
    p_matrix = np.ones((len(channels), len(stages)))

    for i, channel in enumerate(channels):
        for j, stage in enumerate(stages):
            data = comparison_results[(comparison_results['channel'] == channel) &
                                     (comparison_results['stage'] == stage)]
            if len(data) > 0:
                diff_matrix[i, j] = data['mean_diff'].values[0]
                p_matrix[i, j] = data['p_value'].values[0]

    # 创建热图
    fig, ax = plt.subplots(figsize=(12, 10))

    # 创建自定义颜色映射
    cmap = plt.cm.RdBu_r

    # 计算标准差，用于设置更合适的色标范围
    diff_std = np.std(diff_matrix)
    diff_mean = np.mean(diff_matrix)

    # 设置为均值±2个标准差，或者使用数据的实际最大最小值，取较小的范围
    vmax = min(np.max(diff_matrix), diff_mean + 2 * diff_std)
    vmin = max(np.min(diff_matrix), diff_mean - 2 * diff_std)

    # 确保色标是对称的
    vabs = max(abs(vmin), abs(vmax))
    vmin, vmax = -vabs, vabs

    # 绘制热图
    im = ax.imshow(diff_matrix, cmap=cmap, vmin=vmin, vmax=vmax, aspect='auto')

    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax)
    cbar.set_label('高焦虑组 - 低焦虑组 (μV)')

    # 设置坐标轴
    ax.set_xticks(np.arange(len(stages)))
    ax.set_yticks(np.arange(len(channels)))
    ax.set_xticklabels([STAGES[stage] for stage in stages])
    ax.set_yticklabels(channels)

    # 旋转x轴标签
    plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

    # 添加网格线
    ax.set_xticks(np.arange(-.5, len(stages), 1), minor=True)
    ax.set_yticks(np.arange(-.5, len(channels), 1), minor=True)
    ax.grid(which="minor", color="w", linestyle='-', linewidth=2)

    # 在每个单元格中添加p值标记和差异值
    for i in range(len(channels)):
        for j in range(len(stages)):
            # 添加p值标记
            if p_matrix[i, j] < 0.001:
                stars = '***'
            elif p_matrix[i, j] < 0.01:
                stars = '**'
            elif p_matrix[i, j] < 0.05:
                stars = '*'
            else:
                stars = ''

            # 添加差异值和p值标记
            value = diff_matrix[i, j]
            if abs(value) > 0.01:  # 只显示差异较大的值
                value_text = f"{value:.2f}\n{stars}"
            else:
                value_text = stars

            # 根据背景颜色选择文本颜色
            if value > 0:
                text_color = 'black' if value < vmax * 0.7 else 'white'
            else:
                text_color = 'black' if value > vmin * 0.7 else 'white'

            ax.text(j, i, value_text, ha="center", va="center",
                   color=text_color, fontweight="bold", fontsize=8)

    # 设置标题
    ax.set_title(f"高低焦虑组HEP差异热图 ({time_window})")

    # 保存图表
    output_path = os.path.join(output_dir, f'anxiety_heatmap_{time_str}.png')
    plt.tight_layout()
    plt.savefig(output_path, dpi=300)
    plt.close()

    print(f"已保存高低焦虑组HEP差异热图至: {output_path}")

    # 创建另一个版本的热图，只显示显著差异
    fig, ax = plt.subplots(figsize=(12, 10))

    # 创建掩码，只显示显著差异
    mask = p_matrix >= 0.05
    masked_diff = np.ma.array(diff_matrix, mask=mask)

    # 绘制热图
    im = ax.imshow(masked_diff, cmap=cmap, vmin=vmin, vmax=vmax, aspect='auto')

    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax)
    cbar.set_label('高焦虑组 - 低焦虑组 (μV)')

    # 设置坐标轴
    ax.set_xticks(np.arange(len(stages)))
    ax.set_yticks(np.arange(len(channels)))
    ax.set_xticklabels([STAGES[stage] for stage in stages])
    ax.set_yticklabels(channels)

    # 旋转x轴标签
    plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

    # 添加网格线
    ax.set_xticks(np.arange(-.5, len(stages), 1), minor=True)
    ax.set_yticks(np.arange(-.5, len(channels), 1), minor=True)
    ax.grid(which="minor", color="w", linestyle='-', linewidth=2)

    # 在每个单元格中添加p值标记和差异值
    for i in range(len(channels)):
        for j in range(len(stages)):
            if p_matrix[i, j] < 0.05:
                # 添加p值标记
                if p_matrix[i, j] < 0.001:
                    stars = '***'
                elif p_matrix[i, j] < 0.01:
                    stars = '**'
                else:
                    stars = '*'

                # 添加差异值和p值标记
                value = diff_matrix[i, j]
                value_text = f"{value:.2f}\n{stars}"

                # 根据背景颜色选择文本颜色
                if value > 0:
                    text_color = 'black' if value < vmax * 0.7 else 'white'
                else:
                    text_color = 'black' if value > vmin * 0.7 else 'white'

                ax.text(j, i, value_text, ha="center", va="center",
                       color=text_color, fontweight="bold", fontsize=9)

    # 设置标题
    ax.set_title(f"高低焦虑组显著HEP差异热图 (p<0.05, {time_window})")

    # 保存图表
    output_path = os.path.join(output_dir, f'anxiety_heatmap_significant_{time_str}.png')
    plt.tight_layout()
    plt.savefig(output_path, dpi=300)
    plt.close()

    print(f"已保存高低焦虑组显著HEP差异热图至: {output_path}")

    # 2. 绘制通道分组的综合比较图
    # 按前部、中部、后部分组
    channel_groups = {
        '前部': ['Fp1', 'Fp2', 'F3', 'F4', 'F7', 'F8', 'Fz'],
        '中部': ['C3', 'C4', 'Cz', 'T7', 'T8'],
        '后部': ['P3', 'P4', 'Pz', 'O1', 'O2']
    }

    # 计算每个分组的平均差异
    group_diff = {}
    group_p = {}

    for group_name, group_channels in channel_groups.items():
        group_diff[group_name] = []
        group_p[group_name] = []

        for stage in stages:
            # 筛选该分组和阶段的数据
            group_data = comparison_results[
                (comparison_results['channel'].isin(group_channels)) &
                (comparison_results['stage'] == stage)
            ]

            if len(group_data) > 0:
                # 计算平均差异
                mean_diff = group_data['mean_diff'].mean()
                group_diff[group_name].append(mean_diff)

                # 计算平均p值 (使用Fisher's方法合并p值)
                p_values = group_data['p_value'].values
                # 将p值限制在一个很小的非零值，避免log(0)
                p_values = np.maximum(p_values, 1e-10)
                chi_square = -2 * np.sum(np.log(p_values))
                combined_p = 1 - stats.chi2.cdf(chi_square, 2 * len(p_values))
                group_p[group_name].append(combined_p)
            else:
                group_diff[group_name].append(0)
                group_p[group_name].append(1)

    # 创建分组比较图
    fig, ax = plt.subplots(figsize=(12, 8))

    # 设置x轴位置
    x = np.arange(len(stages))
    width = 0.25
    offsets = np.linspace(-width, width, len(channel_groups))

    # 绘制条形图
    for i, (group_name, diffs) in enumerate(group_diff.items()):
        bars = ax.bar(x + offsets[i], diffs, width, label=group_name, alpha=0.7)

        # 添加显著性标记
        for j, p in enumerate(group_p[group_name]):
            if p < 0.05:
                stars = '*' if p < 0.05 else '**' if p < 0.01 else '***' if p < 0.001 else ''
                y_pos = diffs[j] + 0.1 if diffs[j] >= 0 else diffs[j] - 0.3
                ax.text(x[j] + offsets[i], y_pos, stars, ha="center", va="center", fontweight="bold")

    # 设置图表元素
    ax.set_xlabel('实验阶段')
    ax.set_ylabel('高焦虑组 - 低焦虑组 (μV)')
    ax.set_title(f'不同脑区的高低焦虑组HEP差异 ({time_window})')
    ax.set_xticks(x)
    ax.set_xticklabels([STAGES[stage] for stage in stages])
    ax.legend()

    # 添加零线
    ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)

    # 添加网格线
    ax.grid(True, linestyle='--', alpha=0.3)

    # 保存图表
    output_path = os.path.join(output_dir, f'anxiety_region_comparison_{time_str}.png')
    plt.tight_layout()
    plt.savefig(output_path, dpi=300)
    plt.close()

    print(f"已保存不同脑区的高低焦虑组HEP差异图至: {output_path}")

def plot_topographic_maps(hep_features, high_anxiety_ids, low_anxiety_ids, stages, output_dir=OUTPUT_DIR):
    """
    绘制HEP全脑地形图

    参数:
    hep_features (pandas.DataFrame): HEP特征
    high_anxiety_ids (list): 高焦虑组被试ID列表
    low_anxiety_ids (list): 低焦虑组被试ID列表
    stages (list): 要分析的阶段列表
    output_dir (str): 输出目录
    """
    print("绘制HEP全脑地形图...")

    # 检查hep_features是否为空
    if hep_features is None or len(hep_features) == 0:
        raise ValueError("HEP特征为空，无法绘制地形图")

    # 获取时间窗口信息
    if 'time_window' not in hep_features.columns:
        raise ValueError("HEP特征中缺少时间窗口信息")

    time_window = hep_features['time_window'].iloc[0]
    if isinstance(time_window, str):
        time_str = time_window.replace('-', 'to').replace('.', '')
    else:
        time_str = str(time_window).replace('-', 'to').replace('.', '')

    # 获取所有通道
    all_channels = hep_features['channel'].unique().tolist()
    if not all_channels:
        raise ValueError("HEP特征中没有通道信息")

    # 创建标准的10-20系统通道位置信息
    # 注意：这里使用MNE的标准通道位置，实际应用中可能需要根据实际数据调整
    info = mne.create_info(ch_names=all_channels, sfreq=1000, ch_types='eeg')
    montage = mne.channels.make_standard_montage('standard_1020')
    info.set_montage(montage)

    # 获取通道位置 - 修复方法
    pos = []
    missing_channels = []
    for ch in all_channels:
        if ch in montage.ch_names:
            ch_idx = montage.ch_names.index(ch)
            pos.append(montage.dig[ch_idx + 3]['r'][0:2])  # 只取x,y坐标
        else:
            missing_channels.append(ch)
            pos.append([0, 0])  # 使用默认位置

    if missing_channels:
        print(f"警告: 以下通道在标准蒙太奇中不存在，使用默认位置: {', '.join(missing_channels)}")

    pos = np.array(pos)

    # 对每个阶段绘制地形图
    for stage in stages:
        # 创建高焦虑组和低焦虑组的通道数据
        high_anxiety_data = {}
        low_anxiety_data = {}

        # 计算每个通道的平均振幅
        for channel in all_channels:
            # 高焦虑组
            high_channel_data = hep_features[
                (hep_features['subject_id'].isin(high_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            if len(high_channel_data) > 0:
                high_anxiety_data[channel] = np.mean(high_channel_data)
            else:
                high_anxiety_data[channel] = np.nan

            # 低焦虑组
            low_channel_data = hep_features[
                (hep_features['subject_id'].isin(low_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            if len(low_channel_data) > 0:
                low_anxiety_data[channel] = np.mean(low_channel_data)
            else:
                low_anxiety_data[channel] = np.nan

        # 计算差异值
        diff_data = {ch: high_anxiety_data[ch] - low_anxiety_data[ch] for ch in all_channels}

        # 创建数据数组
        high_data = np.array([high_anxiety_data[ch] for ch in all_channels])
        low_data = np.array([low_anxiety_data[ch] for ch in all_channels])
        diff_data_array = np.array([diff_data[ch] for ch in all_channels])

        # 创建地形图
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))

        # 设置颜色范围
        vmin = min(np.nanmin(high_data), np.nanmin(low_data))
        vmax = max(np.nanmax(high_data), np.nanmax(low_data))

        # 绘制高焦虑组地形图
        try:
            # 尝试新版本的参数
            im, _ = plot_topomap(high_data, pos, axes=axes[0], show=False,
                                cmap='RdBu_r', vlim=(vmin, vmax),
                                outlines='head', contours=6)
        except TypeError:
            # 尝试旧版本的参数
            im, _ = plot_topomap(high_data, pos, axes=axes[0], show=False,
                                cmap='RdBu_r', vmin=vmin, vmax=vmax,
                                outlines='head', contours=6)

        axes[0].set_title(f'高焦虑组 - {STAGES[stage]} ({time_window})')

        # 添加颜色条
        cbar = plt.colorbar(im, ax=axes[0], shrink=0.8)
        cbar.set_label('HEP振幅 (μV)')

        # 绘制低焦虑组地形图
        try:
            # 尝试新版本的参数
            im, _ = plot_topomap(low_data, pos, axes=axes[1], show=False,
                                cmap='RdBu_r', vlim=(vmin, vmax),
                                outlines='head', contours=6)
        except TypeError:
            # 尝试旧版本的参数
            im, _ = plot_topomap(low_data, pos, axes=axes[1], show=False,
                                cmap='RdBu_r', vmin=vmin, vmax=vmax,
                                outlines='head', contours=6)

        axes[1].set_title(f'低焦虑组 - {STAGES[stage]} ({time_window})')

        # 添加颜色条
        cbar = plt.colorbar(im, ax=axes[1], shrink=0.8)
        cbar.set_label('HEP振幅 (μV)')

        # 绘制差异地形图
        vmin_diff = np.nanmin(diff_data_array)
        vmax_diff = np.nanmax(diff_data_array)
        vabs = max(abs(vmin_diff), abs(vmax_diff))

        try:
            # 尝试新版本的参数
            im, _ = plot_topomap(diff_data_array, pos, axes=axes[2], show=False,
                                cmap='RdBu_r', vlim=(-vabs, vabs),
                                outlines='head', contours=6)
        except TypeError:
            # 尝试旧版本的参数
            im, _ = plot_topomap(diff_data_array, pos, axes=axes[2], show=False,
                                cmap='RdBu_r', vmin=-vabs, vmax=vabs,
                                outlines='head', contours=6)
        axes[2].set_title(f'差异 (高焦虑 - 低焦虑) - {STAGES[stage]} ({time_window})')

        # 添加颜色条
        cbar = plt.colorbar(im, ax=axes[2], shrink=0.8)
        cbar.set_label('HEP振幅差异 (μV)')

        # 保存图表
        output_path = os.path.join(output_dir, f'topographic_map_{stage}_{time_str}.png')
        plt.tight_layout()
        plt.savefig(output_path, dpi=300)
        plt.close()

        print(f"已保存{STAGES[stage]}阶段的全脑地形图至: {output_path}")

def plot_stages_summary(hep_features, high_anxiety_ids, low_anxiety_ids, stages, output_dir=OUTPUT_DIR):
    """
    绘制所有阶段的汇总图 - 将test1/rest1/test2/rest2/test3/rest3的结果整合在一个图上

    参数:
    hep_features (pandas.DataFrame): HEP特征
    high_anxiety_ids (list): 高焦虑组被试ID列表
    low_anxiety_ids (list): 低焦虑组被试ID列表
    stages (list): 要分析的阶段列表
    output_dir (str): 输出目录
    """
    print("绘制所有阶段的汇总图...")

    # 检查hep_features是否为空
    if hep_features is None or len(hep_features) == 0:
        raise ValueError("HEP特征为空，无法绘制汇总图")

    # 检查被试ID列表
    if not high_anxiety_ids or not low_anxiety_ids:
        raise ValueError("高焦虑组或低焦虑组被试ID列表为空")

    # 检查阶段列表
    if not stages:
        raise ValueError("阶段列表为空")

    # 获取时间窗口信息
    if 'time_window' not in hep_features.columns:
        raise ValueError("HEP特征中缺少时间窗口信息")

    time_window = hep_features['time_window'].iloc[0]
    if isinstance(time_window, str):
        time_str = time_window.replace('-', 'to').replace('.', '')
    else:
        time_str = str(time_window).replace('-', 'to').replace('.', '')

    # 获取所有通道
    all_channels = sorted(hep_features['channel'].unique().tolist())

    # 找出显著差异的通道
    significant_channels = []

    # 对每个阶段和通道进行t检验，找出显著差异的通道
    for channel in all_channels:
        for stage in stages:
            # 高焦虑组
            high_channel_data = hep_features[
                (hep_features['subject_id'].isin(high_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            # 低焦虑组
            low_channel_data = hep_features[
                (hep_features['subject_id'].isin(low_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            # 如果两组都有数据，进行t检验
            if len(high_channel_data) > 0 and len(low_channel_data) > 0:
                # 进行独立样本t检验
                t_stat, p_value = stats.ttest_ind(high_channel_data, low_channel_data, equal_var=False)

                # 如果p值小于0.05，则认为该通道在该阶段有显著差异
                if p_value < 0.05 and channel not in significant_channels:
                    significant_channels.append(channel)

    # 如果没有找到显著差异的通道，则使用默认通道
    if not significant_channels:
        print("未找到显著差异的通道，使用默认通道")
        # 选择前额叶、中央区、顶叶的代表性通道
        f_channels = [ch for ch in all_channels if ch.startswith('F') and not ch.startswith('FP')]
        c_channels = [ch for ch in all_channels if ch.startswith('C')]
        p_channels = [ch for ch in all_channels if ch.startswith('P')]

        key_channels = []
        if f_channels:
            key_channels.append(f_channels[0])
        if c_channels:
            key_channels.append(c_channels[0])
        if p_channels:
            key_channels.append(p_channels[0])

        # 如果还是没有足够的通道，则使用所有通道中的前3个
        if len(key_channels) < 3:
            key_channels = all_channels[:min(3, len(all_channels))]
    else:
        # 如果找到的显著差异通道超过3个，则只使用前3个
        key_channels = significant_channels[:min(3, len(significant_channels))]

    print(f"使用的敏感通道: {key_channels}")

    # 创建图表 - IEEE双栏格式 (宽度约为7.16英寸)
    fig, axes = plt.subplots(3, 2, figsize=(7.16, 9), constrained_layout=True)

    # 设置颜色
    high_color = '#E63946'  # 红色
    low_color = '#457B9D'   # 蓝色

    # 对每个通道绘制一行
    for i, channel in enumerate(key_channels):
        # 提取数据
        high_data = []
        high_sem = []
        low_data = []
        low_sem = []

        # 测试阶段数据 (左图)
        test_stages = [s for s in stages if s.startswith('test')]
        for stage in test_stages:
            # 高焦虑组
            high_channel_data = hep_features[
                (hep_features['subject_id'].isin(high_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            if len(high_channel_data) > 0:
                high_data.append(np.mean(high_channel_data))
                high_sem.append(np.std(high_channel_data) / np.sqrt(len(high_channel_data)))
            else:
                high_data.append(0)
                high_sem.append(0)

            # 低焦虑组
            low_channel_data = hep_features[
                (hep_features['subject_id'].isin(low_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            if len(low_channel_data) > 0:
                low_data.append(np.mean(low_channel_data))
                low_sem.append(np.std(low_channel_data) / np.sqrt(len(low_channel_data)))
            else:
                low_data.append(0)
                low_sem.append(0)

        # 绘制测试阶段数据 (左图)
        x = np.arange(len(test_stages))
        width = 0.35

        axes[i, 0].bar(x - width/2, high_data, width, yerr=high_sem,
                     label='高焦虑组', color=high_color, alpha=0.8, capsize=5)
        axes[i, 0].bar(x + width/2, low_data, width, yerr=low_sem,
                     label='低焦虑组', color=low_color, alpha=0.8, capsize=5)

        # 添加数值标记
        for j in range(len(test_stages)):
            # 高焦虑组数值
            axes[i, 0].text(x[j] - width/2, high_data[j] + high_sem[j] + 0.1,
                          f"{high_data[j]:.2f}", ha='center', va='bottom', fontsize=7, color=high_color)

            # 低焦虑组数值
            axes[i, 0].text(x[j] + width/2, low_data[j] + low_sem[j] + 0.1,
                          f"{low_data[j]:.2f}", ha='center', va='bottom', fontsize=7, color=low_color)

            # 计算p值
            stage = test_stages[j]
            high_stage_data = hep_features[
                (hep_features['subject_id'].isin(high_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            low_stage_data = hep_features[
                (hep_features['subject_id'].isin(low_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            if len(high_stage_data) > 0 and len(low_stage_data) > 0:
                t_stat, p_value = stats.ttest_ind(high_stage_data, low_stage_data, equal_var=False)

                # 添加p值标记
                if p_value < 0.05:
                    stars = '*' if p_value < 0.05 else '**' if p_value < 0.01 else '***' if p_value < 0.001 else ''
                    y_pos = max(high_data[j] + high_sem[j], low_data[j] + low_sem[j]) + 0.5
                    axes[i, 0].text(x[j], y_pos, stars, ha='center', va='bottom', fontsize=10)

                    # 在星号下方添加p值
                    axes[i, 0].text(x[j], y_pos - 0.2, f"p={p_value:.3f}", ha='center', va='top', fontsize=6)

        # 设置图表元素
        axes[i, 0].set_xlabel('测试阶段')
        axes[i, 0].set_ylabel('HEP振幅 (μV)')
        axes[i, 0].set_title(f'{channel}通道 - 测试阶段')
        axes[i, 0].set_xticks(x)
        axes[i, 0].set_xticklabels([STAGES[stage] for stage in test_stages])

        # 添加网格线
        axes[i, 0].grid(True, linestyle='--', alpha=0.3)

        # 调整y轴范围，确保所有标记都可见
        ymin, ymax = axes[i, 0].get_ylim()
        axes[i, 0].set_ylim(ymin, ymax*1.2)

        # 重置数据
        high_data = []
        high_sem = []
        low_data = []
        low_sem = []

        # 休息阶段数据 (右图)
        rest_stages = [s for s in stages if s.startswith('rest')]
        for stage in rest_stages:
            # 高焦虑组
            high_channel_data = hep_features[
                (hep_features['subject_id'].isin(high_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            if len(high_channel_data) > 0:
                high_data.append(np.mean(high_channel_data))
                high_sem.append(np.std(high_channel_data) / np.sqrt(len(high_channel_data)))
            else:
                high_data.append(0)
                high_sem.append(0)

            # 低焦虑组
            low_channel_data = hep_features[
                (hep_features['subject_id'].isin(low_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            if len(low_channel_data) > 0:
                low_data.append(np.mean(low_channel_data))
                low_sem.append(np.std(low_channel_data) / np.sqrt(len(low_channel_data)))
            else:
                low_data.append(0)
                low_sem.append(0)

        # 绘制休息阶段数据 (右图)
        x = np.arange(len(rest_stages))

        axes[i, 1].bar(x - width/2, high_data, width, yerr=high_sem,
                     label='高焦虑组', color=high_color, alpha=0.8, capsize=5)
        axes[i, 1].bar(x + width/2, low_data, width, yerr=low_sem,
                     label='低焦虑组', color=low_color, alpha=0.8, capsize=5)

        # 添加数值标记
        for j in range(len(rest_stages)):
            # 高焦虑组数值
            axes[i, 1].text(x[j] - width/2, high_data[j] + high_sem[j] + 0.1,
                          f"{high_data[j]:.2f}", ha='center', va='bottom', fontsize=7, color=high_color)

            # 低焦虑组数值
            axes[i, 1].text(x[j] + width/2, low_data[j] + low_sem[j] + 0.1,
                          f"{low_data[j]:.2f}", ha='center', va='bottom', fontsize=7, color=low_color)

            # 计算p值
            stage = rest_stages[j]
            high_stage_data = hep_features[
                (hep_features['subject_id'].isin(high_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            low_stage_data = hep_features[
                (hep_features['subject_id'].isin(low_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            if len(high_stage_data) > 0 and len(low_stage_data) > 0:
                _, p_value = stats.ttest_ind(high_stage_data, low_stage_data, equal_var=False)

                # 添加p值标记
                if p_value < 0.05:
                    stars = '*' if p_value < 0.05 else '**' if p_value < 0.01 else '***' if p_value < 0.001 else ''
                    y_pos = max(high_data[j] + high_sem[j], low_data[j] + low_sem[j]) + 0.5
                    axes[i, 1].text(x[j], y_pos, stars, ha='center', va='bottom', fontsize=10)

                    # 在星号下方添加p值
                    axes[i, 1].text(x[j], y_pos - 0.2, f"p={p_value:.3f}", ha='center', va='top', fontsize=6)

        # 设置图表元素
        axes[i, 1].set_xlabel('休息阶段')
        axes[i, 1].set_ylabel('HEP振幅 (μV)')
        axes[i, 1].set_title(f'{channel}通道 - 休息阶段')
        axes[i, 1].set_xticks(x)
        axes[i, 1].set_xticklabels([STAGES[stage] for stage in rest_stages])

        # 添加网格线
        axes[i, 1].grid(True, linestyle='--', alpha=0.3)

        # 调整y轴范围，确保所有标记都可见
        ymin, ymax = axes[i, 1].get_ylim()
        axes[i, 1].set_ylim(ymin, ymax*1.2)

    # 添加图例
    handles, labels = axes[0, 0].get_legend_handles_labels()
    fig.legend(handles, labels, loc='upper center', bbox_to_anchor=(0.5, 0.02), ncol=2)

    # 添加总标题
    fig.suptitle(f'高低焦虑组在不同阶段的HEP振幅比较 ({time_window})', fontsize=14)

    # 保存图表
    output_path = os.path.join(output_dir, f'stages_summary_{time_str}.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"已保存所有阶段的汇总图至: {output_path}")

def identify_significant_regions(hep_features, high_anxiety_ids, low_anxiety_ids, stages, p_threshold=0.05, output_dir=OUTPUT_DIR):
    """
    从全脑地形图数据中识别差异显著的区域

    参数:
    hep_features (pandas.DataFrame): HEP特征
    high_anxiety_ids (list): 高焦虑组被试ID列表
    low_anxiety_ids (list): 低焦虑组被试ID列表
    stages (list): 要分析的阶段列表
    p_threshold (float): p值阈值，用于确定显著性
    output_dir (str): 输出目录

    返回:
    dict: 每个阶段的显著电极列表
    """
    print("识别差异显著的脑区...")

    # 检查hep_features是否为空
    if hep_features is None or len(hep_features) == 0:
        raise ValueError("HEP特征为空，无法识别显著脑区")

    # 获取时间窗口信息
    if 'time_window' not in hep_features.columns:
        raise ValueError("HEP特征中缺少时间窗口信息")

    time_window = hep_features['time_window'].iloc[0]
    if isinstance(time_window, str):
        time_str = time_window.replace('-', 'to').replace('.', '')
    else:
        time_str = str(time_window).replace('-', 'to').replace('.', '')

    # 获取所有通道
    all_channels = sorted(hep_features['channel'].unique().tolist())

    # 存储每个阶段的显著电极
    significant_electrodes = {}

    # 对每个阶段进行分析
    for stage in stages:
        # 存储每个通道的统计结果
        channel_stats = []

        # 对每个通道进行t检验
        for channel in all_channels:
            # 高焦虑组
            high_channel_data = hep_features[
                (hep_features['subject_id'].isin(high_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            # 低焦虑组
            low_channel_data = hep_features[
                (hep_features['subject_id'].isin(low_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            # 如果两组都有数据，进行t检验
            if len(high_channel_data) > 0 and len(low_channel_data) > 0:
                # 进行独立样本t检验
                t_stat, p_value = stats.ttest_ind(high_channel_data, low_channel_data, equal_var=False)

                # 计算效应量 (Cohen's d)
                cohen_d = (np.mean(high_channel_data) - np.mean(low_channel_data)) / np.sqrt(
                    ((len(high_channel_data) - 1) * np.std(high_channel_data, ddof=1) ** 2 +
                     (len(low_channel_data) - 1) * np.std(low_channel_data, ddof=1) ** 2) /
                    (len(high_channel_data) + len(low_channel_data) - 2)
                )

                # 存储结果
                channel_stats.append({
                    'channel': channel,
                    'high_mean': np.mean(high_channel_data),
                    'low_mean': np.mean(low_channel_data),
                    'mean_diff': np.mean(high_channel_data) - np.mean(low_channel_data),
                    't_stat': t_stat,
                    'p_value': p_value,
                    'cohen_d': cohen_d,
                    'significant': p_value < p_threshold
                })

        # 转换为DataFrame
        stats_df = pd.DataFrame(channel_stats)

        # 应用FDR校正
        if len(stats_df) > 0:
            # 使用Benjamini-Hochberg程序进行FDR校正
            from statsmodels.stats.multitest import multipletests
            _, p_corrected, _, _ = multipletests(stats_df['p_value'].values, alpha=p_threshold, method='fdr_bh')
            stats_df['p_corrected'] = p_corrected
            stats_df['significant_corrected'] = p_corrected < p_threshold

            # 获取校正后显著的电极
            sig_electrodes = stats_df[stats_df['significant_corrected']]['channel'].tolist()
            significant_electrodes[stage] = sig_electrodes

            # 保存统计结果
            output_path = os.path.join(output_dir, f'significant_electrodes_{stage}_{time_str}.csv')
            stats_df.to_csv(output_path, index=False)
            print(f"已保存{stage}阶段的显著电极统计结果至: {output_path}")

            # 创建显著电极的脑地形图
            if sig_electrodes:
                # 创建一个掩码数组，只显示显著的电极
                mask = np.zeros(len(all_channels), dtype=bool)
                for i, ch in enumerate(all_channels):
                    if ch in sig_electrodes:
                        mask[i] = True

                # 创建数据数组
                data = np.zeros(len(all_channels))
                for i, row in stats_df.iterrows():
                    idx = all_channels.index(row['channel'])
                    data[idx] = row['mean_diff']  # 使用均值差异作为数据

                # 获取通道位置
                montage = mne.channels.make_standard_montage('standard_1020')
                pos = []
                for ch in all_channels:
                    if ch in montage.ch_names:
                        ch_idx = montage.ch_names.index(ch)
                        pos.append(montage.dig[ch_idx + 3]['r'][0:2])  # 只取x,y坐标
                    else:
                        pos.append([0, 0])  # 使用默认位置
                pos = np.array(pos)

                # 创建地形图
                fig, ax = plt.subplots(figsize=(10, 8))

                # 设置颜色范围
                vmin, vmax = np.min(data), np.max(data)
                vabs = max(abs(vmin), abs(vmax))

                try:
                    # 尝试新版本的参数
                    im, _ = plot_topomap(data, pos, axes=ax, show=False,
                                       cmap='RdBu_r', vlim=(-vabs, vabs),
                                       outlines='head', contours=6, mask=mask,
                                       mask_params=dict(marker='o', markerfacecolor='black', markeredgecolor='white',
                                                       linewidth=0, markersize=8))
                except TypeError:
                    # 尝试旧版本的参数
                    im, _ = plot_topomap(data, pos, axes=ax, show=False,
                                       cmap='RdBu_r', vmin=-vabs, vmax=vabs,
                                       outlines='head', contours=6, mask=mask,
                                       mask_params=dict(marker='o', markerfacecolor='black', markeredgecolor='white',
                                                       linewidth=0, markersize=8))

                ax.set_title(f'{stage}阶段的显著电极 (p < {p_threshold}, FDR校正)')

                # 添加颜色条
                cbar = plt.colorbar(im, ax=ax, shrink=0.8)
                cbar.set_label('HEP振幅差异 (高焦虑 - 低焦虑) (μV)')

                # 保存图表
                output_path = os.path.join(output_dir, f'significant_electrodes_map_{stage}_{time_str}.png')
                plt.tight_layout()
                plt.savefig(output_path, dpi=300)
                plt.close()

                print(f"已保存{stage}阶段的显著电极地形图至: {output_path}")
        else:
            significant_electrodes[stage] = []

    return significant_electrodes

def analyze_significant_electrodes(hep_features, high_anxiety_ids, low_anxiety_ids, significant_electrodes, output_dir=OUTPUT_DIR):
    """
    对识别出的显著电极进行详细的HEP差异分析

    参数:
    hep_features (pandas.DataFrame): HEP特征
    high_anxiety_ids (list): 高焦虑组被试ID列表
    low_anxiety_ids (list): 低焦虑组被试ID列表
    significant_electrodes (dict): 每个阶段的显著电极列表
    output_dir (str): 输出目录
    """
    print("分析显著电极的HEP差异...")

    # 检查hep_features是否为空
    if hep_features is None or len(hep_features) == 0:
        raise ValueError("HEP特征为空，无法分析显著电极")

    # 获取时间窗口信息
    if 'time_window' not in hep_features.columns:
        raise ValueError("HEP特征中缺少时间窗口信息")

    time_window = hep_features['time_window'].iloc[0]
    if isinstance(time_window, str):
        time_str = time_window.replace('-', 'to').replace('.', '')
    else:
        time_str = str(time_window).replace('-', 'to').replace('.', '')

    # 检查是否有显著电极
    if not any(significant_electrodes.values()):
        print(f"警告: 在时间窗口 {time_window} 中没有找到显著电极")
        return

    # 创建结果目录
    sig_output_dir = os.path.join(output_dir, 'significant_electrodes')
    os.makedirs(sig_output_dir, exist_ok=True)

    # 对每个阶段进行分析
    for stage, electrodes in significant_electrodes.items():
        if not electrodes:
            continue

        print(f"分析{stage}阶段的{len(electrodes)}个显著电极...")

        # 创建图表 - IEEE双栏格式 (宽度约为7.16英寸)
        n_electrodes = len(electrodes)
        fig_height = max(6, n_electrodes * 0.8)  # 根据电极数量调整高度
        fig, axes = plt.subplots(n_electrodes, 1, figsize=(7.16, fig_height), constrained_layout=True, sharex=True)

        # 如果只有一个电极，确保axes是一个列表
        if n_electrodes == 1:
            axes = [axes]

        # 设置颜色
        high_color = '#E63946'  # 红色
        low_color = '#457B9D'   # 蓝色

        # 对每个显著电极进行分析
        for i, electrode in enumerate(electrodes):
            ax = axes[i]

            # 提取该电极在该阶段的数据
            high_data = hep_features[
                (hep_features['subject_id'].isin(high_anxiety_ids)) &
                (hep_features['channel'] == electrode) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            low_data = hep_features[
                (hep_features['subject_id'].isin(low_anxiety_ids)) &
                (hep_features['channel'] == electrode) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            # 计算统计量
            t_stat, p_value = stats.ttest_ind(high_data, low_data, equal_var=False)
            cohen_d = (np.mean(high_data) - np.mean(low_data)) / np.sqrt(
                ((len(high_data) - 1) * np.std(high_data, ddof=1) ** 2 +
                 (len(low_data) - 1) * np.std(low_data, ddof=1) ** 2) /
                (len(high_data) + len(low_data) - 2)
            )

            # 绘制小提琴图
            parts = ax.violinplot(
                [high_data, low_data],
                positions=[0, 1],
                showmeans=False,
                showmedians=False,
                showextrema=False
            )

            # 设置小提琴图颜色
            for pc, color in zip(parts['bodies'], [high_color, low_color]):
                pc.set_facecolor(color)
                pc.set_edgecolor('black')
                pc.set_alpha(0.7)

            # 添加箱线图
            boxprops = dict(linestyle='-', linewidth=1.5, color='black')
            whiskerprops = dict(linestyle='-', linewidth=1.5, color='black')
            medianprops = dict(linestyle='-', linewidth=1.5, color='white')

            ax.boxplot(
                [high_data, low_data],
                positions=[0, 1],
                widths=0.15,
                boxprops=boxprops,
                whiskerprops=whiskerprops,
                medianprops=medianprops
            )

            # 添加散点图
            for j, (data, pos, color) in enumerate(zip([high_data, low_data], [0, 1], [high_color, low_color])):
                # 添加抖动
                x = np.random.normal(pos, 0.04, size=len(data))
                ax.scatter(x, data, color=color, alpha=0.6, s=20, edgecolor='none')

            # 添加均值和标准误差
            for j, (data, pos, color) in enumerate(zip([high_data, low_data], [0, 1], [high_color, low_color])):
                mean = np.mean(data)
                sem = np.std(data) / np.sqrt(len(data))
                ax.errorbar(pos, mean, yerr=sem, fmt='o', color='white', markerfacecolor=color,
                           markeredgecolor='black', markersize=8, ecolor='black', capsize=5, linewidth=2)

            # 设置图表元素
            ax.set_ylabel('HEP振幅 (μV)')
            ax.set_title(f'{electrode} (t={t_stat:.2f}, p={p_value:.4f}, d={cohen_d:.2f})')
            ax.grid(True, linestyle='--', alpha=0.3)

            # 设置x轴刻度和标签
            ax.set_xticks([0, 1])
            ax.set_xticklabels(['高焦虑组', '低焦虑组'])

            # 添加显著性标记
            if p_value < 0.001:
                sig_text = '***'
            elif p_value < 0.01:
                sig_text = '**'
            elif p_value < 0.05:
                sig_text = '*'
            else:
                sig_text = 'n.s.'

            y_max = max(np.max(high_data), np.max(low_data))
            y_min = min(np.min(high_data), np.min(low_data))
            y_range = y_max - y_min

            ax.plot([0, 1], [y_max + y_range * 0.1] * 2, '-', color='black')
            ax.text(0.5, y_max + y_range * 0.15, sig_text, ha='center', va='bottom', fontsize=12)

        # 添加总标题
        fig.suptitle(f'{stage}阶段显著电极的HEP振幅比较 ({time_window})', fontsize=14)

        # 保存图表
        output_path = os.path.join(sig_output_dir, f'significant_electrodes_detail_{stage}_{time_str}.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"已保存{stage}阶段显著电极的详细分析图至: {output_path}")

        # 创建显著电极的平均波形图
        # 这里我们假设hep_features中包含了波形数据，如果没有，这部分可以跳过
        if 'waveform' in hep_features.columns:
            # 创建波形图
            fig, ax = plt.subplots(figsize=(7.16, 5), constrained_layout=True)

            # 提取波形数据
            high_waveforms = []
            low_waveforms = []

            for electrode in electrodes:
                high_electrode_data = hep_features[
                    (hep_features['subject_id'].isin(high_anxiety_ids)) &
                    (hep_features['channel'] == electrode) &
                    (hep_features['stage'] == stage)
                ]['waveform'].values

                low_electrode_data = hep_features[
                    (hep_features['subject_id'].isin(low_anxiety_ids)) &
                    (hep_features['channel'] == electrode) &
                    (hep_features['stage'] == stage)
                ]['waveform'].values

                if len(high_electrode_data) > 0 and len(low_electrode_data) > 0:
                    high_waveforms.extend(high_electrode_data)
                    low_waveforms.extend(low_electrode_data)

            if high_waveforms and low_waveforms:
                # 计算平均波形
                high_mean = np.mean(high_waveforms, axis=0)
                high_sem = np.std(high_waveforms, axis=0) / np.sqrt(len(high_waveforms))

                low_mean = np.mean(low_waveforms, axis=0)
                low_sem = np.std(low_waveforms, axis=0) / np.sqrt(len(low_waveforms))

                # 创建时间轴
                time_axis = np.linspace(-0.2, 0.8, len(high_mean))  # 假设波形从-200ms到800ms

                # 绘制波形
                ax.plot(time_axis, high_mean, color=high_color, linewidth=2, label='高焦虑组')
                ax.fill_between(time_axis, high_mean - high_sem, high_mean + high_sem, color=high_color, alpha=0.3)

                ax.plot(time_axis, low_mean, color=low_color, linewidth=2, label='低焦虑组')
                ax.fill_between(time_axis, low_mean - low_sem, low_mean + low_sem, color=low_color, alpha=0.3)

                # 添加时间窗口阴影
                if isinstance(time_window, str) and '-' in time_window:
                    start, end = time_window.replace('s', '').split('-')
                    start, end = float(start), float(end)
                    ax.axvspan(start, end, color='gray', alpha=0.2)

                # 设置图表元素
                ax.set_xlabel('时间 (s)')
                ax.set_ylabel('振幅 (μV)')
                ax.set_title(f'{stage}阶段显著电极的平均HEP波形 ({len(electrodes)}个电极)')
                ax.grid(True, linestyle='--', alpha=0.3)
                ax.legend()

                # 添加R波标记
                ax.axvline(x=0, color='black', linestyle='--', linewidth=1)
                ax.text(0, ax.get_ylim()[1], 'R波', ha='center', va='bottom')

                # 保存图表
                output_path = os.path.join(sig_output_dir, f'significant_electrodes_waveform_{stage}_{time_str}.png')
                plt.savefig(output_path, dpi=300, bbox_inches='tight')
                plt.close()

                print(f"已保存{stage}阶段显著电极的平均波形图至: {output_path}")

def plot_comprehensive_timeline(hep_features, high_anxiety_ids, low_anxiety_ids, output_dir=OUTPUT_DIR):
    """
    绘制综合时间线图 - 将所有阶段按时间顺序排列，并按状态类型分组

    参数:
    hep_features (pandas.DataFrame): HEP特征
    high_anxiety_ids (list): 高焦虑组被试ID列表
    low_anxiety_ids (list): 低焦虑组被试ID列表
    output_dir (str): 输出目录
    """
    print("绘制综合时间线图...")

    # 检查hep_features是否为空
    if hep_features is None or len(hep_features) == 0:
        raise ValueError("HEP特征为空，无法绘制综合时间线图")

    # 检查被试ID列表
    if not high_anxiety_ids or not low_anxiety_ids:
        raise ValueError("高焦虑组或低焦虑组被试ID列表为空")

    # 获取时间窗口信息
    if 'time_window' not in hep_features.columns:
        raise ValueError("HEP特征中缺少时间窗口信息")

    time_window = hep_features['time_window'].iloc[0]
    if isinstance(time_window, str):
        time_str = time_window.replace('-', 'to').replace('.', '')
    else:
        time_str = str(time_window).replace('-', 'to').replace('.', '')

    # 定义所有阶段和它们的时间顺序
    all_stages = ['prac', 'test1', 'rest1', 'test2', 'rest2', 'test3', 'rest3']

    # 按状态类型分组
    practice_stages = ['prac']
    test_stages = ['test1', 'test2', 'test3']
    rest_stages = ['rest1', 'rest2', 'rest3']

    # 获取所有通道
    all_channels = sorted(hep_features['channel'].unique().tolist())

    # 选择要显示的通道 - 包括更多通道而不仅仅是Fz, Cz, Pz
    # 选择前额叶、中央区、顶叶、枕叶和颞叶的代表性通道
    display_channels = []

    # 前额叶通道 (F)
    f_channels = [ch for ch in all_channels if ch.startswith('F') and not ch.startswith('FP')]
    if f_channels:
        display_channels.extend(sorted(f_channels)[:3])  # 最多选择3个

    # 中央区通道 (C)
    c_channels = [ch for ch in all_channels if ch.startswith('C')]
    if c_channels:
        display_channels.extend(sorted(c_channels)[:3])  # 最多选择3个

    # 顶叶通道 (P)
    p_channels = [ch for ch in all_channels if ch.startswith('P')]
    if p_channels:
        display_channels.extend(sorted(p_channels)[:3])  # 最多选择3个

    # 枕叶通道 (O)
    o_channels = [ch for ch in all_channels if ch.startswith('O')]
    if o_channels:
        display_channels.extend(sorted(o_channels)[:2])  # 最多选择2个

    # 颞叶通道 (T)
    t_channels = [ch for ch in all_channels if ch.startswith('T')]
    if t_channels:
        display_channels.extend(sorted(t_channels)[:2])  # 最多选择2个

    # 如果没有足够的通道，则使用所有可用通道
    if len(display_channels) < 5:
        display_channels = all_channels[:min(10, len(all_channels))]

    # 确保关键通道在列表中
    for key_ch in ['Fz', 'Cz', 'Pz']:
        if key_ch in all_channels and key_ch not in display_channels:
            display_channels.append(key_ch)

    # 限制通道数量，避免图表过于拥挤
    if len(display_channels) > 12:
        display_channels = display_channels[:12]

    # 创建图表 - IEEE双栏格式 (宽度约为7.16英寸)
    fig, axes = plt.subplots(3, 1, figsize=(7.16, 10), constrained_layout=True, sharex=True)

    # 设置颜色
    high_color = '#E63946'  # 红色
    low_color = '#457B9D'   # 蓝色

    # 设置x轴标签位置
    x_positions = np.arange(len(all_stages))

    # 1. 绘制练习状态 (上图)
    ax = axes[0]

    # 为每个通道创建一组数据
    for i, channel in enumerate(display_channels):
        high_means = []
        high_sems = []
        low_means = []
        low_sems = []

        for stage in practice_stages:
            # 高焦虑组
            high_channel_data = hep_features[
                (hep_features['subject_id'].isin(high_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            if len(high_channel_data) > 0:
                high_means.append(np.mean(high_channel_data))
                high_sems.append(np.std(high_channel_data) / np.sqrt(len(high_channel_data)))
            else:
                high_means.append(np.nan)
                high_sems.append(np.nan)

            # 低焦虑组
            low_channel_data = hep_features[
                (hep_features['subject_id'].isin(low_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            if len(low_channel_data) > 0:
                low_means.append(np.mean(low_channel_data))
                low_sems.append(np.std(low_channel_data) / np.sqrt(len(low_channel_data)))
            else:
                low_means.append(np.nan)
                low_sems.append(np.nan)

        # 绘制高焦虑组
        ax.errorbar(x_positions[:len(practice_stages)], high_means, yerr=high_sems,
                   fmt='o-', color=high_color, alpha=0.7, label=f'{channel} 高焦虑' if i == 0 else None,
                   linewidth=1.5, markersize=4)

        # 绘制低焦虑组
        ax.errorbar(x_positions[:len(practice_stages)], low_means, yerr=low_sems,
                   fmt='s--', color=low_color, alpha=0.7, label=f'{channel} 低焦虑' if i == 0 else None,
                   linewidth=1.5, markersize=4)

    # 设置图表元素
    ax.set_ylabel('HEP振幅 (μV)')
    ax.set_title('练习状态')
    ax.grid(True, linestyle='--', alpha=0.3)

    # 添加通道图例
    handles, labels = [], []
    for channel in display_channels:
        line = plt.Line2D([0], [0], color='black', marker='o', markersize=4, label=channel)
        handles.append(line)
        labels.append(channel)

    legend1 = ax.legend(handles, labels, loc='upper right', title='通道', ncol=3, fontsize='small')
    ax.add_artist(legend1)

    # 添加组别图例
    high_line = plt.Line2D([0], [0], color=high_color, marker='o', linestyle='-', markersize=4, label='高焦虑组')
    low_line = plt.Line2D([0], [0], color=low_color, marker='s', linestyle='--', markersize=4, label='低焦虑组')
    ax.legend([high_line, low_line], ['高焦虑组', '低焦虑组'], loc='upper left', fontsize='small')

    # 2. 绘制刺激态 (中图)
    ax = axes[1]

    # 为每个通道创建一组数据
    for i, channel in enumerate(display_channels):
        high_means = []
        high_sems = []
        low_means = []
        low_sems = []

        for stage in test_stages:
            # 高焦虑组
            high_channel_data = hep_features[
                (hep_features['subject_id'].isin(high_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            if len(high_channel_data) > 0:
                high_means.append(np.mean(high_channel_data))
                high_sems.append(np.std(high_channel_data) / np.sqrt(len(high_channel_data)))
            else:
                high_means.append(np.nan)
                high_sems.append(np.nan)

            # 低焦虑组
            low_channel_data = hep_features[
                (hep_features['subject_id'].isin(low_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            if len(low_channel_data) > 0:
                low_means.append(np.mean(low_channel_data))
                low_sems.append(np.std(low_channel_data) / np.sqrt(len(low_channel_data)))
            else:
                low_means.append(np.nan)
                low_sems.append(np.nan)

        # 计算x轴位置 - 对应all_stages中test阶段的位置
        test_indices = [all_stages.index(stage) for stage in test_stages]

        # 绘制高焦虑组
        ax.errorbar(x_positions[test_indices], high_means, yerr=high_sems,
                   fmt='o-', color=high_color, alpha=0.7, label=f'{channel} 高焦虑' if i == 0 else None,
                   linewidth=1.5, markersize=4)

        # 绘制低焦虑组
        ax.errorbar(x_positions[test_indices], low_means, yerr=low_sems,
                   fmt='s--', color=low_color, alpha=0.7, label=f'{channel} 低焦虑' if i == 0 else None,
                   linewidth=1.5, markersize=4)

    # 设置图表元素
    ax.set_ylabel('HEP振幅 (μV)')
    ax.set_title('刺激态')
    ax.grid(True, linestyle='--', alpha=0.3)

    # 3. 绘制静息态 (下图)
    ax = axes[2]

    # 为每个通道创建一组数据
    for i, channel in enumerate(display_channels):
        high_means = []
        high_sems = []
        low_means = []
        low_sems = []

        for stage in rest_stages:
            # 高焦虑组
            high_channel_data = hep_features[
                (hep_features['subject_id'].isin(high_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            if len(high_channel_data) > 0:
                high_means.append(np.mean(high_channel_data))
                high_sems.append(np.std(high_channel_data) / np.sqrt(len(high_channel_data)))
            else:
                high_means.append(np.nan)
                high_sems.append(np.nan)

            # 低焦虑组
            low_channel_data = hep_features[
                (hep_features['subject_id'].isin(low_anxiety_ids)) &
                (hep_features['channel'] == channel) &
                (hep_features['stage'] == stage)
            ]['hep_amplitude'].values

            if len(low_channel_data) > 0:
                low_means.append(np.mean(low_channel_data))
                low_sems.append(np.std(low_channel_data) / np.sqrt(len(low_channel_data)))
            else:
                low_means.append(np.nan)
                low_sems.append(np.nan)

        # 计算x轴位置 - 对应all_stages中rest阶段的位置
        rest_indices = [all_stages.index(stage) for stage in rest_stages]

        # 绘制高焦虑组
        ax.errorbar(x_positions[rest_indices], high_means, yerr=high_sems,
                   fmt='o-', color=high_color, alpha=0.7, label=f'{channel} 高焦虑' if i == 0 else None,
                   linewidth=1.5, markersize=4)

        # 绘制低焦虑组
        ax.errorbar(x_positions[rest_indices], low_means, yerr=low_sems,
                   fmt='s--', color=low_color, alpha=0.7, label=f'{channel} 低焦虑' if i == 0 else None,
                   linewidth=1.5, markersize=4)

    # 设置图表元素
    ax.set_xlabel('实验阶段')
    ax.set_ylabel('HEP振幅 (μV)')
    ax.set_title('静息态')
    ax.grid(True, linestyle='--', alpha=0.3)

    # 设置x轴刻度和标签
    ax.set_xticks(x_positions)
    ax.set_xticklabels([STAGES.get(stage, stage) for stage in all_stages])

    # 添加总标题
    fig.suptitle(f'高低焦虑组在不同阶段的HEP振幅比较 ({time_window})', fontsize=14)

    # 保存图表
    output_path = os.path.join(output_dir, f'comprehensive_timeline_{time_str}.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"已保存综合时间线图至: {output_path}")
    return output_path

def main():
    """主函数"""
    print("\n开始HEP焦虑分组分析...")

    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 加载心理量表数据
    psych_data = load_psychological_data()
    if psych_data is None:
        print("无法继续分析，缺少心理量表数据")
        return

    # 按焦虑水平分组
    high_anxiety_ids, low_anxiety_ids = group_subjects_by_anxiety(psych_data)
    if not high_anxiety_ids or not low_anxiety_ids:
        print("无法继续分析，焦虑分组失败")
        return

    # 加载HEP数据
    hep_data = load_hep_data()
    if not any(hep_data.values()):
        print("无法继续分析，缺少HEP数据")
        return

    # 定义要分析的阶段
    test_stages = ['test1', 'test2', 'test3']
    all_stages = ['prac', 'test1', 'rest1', 'test2', 'rest2', 'test3', 'rest3']

    # 对每个时间窗口进行分析
    for time_window in TIME_WINDOWS:
        try:
            # 提取HEP特征
            hep_features = extract_hep_features_by_subject(hep_data, KEY_CHANNELS, time_window)

            if len(hep_features) == 0:
                print(f"警告: 时间窗口 {time_window} 的HEP特征为空，跳过此时间窗口的分析")
                continue

            # 比较高低焦虑组的HEP差异
            try:
                comparison_results = compare_anxiety_groups(hep_features, high_anxiety_ids, low_anxiety_ids,
                                                          KEY_CHANNELS, test_stages)

                # 绘制比较图
                if comparison_results is not None and len(comparison_results) > 0:
                    try:
                        # 只绘制关键通道的比较图，减少图片数量
                        plot_anxiety_group_comparison(comparison_results, ['Fz', 'Cz', 'Pz'])

                        # 绘制综合比较图，在一张图上展示多个通道和阶段的数据
                        plot_comprehensive_comparison(comparison_results)
                    except Exception as e:
                        print(f"绘制焦虑组比较图时出错: {e}")
                else:
                    print("警告: 焦虑组比较结果为空，跳过绘图")
            except Exception as e:
                print(f"比较高低焦虑组的HEP差异时出错: {e}")
                comparison_results = None

            # 分析适应性差异
            try:
                adaptation_results = analyze_adaptation_by_anxiety(hep_features, high_anxiety_ids,
                                                                low_anxiety_ids, KEY_CHANNELS)

                # 绘制适应性比较图
                if adaptation_results is not None and len(adaptation_results) > 0:
                    try:
                        # 使用敏感通道绘制适应性比较图
                        plot_adaptation_comparison(adaptation_results)
                    except Exception as e:
                        print(f"绘制适应性比较图时出错: {e}")
                else:
                    print("警告: 适应性分析结果为空，跳过绘图")
            except Exception as e:
                print(f"分析适应性差异时出错: {e}")
                adaptation_results = None

            # 按通道分组分析
            try:
                group_results = analyze_channel_groups(hep_features, high_anxiety_ids, low_anxiety_ids,
                                                    CHANNEL_GROUPS, test_stages)

                # 绘制通道分组比较图
                if group_results is not None and len(group_results) > 0:
                    try:
                        plot_channel_groups_comparison(group_results)
                    except Exception as e:
                        print(f"绘制通道分组比较图时出错: {e}")
                else:
                    print("警告: 通道分组分析结果为空，跳过绘图")
            except Exception as e:
                print(f"按通道分组分析时出错: {e}")
                group_results = None

            # 绘制全脑地形图 - 只为test1阶段绘制，减少图片数量
            plot_topographic_maps(hep_features, high_anxiety_ids, low_anxiety_ids, ['test1'])

            # 绘制所有阶段的汇总图
            plot_stages_summary(hep_features, high_anxiety_ids, low_anxiety_ids, all_stages)

            # 不再绘制综合时间线图，因为不符合需求

            # 识别差异显著的脑区
            significant_electrodes = identify_significant_regions(hep_features, high_anxiety_ids, low_anxiety_ids, all_stages)

            # 对显著电极进行详细分析
            analyze_significant_electrodes(hep_features, high_anxiety_ids, low_anxiety_ids, significant_electrodes)

        except Exception as e:
            print(f"处理时间窗口 {time_window} 时出错: {e}")
            continue

    print("\nHEP焦虑分组分析完成！")
    print(f"输出文件保存在: {OUTPUT_DIR}")

if __name__ == "__main__":
    main()