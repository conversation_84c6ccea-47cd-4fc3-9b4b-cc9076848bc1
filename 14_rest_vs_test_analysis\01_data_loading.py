#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
静息态与刺激态对比分析 - 模块1：数据加载与预处理

功能：
- 加载HEP数据和心理量表数据
- 提取静息态（rest1-3）和刺激态（test1-3）的HEP振幅
- 重点关注Fz、Cz、Pz导联在0.4-0.6s和0.5-0.7s时间窗口
- 整合数据并进行预处理
- 保存处理后的数据集供后续分析使用

作者：AI助手
日期：2024年
"""

import os
import numpy as np
import pandas as pd
import h5py
import matplotlib.pyplot as plt
from datetime import datetime

# 定义常量
HEP_RAW_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\13_raw_epochs"
RATINGS_FILE = r"C:\Users\<USER>\Desktop\stress0422.xlsx"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\14_rest_vs_test_analysis"

# 定义关键导联和时间窗口
KEY_CHANNELS = ['Fz', 'Cz', 'Pz']  # 关键中线导联
TIME_WINDOWS = [(0.4, 0.6), (0.5, 0.7)]  # 关键时间窗口

def create_output_directory():
    """创建输出目录"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    print(f"已创建输出目录: {OUTPUT_DIR}")

def load_subjective_ratings(file_path=RATINGS_FILE):
    """
    加载主观量表数据
    
    参数:
    file_path (str): 主观量表数据文件路径
    
    返回:
    pd.DataFrame: 主观量表数据
    """
    print(f"加载主观量表数据: {file_path}")
    
    # 读取Excel文件
    ratings_df = pd.read_excel(file_path)
    
    print(f"原始量表列名: {ratings_df.columns.tolist()}")
    
    # 定义列名映射
    column_mapping = {
        '编号': 'subject_id',
        '被试姓名': 'subject_name',
        '压力1': 'stress_1',
        '压力2': 'stress_2',
        '压力3': 'stress_3',
        '成功1': 'success_1',
        '成功2': 'success_2',
        '成功3': 'success_3',
        '自信1': 'confidence_1',
        '自信2': 'confidence_2',
        '自信3': 'confidence_3',
        '疼痛1': 'pain_1',
        '疼痛2': 'pain_2',
        '疼痛3': 'pain_3',
        '特质焦虑0': 'trait_anxiety_0',
        '特质焦虑1': 'trait_anxiety_1',
        '特质焦虑2': 'trait_anxiety_2',
        '状态焦虑1': 'state_anxiety_1',
        '状态焦虑2': 'state_anxiety_2',
        ' 心理韧性': 'resilience',
        '坚韧': 'tenacity',
        '乐观': 'optimism',
        '力量': 'strength'
    }
    
    # 只重命名存在的列
    rename_dict = {k: v for k, v in column_mapping.items() if k in ratings_df.columns}
    ratings_df = ratings_df.rename(columns=rename_dict)
    
    print(f"重命名后的列名: {ratings_df.columns.tolist()}")
    
    # 确保subject_id是字符串类型
    if 'subject_id' in ratings_df.columns:
        ratings_df['subject_id'] = ratings_df['subject_id'].astype(str).str.zfill(2)
    
    # 检查关键列是否存在
    required_columns = ['subject_id', 'resilience', 'trait_anxiety_1', 'state_anxiety_1', 'state_anxiety_2']
    missing_columns = [col for col in required_columns if col not in ratings_df.columns]
    
    if missing_columns:
        print(f"警告: 以下关键列缺失: {missing_columns}")
    else:
        print("所有关键列均存在")
    
    # 检查数据完整性
    print(f"主观量表数据包含{len(ratings_df)}个被试")
    
    return ratings_df

def find_hep_files(hep_dir=HEP_RAW_DIR):
    """
    查找HEP数据文件
    
    参数:
    hep_dir (str): HEP数据目录
    
    返回:
    dict: 各阶段的HEP文件路径
    """
    print(f"查找HEP数据文件: {hep_dir}")
    
    # 定义阶段
    stages = ['rest1', 'rest2', 'rest3', 'test1', 'test2', 'test3']
    
    # 存储各阶段的文件
    stage_files = {}
    
    # 查找各阶段的文件
    for stage in stages:
        # 查找该阶段的所有文件
        files = [f for f in os.listdir(hep_dir) if f.startswith(f"{stage}_raw_epochs") and f.endswith('.h5')]
        
        if files:
            # 选择最新的文件
            latest_file = max([os.path.join(hep_dir, f) for f in files], key=os.path.getmtime)
            stage_files[stage] = latest_file
            print(f"  - {stage}: {os.path.basename(latest_file)}")
        else:
            print(f"  - {stage}: 未找到文件")
    
    return stage_files

def extract_hep_features(hep_files, channels=KEY_CHANNELS, time_windows=TIME_WINDOWS):
    """
    从HEP数据文件中提取特征
    
    参数:
    hep_files (dict): 各阶段的HEP文件路径
    channels (list): 要提取的通道
    time_windows (list): 要提取的时间窗口
    
    返回:
    pd.DataFrame: HEP特征数据
    """
    print("提取HEP特征...")
    
    # 存储所有特征
    all_features = []
    
    # 处理每个阶段的文件
    for stage, file_path in hep_files.items():
        print(f"处理{stage}阶段: {os.path.basename(file_path)}")
        
        # 加载H5文件
        with h5py.File(file_path, 'r') as f:
            # 获取数据
            data = f['data'][:]  # (n_epochs, n_channels, n_times)
            times = f['times'][:]
            ch_names = [ch.decode('utf-8') for ch in f['ch_names'][:]]
            subject_ids = [subj.decode('utf-8') for subj in f['subject_ids'][:]]
            
            # 获取通道索引
            ch_indices = [ch_names.index(ch) for ch in channels if ch in ch_names]
            
            if not ch_indices:
                print(f"警告: 未找到指定的通道: {channels}")
                continue
            
            # 对每个时间窗口计算平均振幅
            for window_start, window_end in time_windows:
                # 获取时间窗口索引
                time_mask = (times >= window_start) & (times <= window_end)
                
                # 对每个通道计算平均振幅
                for ch_idx in ch_indices:
                    ch_name = ch_names[ch_idx]
                    
                    # 对每个epoch计算平均振幅
                    for epoch_idx in range(data.shape[0]):
                        # 计算时间窗口内的平均振幅
                        mean_amp = np.mean(data[epoch_idx, ch_idx, time_mask])
                        
                        # 添加特征
                        all_features.append({
                            'subject_id': subject_ids[epoch_idx],
                            'channel': ch_name,
                            'window_start': window_start,
                            'window_end': window_end,
                            'window': f"{window_start}-{window_end}s",
                            'epoch_index': epoch_idx,
                            'mean_amplitude': mean_amp,
                            'stage': stage
                        })
    
    # 转换为DataFrame
    features_df = pd.DataFrame(all_features)
    
    # 确保subject_id是字符串类型
    features_df['subject_id'] = features_df['subject_id'].astype(str)
    
    # 添加条件列（rest/test）
    features_df['condition'] = features_df['stage'].apply(
        lambda x: 'rest' if 'rest' in x else 'test' if 'test' in x else 'other'
    )
    
    print(f"共提取{len(features_df)}个特征")
    
    return features_df

def aggregate_hep_features(features_df):
    """
    聚合HEP特征
    
    参数:
    features_df (pd.DataFrame): HEP特征数据
    
    返回:
    pd.DataFrame: 聚合后的HEP特征数据
    """
    print("聚合HEP特征...")
    
    # 按被试ID、条件、通道和时间窗口聚合数据
    agg_features = features_df.groupby(
        ['subject_id', 'condition', 'channel', 'window_start', 'window_end', 'window']
    )['mean_amplitude'].mean().reset_index()
    
    print(f"聚合后共{len(agg_features)}个特征")
    
    return agg_features

def calculate_rest_test_difference(agg_features):
    """
    计算静息态与刺激态的差异
    
    参数:
    agg_features (pd.DataFrame): 聚合后的HEP特征数据
    
    返回:
    pd.DataFrame: 包含差异值的数据
    """
    print("计算静息态与刺激态的差异...")
    
    # 创建一个空的差异数据框
    diff_data = []
    
    # 获取唯一的被试ID、通道和时间窗口组合
    subjects = agg_features['subject_id'].unique()
    channels = agg_features['channel'].unique()
    windows = agg_features['window'].unique()
    
    # 对每个被试、通道和时间窗口，计算静息态与刺激态的差异
    for subject in subjects:
        for channel in channels:
            for window in windows:
                # 获取静息态和刺激态的数据
                rest_data = agg_features[(agg_features['subject_id'] == subject) & 
                                        (agg_features['channel'] == channel) & 
                                        (agg_features['window'] == window) & 
                                        (agg_features['condition'] == 'rest')]
                
                test_data = agg_features[(agg_features['subject_id'] == subject) & 
                                        (agg_features['channel'] == channel) & 
                                        (agg_features['window'] == window) & 
                                        (agg_features['condition'] == 'test')]
                
                # 如果两种条件都有数据，计算差异
                if len(rest_data) > 0 and len(test_data) > 0:
                    rest_amp = rest_data['mean_amplitude'].values[0]
                    test_amp = test_data['mean_amplitude'].values[0]
                    diff_amp = rest_amp - test_amp
                    
                    # 添加到差异数据框
                    diff_data.append({
                        'subject_id': subject,
                        'channel': channel,
                        'window': window,
                        'window_start': rest_data['window_start'].values[0],
                        'window_end': rest_data['window_end'].values[0],
                        'rest_amplitude': rest_amp,
                        'test_amplitude': test_amp,
                        'diff_amplitude': diff_amp
                    })
    
    # 转换为DataFrame
    diff_df = pd.DataFrame(diff_data)
    
    print(f"计算了{len(diff_df)}个差异值")
    
    return diff_df

def merge_with_ratings(diff_df, ratings_df):
    """
    将差异数据与主观量表数据合并
    
    参数:
    diff_df (pd.DataFrame): 差异数据
    ratings_df (pd.DataFrame): 主观量表数据
    
    返回:
    pd.DataFrame: 合并后的数据
    """
    print("合并差异数据与主观量表数据...")
    
    # 确保subject_id是字符串类型
    diff_df['subject_id'] = diff_df['subject_id'].astype(str)
    ratings_df['subject_id'] = ratings_df['subject_id'].astype(str)
    
    # 合并数据
    merged_df = pd.merge(diff_df, ratings_df, on='subject_id', how='inner')
    
    print(f"合并后共{len(merged_df)}条记录，{merged_df['subject_id'].nunique()}个被试")
    
    return merged_df

def save_processed_data(features_df, agg_features, diff_df, merged_df, output_dir=OUTPUT_DIR):
    """
    保存处理后的数据
    
    参数:
    features_df (pd.DataFrame): HEP特征数据
    agg_features (pd.DataFrame): 聚合后的HEP特征数据
    diff_df (pd.DataFrame): 差异数据
    merged_df (pd.DataFrame): 合并后的数据
    output_dir (str): 输出目录
    
    返回:
    dict: 保存的文件路径
    """
    print("保存处理后的数据...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存HEP特征数据
    features_path = os.path.join(output_dir, f"hep_features_{timestamp}.csv")
    features_df.to_csv(features_path, index=False)
    
    # 保存聚合后的HEP特征数据
    agg_features_path = os.path.join(output_dir, f"hep_agg_features_{timestamp}.csv")
    agg_features.to_csv(agg_features_path, index=False)
    
    # 保存差异数据
    diff_path = os.path.join(output_dir, f"hep_diff_{timestamp}.csv")
    diff_df.to_csv(diff_path, index=False)
    
    # 保存合并后的数据
    merged_path = os.path.join(output_dir, f"hep_merged_{timestamp}.csv")
    merged_df.to_csv(merged_path, index=False)
    
    print(f"数据已保存至: {output_dir}")
    
    return {
        'features': features_path,
        'agg_features': agg_features_path,
        'diff': diff_path,
        'merged': merged_path
    }

def main():
    """主函数"""
    # 创建输出目录
    create_output_directory()
    
    # 加载主观量表数据
    ratings_df = load_subjective_ratings()
    
    # 查找HEP文件
    hep_files = find_hep_files()
    
    # 提取HEP特征
    features_df = extract_hep_features(hep_files)
    
    # 聚合HEP特征
    agg_features = aggregate_hep_features(features_df)
    
    # 计算静息态与刺激态的差异
    diff_df = calculate_rest_test_difference(agg_features)
    
    # 合并差异数据与主观量表数据
    merged_df = merge_with_ratings(diff_df, ratings_df)
    
    # 保存处理后的数据
    file_paths = save_processed_data(features_df, agg_features, diff_df, merged_df)
    
    print("\n数据加载与预处理完成")
    print(f"处理后的数据已保存至: {OUTPUT_DIR}")

if __name__ == "__main__":
    main()
