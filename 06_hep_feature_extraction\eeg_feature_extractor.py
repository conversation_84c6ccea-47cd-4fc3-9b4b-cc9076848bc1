import numpy as np
from scipy.stats import skew, kurtosis
from scipy.signal import find_peaks
from scipy.fft import rfft, rfftfreq

def extract_eeg_features(signal, sfreq, times=None):
    """
    提取单通道单epoch信号的常见脑电特征
    signal: 1D array, 信号
    sfreq: 采样率
    times: 时间轴（可选），用于计算峰值延迟
    返回：dict
    """
    features = {}
    x = np.asarray(signal)
    N = len(x)
    t = times if times is not None else np.arange(N) / sfreq

    # 1. 均值
    features['mean'] = np.mean(x)
    # 2. 标准差
    features['std'] = np.std(x)
    # 3. 最大值
    features['max'] = np.max(x)
    # 4. 最小值
    features['min'] = np.min(x)
    # 5. 正峰值幅度
    features['pos_peak'] = np.max(x)
    # 6. 负峰值幅度
    features['neg_peak'] = np.min(x)
    # 7. 正峰值延迟
    features['pos_peak_latency'] = t[np.argmax(x)] if t is not None else np.argmax(x)
    # 8. 负峰值延迟
    features['neg_peak_latency'] = t[np.argmin(x)] if t is not None else np.argmin(x)
    # 9. 峰-峰值
    features['peak2peak'] = np.ptp(x)
    # 10. RMS
    features['rms'] = np.sqrt(np.mean(x**2))
    # 11. 能量
    features['energy'] = np.sum(x**2)
    # 12. 斜率
    features['slope'] = (x[-1] - x[0]) / (t[-1] - t[0]) if t is not None and t[-1] != t[0] else 0
    # 13. 偏度
    features['skew'] = skew(x)
    # 14. 峰度
    features['kurtosis'] = kurtosis(x)

    # 15. 主频率、频谱质心
    freqs = rfftfreq(N, 1/sfreq)
    fft_vals = np.abs(rfft(x))
    if len(fft_vals) > 1:
        features['main_freq'] = freqs[np.argmax(fft_vals[1:])+1]  # 跳过直流分量
        features['spec_centroid'] = np.sum(freqs * fft_vals) / np.sum(fft_vals)
    else:
        features['main_freq'] = 0
        features['spec_centroid'] = 0

    # 16. Hjorth参数
    diff1 = np.diff(x)
    diff2 = np.diff(diff1)
    var_zero = np.var(x)
    var_d1 = np.var(diff1)
    var_d2 = np.var(diff2)
    features['hjorth_activity'] = var_zero
    features['hjorth_mobility'] = np.sqrt(var_d1 / var_zero) if var_zero > 0 else 0
    features['hjorth_complexity'] = (np.sqrt(var_d2 / var_d1) / features['hjorth_mobility']) if var_d1 > 0 and features['hjorth_mobility'] > 0 else 0

    return features 