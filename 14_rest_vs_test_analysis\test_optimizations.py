#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HEP滤波优化测试脚本
测试新增的科学优化功能
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_filter_configurations():
    """测试滤波配置"""
    print("=" * 50)
    print("测试滤波配置")
    print("=" * 50)
    
    # 从主脚本导入配置
    try:
        from main_script import FILTER_CONFIG, QUALITY_STANDARDS, RR_INTERVAL_LIMITS
        print("✅ 成功导入配置")
        
        print("\n滤波配置:")
        for name, params in FILTER_CONFIG.items():
            print(f"  {name}: {params['low']}-{params['high']} Hz")
        
        print("\n质量控制标准:")
        for key, value in QUALITY_STANDARDS.items():
            print(f"  {key}: {value}")
            
        print("\n心跳周期限制:")
        for key, value in RR_INTERVAL_LIMITS.items():
            print(f"  {key}: {value}")
            
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_synthetic_hep_data():
    """测试合成HEP数据的滤波效果"""
    print("\n" + "=" * 50)
    print("测试合成HEP数据的滤波效果")
    print("=" * 50)
    
    # 创建合成HEP信号
    sampling_freq = 500  # Hz
    duration = 1.8  # 秒 (-800ms到1000ms)
    times = np.linspace(-0.8, 1.0, int(duration * sampling_freq))
    
    # 创建理想的HEP信号
    hep_signal = np.zeros_like(times)
    
    # 添加HEP成分 (200-300ms窗口)
    hep_mask = (times >= 0.2) & (times <= 0.3)
    hep_signal[hep_mask] = 2.0 * np.sin(2 * np.pi * 10 * times[hep_mask]) * np.exp(-5 * (times[hep_mask] - 0.25)**2)
    
    # 添加噪声
    noise_levels = [0.5, 1.0, 2.0, 5.0]  # μV
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    fig.suptitle('合成HEP数据滤波效果测试', fontsize=14, fontweight='bold')
    
    for i, noise_level in enumerate(noise_levels):
        ax = axes[i//2, i%2]
        
        # 添加噪声
        noisy_signal = hep_signal + np.random.normal(0, noise_level * 1e-6, len(times))
        
        # 应用不同滤波
        from scipy.signal import butter, filtfilt
        
        # 原始滤波 (0.1-75Hz)
        nyquist = sampling_freq / 2
        low_orig = 0.1 / nyquist
        high_orig = 75 / nyquist
        b_orig, a_orig = butter(4, [low_orig, high_orig], btype='band')
        filtered_orig = filtfilt(b_orig, a_orig, noisy_signal)
        
        # 优化滤波 (0.5-45Hz)
        low_opt = 0.5 / nyquist
        high_opt = 45 / nyquist
        b_opt, a_opt = butter(4, [low_opt, high_opt], btype='band')
        filtered_opt = filtfilt(b_opt, a_opt, noisy_signal)
        
        # 绘图
        ax.plot(times * 1000, hep_signal * 1e6, 'k-', linewidth=2, label='理想信号', alpha=0.8)
        ax.plot(times * 1000, filtered_orig * 1e6, 'r-', linewidth=1, label='原始滤波(0.1-75Hz)', alpha=0.7)
        ax.plot(times * 1000, filtered_opt * 1e6, 'b-', linewidth=1, label='优化滤波(0.5-45Hz)', alpha=0.7)
        
        ax.set_title(f'噪声水平: {noise_level}μV')
        ax.set_xlabel('时间 (ms)')
        ax.set_ylabel('幅值 (μV)')
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=8)
        ax.set_xlim(-800, 1000)
        
        # 计算信噪比
        baseline_mask = (times >= -0.2) & (times <= 0.0)
        signal_mask = (times >= 0.2) & (times <= 0.3)
        
        noise_orig = np.std(filtered_orig[baseline_mask]) * 1e6
        signal_orig = np.max(np.abs(filtered_orig[signal_mask])) * 1e6
        snr_orig = signal_orig / noise_orig if noise_orig > 0 else 0
        
        noise_opt = np.std(filtered_opt[baseline_mask]) * 1e6
        signal_opt = np.max(np.abs(filtered_opt[signal_mask])) * 1e6
        snr_opt = signal_opt / noise_opt if noise_opt > 0 else 0
        
        print(f"噪声水平 {noise_level}μV:")
        print(f"  原始滤波 SNR: {snr_orig:.2f}")
        print(f"  优化滤波 SNR: {snr_opt:.2f}")
        print(f"  改善程度: {((snr_opt - snr_orig) / snr_orig * 100):.1f}%")
    
    plt.tight_layout()
    plt.show()
    
    return True

def test_quality_metrics():
    """测试质量指标计算"""
    print("\n" + "=" * 50)
    print("测试质量指标计算")
    print("=" * 50)
    
    # 创建测试数据
    sampling_freq = 500
    times = np.linspace(-0.8, 1.0, int(1.8 * sampling_freq))
    
    # 测试不同质量的信号
    test_cases = {
        '高质量信号': {
            'baseline_std': 0.5,  # μV
            'hep_amplitude': 3.0,  # μV
            'gradient_noise': 0.1
        },
        '中等质量信号': {
            'baseline_std': 1.5,  # μV
            'hep_amplitude': 1.5,  # μV
            'gradient_noise': 0.3
        },
        '低质量信号': {
            'baseline_std': 3.0,  # μV
            'hep_amplitude': 0.3,  # μV
            'gradient_noise': 0.8
        }
    }
    
    for case_name, params in test_cases.items():
        print(f"\n{case_name}:")
        
        # 创建信号
        signal = np.zeros_like(times)
        
        # 添加HEP成分
        hep_mask = (times >= 0.2) & (times <= 0.3)
        signal[hep_mask] = params['hep_amplitude'] * 1e-6 * np.sin(2 * np.pi * 10 * times[hep_mask])
        
        # 添加基线噪声
        baseline_mask = (times >= -0.2) & (times <= 0.0)
        signal += np.random.normal(0, params['baseline_std'] * 1e-6, len(times))
        
        # 添加梯度噪声
        gradient_noise = np.random.normal(0, params['gradient_noise'] * 1e-6, len(times))
        signal += np.cumsum(gradient_noise) * 0.01
        
        # 计算质量指标
        baseline_std_calc = np.std(signal[baseline_mask]) * 1e6
        hep_amplitude_calc = np.max(np.abs(signal[hep_mask])) * 1e6
        gradient_calc = np.max(np.abs(np.gradient(signal))) * 1e6
        snr_calc = hep_amplitude_calc / baseline_std_calc if baseline_std_calc > 0 else 0
        
        print(f"  基线标准差: {baseline_std_calc:.2f}μV (目标: {params['baseline_std']:.2f}μV)")
        print(f"  HEP幅度: {hep_amplitude_calc:.2f}μV (目标: {params['hep_amplitude']:.2f}μV)")
        print(f"  最大梯度: {gradient_calc:.2f}μV/点")
        print(f"  信噪比: {snr_calc:.2f}")
        
        # 质量评估
        quality_score = 0
        if baseline_std_calc < 2.0:
            quality_score += 1
        if hep_amplitude_calc > 0.5:
            quality_score += 1
        if gradient_calc < 3.0:
            quality_score += 1
        if snr_calc > 2.0:
            quality_score += 1
            
        quality_level = ['差', '较差', '中等', '良好', '优秀'][quality_score]
        print(f"  质量评级: {quality_level} ({quality_score}/4)")

def main():
    """主测试函数"""
    print("HEP滤波优化功能测试")
    print("=" * 60)
    
    # 测试1: 配置导入
    config_ok = test_filter_configurations()
    
    # 测试2: 合成数据滤波
    if config_ok:
        synthetic_ok = test_synthetic_hep_data()
    
    # 测试3: 质量指标
    quality_ok = test_quality_metrics()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    if config_ok:
        print("✅ 配置测试通过")
    else:
        print("❌ 配置测试失败")
    
    print("✅ 合成数据测试完成")
    print("✅ 质量指标测试完成")
    
    print("\n建议:")
    print("1. 对于高噪声数据，使用 'smooth' 或 'ultra_smooth' 滤波配置")
    print("2. 监控基线标准差，确保 < 2μV")
    print("3. 确保HEP幅度 > 0.5μV")
    print("4. 信噪比应 > 2.0 以获得可靠结果")

if __name__ == "__main__":
    main()
