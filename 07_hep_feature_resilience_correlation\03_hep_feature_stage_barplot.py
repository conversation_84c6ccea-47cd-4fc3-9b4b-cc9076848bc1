import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib as mpl
from scipy.stats import ttest_rel

# 设置全局字体和字号
mpl.rcParams['font.sans-serif'] = ['LXGW Wenkai']
mpl.rcParams['axes.unicode_minus'] = False
mpl.rcParams['font.size'] = 10
mpl.rcParams['figure.facecolor'] = 'white'
mpl.rcParams['axes.facecolor'] = 'white'

# 读取HEP特征表
feature_dir = r"D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/features"
files = [f for f in os.listdir(feature_dir) if f.startswith('central_eeg_features_0.5_0.6') and f.endswith('.xlsx')]
files.sort(key=lambda x: os.path.getmtime(os.path.join(feature_dir, x)), reverse=True)
file = os.path.join(feature_dir, files[0])
hep_df = pd.read_excel(file)

# 只保留静息态1和刺激态3
target_stages = ['rest1', 'test3']
stage_labels = ['静息态1', '刺激态3']
hep_df = hep_df[hep_df['stage'].isin(target_stages)]

# 针对每个被试-阶段，中央区所有通道特征取均值
features = ['neg_peak_latency', 'rms']
agg_rows = []
for subject in hep_df['subject_id'].unique():
    for stage in target_stages:
        subdf = hep_df[(hep_df['subject_id']==subject) & (hep_df['stage']==stage)]
        if subdf.empty:
            continue
        row = {'subject_id': subject, 'stage': stage}
        for feat in features:
            row[feat] = subdf[feat].mean()
        agg_rows.append(row)
agg_df = pd.DataFrame(agg_rows)

# 只保留两个阶段都不缺失的被试
good_subjects = agg_df.groupby('subject_id').filter(lambda x: len(x)==2)['subject_id'].unique()
agg_df = agg_df[agg_df['subject_id'].isin(good_subjects)]

out_dir = r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/hep_feature_stage_barplot'
os.makedirs(out_dir, exist_ok=True)

for feat in features:
    data = agg_df.pivot(index='subject_id', columns='stage', values=feat)
    means = data.mean()
    stds = data.std()
    # 配对t检验
    tval, pval = ttest_rel(data['rest1'], data['test3'])
    # 绘图
    fig, ax = plt.subplots(figsize=(5, 6))
    x = np.arange(2)
    colors = ['#1f77b4', '#d62728']
    ax.bar(x, means, yerr=stds, capsize=6, color=colors, alpha=0.7, width=0.6, edgecolor='black', linewidth=1.2)
    # 叠加散点
    for i, stage in enumerate(['rest1', 'test3']):
        ax.scatter(np.full(data.shape[0], x[i]), data[stage], color=colors[i], alpha=0.7, s=60, edgecolor='black', linewidth=0.8)
    # 连线
    for i in range(data.shape[0]):
        ax.plot(x, data.iloc[i], color='gray', alpha=0.4, linewidth=1)
    ax.set_xticks(x)
    ax.set_xticklabels(stage_labels, fontsize=12)
    ax.set_ylabel(feat, fontsize=14)
    ax.set_title(f'{feat}在静息态1与刺激态3的均值±标准差', fontsize=16, fontweight='bold', fontname='LXGW Wenkai')
    # 标注P值
    y_max = (means + stds).max()
    ax.plot([0, 1], [y_max*1.05]*2, color='black', linewidth=1.2)
    ax.text(0.5, y_max*1.08, f'p = {pval:.4f}', ha='center', va='bottom', fontsize=12, fontweight='bold')
    plt.tight_layout()
    plt.savefig(os.path.join(out_dir, f'hep_{feat}_rest1_vs_test3_barplot.png'), dpi=600, bbox_inches='tight')
    plt.savefig(os.path.join(out_dir, f'hep_{feat}_rest1_vs_test3_barplot.svg'), format='svg', bbox_inches='tight')
    plt.close() 