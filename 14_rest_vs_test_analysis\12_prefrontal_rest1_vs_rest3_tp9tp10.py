#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前额叶区域HEP分析：两个静息阶段对比分析 (rest1 vs rest3) - 双侧乳突参考版本

本脚本分析：
1. 两个静息阶段的HEP时间序列对比（rest1和rest3）
2. 静息阶段间的差异波形分析（rest3 - rest1）
3. 按照指定脑区分组的区域拟合分析

数据来源：双侧乳突参考(TP9/TP10)的HEP数据
时间窗口：-800ms到1000ms
基线校正：-200ms到0ms (统一基线矫正)
滤波设置：0.1Hz-75Hz (可调整为0.5Hz-45Hz)

图表输出：
- 简化的2行2列布局，每个脑区一个子图
- 每个子图显示3条线：rest1、rest3、rest3-rest1差异
- 移除所有时间标记和R波标记，保持图表清洁
"""

import os
import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
import csv
from scipy.signal import find_peaks, butter, filtfilt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-HBA'  # 修正为当前工作目录
DATA_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', 'tp9tp10')
RESULT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'tp9tp10')

# 创建结果目录
os.makedirs(RESULT_DIR, exist_ok=True)

# 统一后缀
RESULT_SUFFIX = '_rest1_vs_rest3_simplified_tp9tp10'

# 定义脑区电极分组 - 按照用户要求的4个脑区
BRAIN_REGIONS = {
    '左前额叶区域': ['Fp1', 'AF3', 'AF7', 'F3', 'F5'],
    '右前额叶区域': ['Fp2', 'AF4', 'AF8', 'F4', 'F6'],
    '额中央区域': ['Fpz', 'AFz', 'Fz', 'FCz'],
    '中央区域': ['Cz', 'CPz', 'Pz']
}

# 时间窗口设置 - 按照用户要求调整
VIS_WINDOW = (-0.8, 1.0)       # 可视化窗口 -800ms到1000ms
BASELINE_WINDOW = (-0.2, 0.0)  # 基线矫正窗口 -200ms到0ms
CLASSIC_WINDOW = (0.2, 0.3)    # 经典HEP窗口 200-300ms

# 滤波参数设置 - 科学优化版本
FILTER_CONFIG = {
    'original': {'low': 0.1, 'high': 75},     # 原始滤波（保留用于对比）
    'optimized': {'low': 0.5, 'high': 45},    # 第一级优化滤波 - 减少噪声
    'smooth': {'low': 1.0, 'high': 30},       # 第二级优化滤波 - 进一步收窄
    'ultra_smooth': {'low': 0.5, 'high': 20}  # 超平滑滤波（极度抖动时使用）
}

# 当前使用的滤波参数（科学优化设置）
CURRENT_FILTER = 'optimized'  # 'original', 'optimized', 'smooth', 'ultra_smooth'

# 心跳周期质量控制参数
RR_INTERVAL_LIMITS = {
    'min_rr': 0.4,  # 最小R-R间期（秒）- 150 BPM
    'max_rr': 1.5,  # 最大R-R间期（秒）- 40 BPM
    'min_cycles': 100  # 每个条件最少心跳周期数
}

# 数据质量控制标准
QUALITY_STANDARDS = {
    'baseline_std_threshold': 2.0,  # 基线标准差阈值（μV）
    'hep_min_amplitude': 0.5,       # HEP最小幅度（μV）
    'max_gradient_threshold': 3.0,  # 最大梯度阈值（μV/采样点）
    'smoothing_window': 5           # 移动平均窗口大小（采样点）
}

def load_hep_data(h5_path):
    """加载双侧乳突参考HEP数据"""
    print(f"正在加载数据: {os.path.basename(h5_path)}")

    with h5py.File(h5_path, 'r') as f:
        data = f['data'][:]
        ch_names = [ch.decode('utf-8').strip() if isinstance(ch, bytes) else str(ch).strip()
                   for ch in f['ch_names'][:]]
        times = f['times'][:]

        if 'subject_ids' in f:
            subject_ids = f['subject_ids'][:]
            subject_ids = [s.decode('utf-8') if isinstance(s, bytes) else str(s)
                          for s in subject_ids]
        else:
            subject_ids = ['unknown'] * data.shape[0]

    sampling_freq = 1 / (times[1] - times[0])
    print(f"  数据形状: {data.shape}")
    print(f"  采样频率: {sampling_freq:.1f} Hz")
    print(f"  时间范围: {times[0]*1000:.1f} 到 {times[-1]*1000:.1f} ms")
    print(f"  通道数: {len(ch_names)}")

    return data, ch_names, times, subject_ids, sampling_freq

def apply_bandpass_filter(data, sampling_freq, low_freq, high_freq):
    """应用带通滤波器"""
    print(f"  应用带通滤波: {low_freq}-{high_freq} Hz")

    # 设计Butterworth滤波器
    nyquist = sampling_freq / 2
    low_norm = low_freq / nyquist
    high_norm = high_freq / nyquist

    # 确保归一化频率在有效范围内
    low_norm = max(low_norm, 0.001)
    high_norm = min(high_norm, 0.999)

    b, a = butter(4, [low_norm, high_norm], btype='band')

    # 对每个epoch和每个通道进行滤波
    filtered_data = np.zeros_like(data)
    for i in range(data.shape[0]):  # 遍历每个epoch
        for j in range(data.shape[1]):  # 遍历每个通道
            filtered_data[i, j, :] = filtfilt(b, a, data[i, j, :])

    return filtered_data

def apply_moving_average_smoothing(data, window_size=None):
    """应用移动平均平滑"""
    if window_size is None:
        window_size = QUALITY_STANDARDS['smoothing_window']

    print(f"  应用移动平均平滑，窗口大小: {window_size}")

    smoothed_data = np.zeros_like(data)

    # 对每个epoch和每个通道应用移动平均
    for i in range(data.shape[0]):  # 遍历每个epoch
        for j in range(data.shape[1]):  # 遍历每个通道
            signal = data[i, j, :]
            # 使用numpy的卷积实现移动平均
            kernel = np.ones(window_size) / window_size
            # 使用'same'模式保持原始长度
            smoothed_signal = np.convolve(signal, kernel, mode='same')
            smoothed_data[i, j, :] = smoothed_signal

    return smoothed_data

def detect_and_remove_artifact_epochs(data, times, sampling_freq):
    """检测并移除伪迹epochs"""
    print("  检测并移除伪迹epochs...")

    n_epochs, n_channels, n_times = data.shape
    valid_epochs = []

    # 计算每个epoch的质量指标
    for i in range(n_epochs):
        epoch_data = data[i]

        # 1. 检查幅度异常
        max_amplitude = np.max(np.abs(epoch_data))
        if max_amplitude > 100e-6:  # 100μV阈值
            continue

        # 2. 检查基线稳定性
        baseline_mask = (times >= BASELINE_WINDOW[0]) & (times <= BASELINE_WINDOW[1])
        baseline_data = epoch_data[:, baseline_mask]
        baseline_std = np.mean(np.std(baseline_data, axis=1))

        if baseline_std > QUALITY_STANDARDS['baseline_std_threshold'] * 1e-6:
            continue

        # 3. 检查梯度异常（突变）
        gradients = np.gradient(epoch_data, axis=1)
        max_gradient = np.max(np.abs(gradients))

        if max_gradient > QUALITY_STANDARDS['max_gradient_threshold'] * 1e-6:
            continue

        valid_epochs.append(i)

    if len(valid_epochs) < RR_INTERVAL_LIMITS['min_cycles']:
        print(f"    警告: 有效epochs数量({len(valid_epochs)})少于最小要求({RR_INTERVAL_LIMITS['min_cycles']})")

    print(f"    保留 {len(valid_epochs)}/{n_epochs} 个有效epochs")

    return data[valid_epochs] if valid_epochs else data

def apply_unified_baseline_correction(rest1_data, rest3_data, times, baseline_window=BASELINE_WINDOW):
    """对两个静息阶段数据进行统一基线矫正"""
    print("执行统一基线矫正...")
    
    # 找到基线窗口的索引
    baseline_mask = (times >= baseline_window[0]) & (times <= baseline_window[1])
    print(f"  基线窗口: {baseline_window[0]*1000:.0f} 到 {baseline_window[1]*1000:.0f} ms")

    # 提取基线窗口的数据
    rest1_baseline = rest1_data[..., baseline_mask]
    rest3_baseline = rest3_data[..., baseline_mask]

    # 计算每个条件的基线均值
    rest1_baseline_mean = np.mean(rest1_baseline, axis=-1, keepdims=True)
    rest3_baseline_mean = np.mean(rest3_baseline, axis=-1, keepdims=True)
    
    # 合并所有基线数据计算全局基线
    all_baseline_values = np.concatenate([
        rest1_baseline_mean.reshape(-1, rest1_baseline_mean.shape[1]),
        rest3_baseline_mean.reshape(-1, rest3_baseline_mean.shape[1])
    ], axis=0)
    
    # 计算全局基线均值
    global_baseline_mean = np.mean(all_baseline_values, axis=0, keepdims=True)
    global_baseline_mean = global_baseline_mean[..., np.newaxis]
    
    # 应用统一基线矫正
    rest1_corrected = rest1_data - global_baseline_mean
    rest3_corrected = rest3_data - global_baseline_mean

    print(f"  统一基线矫正完成，全局基线均值形状: {global_baseline_mean.shape}")
    
    return rest1_corrected, rest3_corrected

def find_latest_files():
    """查找最新的两个静息阶段数据文件（rest1和rest3）"""
    rest1_files = [f for f in os.listdir(DATA_DIR) if f.startswith('rest1_raw_epochs_') and f.endswith('.h5')]
    rest3_files = [f for f in os.listdir(DATA_DIR) if f.startswith('rest3_raw_epochs_') and f.endswith('.h5')]

    if not rest1_files or not rest3_files:
        missing = []
        if not rest1_files: missing.append('rest1')
        if not rest3_files: missing.append('rest3')
        raise FileNotFoundError(f"未找到以下数据文件: {', '.join(missing)}")

    # 按时间戳排序，取最新的
    rest1_files.sort()
    rest3_files.sort()

    rest1_file = os.path.join(DATA_DIR, rest1_files[-1])
    rest3_file = os.path.join(DATA_DIR, rest3_files[-1])

    print(f"使用的数据文件:")
    print(f"  Rest1: {rest1_files[-1]}")
    print(f"  Rest3: {rest3_files[-1]}")

    return rest1_file, rest3_file

def extract_region_data(data, ch_names, subject_ids, region_electrodes):
    """提取指定区域的电极数据"""
    valid_electrodes = [e for e in region_electrodes if e in ch_names]
    print(f"    实际存在的电极: {valid_electrodes}")

    if not valid_electrodes:
        return None, []

    ch_indices = [ch_names.index(e) for e in valid_electrodes]

    subject_data = {}
    unique_subjects = sorted(set(subject_ids))

    for subj in unique_subjects:
        subj_indices = [i for i, s in enumerate(subject_ids) if s == subj]
        if subj_indices:
            subj_data = data[subj_indices][:, ch_indices, :]
            subject_data[subj] = subj_data

    return subject_data, valid_electrodes

def calculate_region_average(subject_data):
    """计算区域平均波形"""
    if subject_data is None:
        return None

    all_data = []
    for subj_data in subject_data.values():
        # 对每个被试的epochs求平均，然后对电极求平均
        subj_avg = np.mean(subj_data, axis=0)  # 对epochs求平均
        region_avg = np.mean(subj_avg, axis=0)  # 对电极求平均
        all_data.append(region_avg)

    # 对所有被试求平均，得到区域内平滑的平均曲线
    grand_avg = np.mean(all_data, axis=0)
    return grand_avg

def load_and_process_data():
    """加载并处理两个静息阶段的数据（rest1和rest3）- 增强版预处理"""
    print("\n" + "=" * 50)
    print("数据加载与科学预处理")
    print("=" * 50)

    # 查找并加载最新的数据文件
    rest1_file, rest3_file = find_latest_files()

    # 加载原始数据
    rest1_data, ch_names, times, rest1_subject_ids, sampling_freq = load_hep_data(rest1_file)
    rest3_data, _, _, rest3_subject_ids, _ = load_hep_data(rest3_file)

    # 获取滤波参数
    filter_params = FILTER_CONFIG[CURRENT_FILTER]
    print(f"\n使用滤波配置: {CURRENT_FILTER}")
    print(f"滤波参数: {filter_params['low']}-{filter_params['high']} Hz")

    # 步骤1: 伪迹检测和移除
    print("\n步骤1: 伪迹检测和移除")
    rest1_data = detect_and_remove_artifact_epochs(rest1_data, times, sampling_freq)
    rest3_data = detect_and_remove_artifact_epochs(rest3_data, times, sampling_freq)

    # 步骤2: 应用滤波器
    print("\n步骤2: 应用带通滤波")
    rest1_data = apply_bandpass_filter(rest1_data, sampling_freq,
                                     filter_params['low'], filter_params['high'])
    rest3_data = apply_bandpass_filter(rest3_data, sampling_freq,
                                     filter_params['low'], filter_params['high'])

    # 步骤3: 统一基线矫正
    print("\n步骤3: 统一基线矫正")
    rest1_data, rest3_data = apply_unified_baseline_correction(
        rest1_data, rest3_data, times)

    # 步骤4: 信号平滑（移动平均）
    print("\n步骤4: 信号平滑处理")
    rest1_data = apply_moving_average_smoothing(rest1_data)
    rest3_data = apply_moving_average_smoothing(rest3_data)

    # 输出处理后的数据统计
    print(f"\n预处理完成:")
    print(f"  Rest1数据: {rest1_data.shape[0]} epochs")
    print(f"  Rest3数据: {rest3_data.shape[0]} epochs")
    print(f"  采样频率: {sampling_freq:.1f} Hz")
    print(f"  时间窗口: {times[0]*1000:.0f} 到 {times[-1]*1000:.0f} ms")

    return (rest1_data, rest3_data, ch_names, times,
            rest1_subject_ids, rest3_subject_ids, sampling_freq)

def create_region_analysis():
    """创建脑区分析（简化为rest1和rest3对比）"""
    print("\n" + "=" * 50)
    print("脑区数据分析")
    print("=" * 50)
    
    # 加载并处理数据
    data_results = load_and_process_data()
    (rest1_data, rest3_data, ch_names, times, 
     rest1_subject_ids, rest3_subject_ids, sampling_freq) = data_results
    
    # 分析各脑区
    region_results = {}

    for region_name, electrodes in BRAIN_REGIONS.items():
        print(f"\n分析 {region_name}...")

        # 提取区域数据
        rest1_region, valid_electrodes = extract_region_data(
            rest1_data, ch_names, rest1_subject_ids, electrodes)
        rest3_region, _ = extract_region_data(
            rest3_data, ch_names, rest3_subject_ids, electrodes)

        if rest1_region is None or rest3_region is None:
            print(f"  {region_name} 区域数据不可用")
            continue

        # 计算区域平均波形
        rest1_avg = calculate_region_average(rest1_region)
        rest3_avg = calculate_region_average(rest3_region)

        if rest1_avg is None or rest3_avg is None:
            print(f"  {region_name} 区域平均计算失败")
            continue

        print(f"  成功计算区域平均波形")

        region_results[region_name] = {
            'electrodes': valid_electrodes,
            'rest1_avg': rest1_avg,
            'rest3_avg': rest3_avg
        }

    return region_results, times

def create_dual_visualization(region_results, times):
    """创建优化的简化可视化 - 2:1宽高比、细线条和高对比度颜色方案"""
    print("\n" + "=" * 50)
    print("创建优化可视化图表")
    print("=" * 50)
    
    # 获取当前滤波参数信息
    filter_params = FILTER_CONFIG[CURRENT_FILTER]
    filter_info = f"{filter_params['low']}-{filter_params['high']}Hz"
    
    # 创建2:1宽高比的图形（横轴:纵轴 = 2:1）
    # 使用16x8英寸确保充分的时间轴显示空间（-800ms到+1000ms）
    fig = plt.figure(figsize=(16, 8))
    
    # 设置总标题，包含滤波参数信息
    fig.suptitle(f'HEP静息阶段分析：Rest1 vs Rest3 对比分析\n'
                f'双侧乳突参考 (TP9/TP10) | 滤波: {filter_info}', 
                 fontsize=14, fontweight='bold', y=0.95)
    
    # 创建子图布局：2行2列，每个脑区一个子图
    gs = fig.add_gridspec(2, 2, hspace=0.35, wspace=0.3)
    
    # 高对比度颜色方案 - 确保打印和灰度显示时可清晰区分
    phase_colors = {
        'Rest1': '#000080',   # 深蓝色 - 原始基线（海军蓝）
        'Rest3': '#B8860B'    # 深黄色 - 对比数据（暗金色）
    }
    diff_color = '#8B0000'    # 深红色 - 差异波形（暗红色）
    
    # 线型样式配置 - 增强区分度
    line_styles = {
        'Rest1': '-',         # 实线
        'Rest3': '-',         # 实线  
        'Diff': '-'           # 实线（保持实线，用颜色区分）
    }
    
    # 为每个脑区创建子图
    region_names = list(region_results.keys())
    
    for i, region_name in enumerate(region_names[:4]):  # 最多显示4个脑区
        result = region_results[region_name]

        rest1_avg = result['rest1_avg']
        rest3_avg = result['rest3_avg']
        
        # 计算差异波形（rest3 - rest1）
        diff_rest3_rest1 = rest3_avg - rest1_avg
        
        # 子图位置
        row = i // 2
        col = i % 2
        ax = fig.add_subplot(gs[row, col])
        
        # 绘制三条优化的波形线 - 使用更细的线条（linewidth=1.0）
        ax.plot(times * 1000, rest1_avg * 1e6, 
                color=phase_colors['Rest1'], linewidth=1.0, 
                linestyle=line_styles['Rest1'], label='Rest1', alpha=0.95)
        ax.plot(times * 1000, rest3_avg * 1e6, 
                color=phase_colors['Rest3'], linewidth=1.0, 
                linestyle=line_styles['Rest3'], label='Rest3', alpha=0.95)
        ax.plot(times * 1000, diff_rest3_rest1 * 1e6, 
                color=diff_color, linewidth=1.0, 
                linestyle=line_styles['Diff'], label='Rest3 - Rest1', alpha=0.9)
        
        # 设置坐标轴和标签 - 确保时间窗口完整显示-800ms到+1000ms
        ax.set_xlim(VIS_WINDOW[0]*1000, VIS_WINDOW[1]*1000)
        ax.set_xlabel('时间 (ms)', fontsize=10)
        ax.set_ylabel('幅值 (μV)', fontsize=10)
        ax.set_title(f'{region_name}\n滤波: {filter_info}', fontsize=11, fontweight='bold')
        
        # 保持简洁的网格
        ax.grid(True, alpha=0.3, linewidth=0.5)
        
        # 只保留零线，移除其他所有标记
        ax.axhline(0, color='black', linewidth=0.5, alpha=0.6)
        
        # 优化图例位置 - 避免遮挡重要数据区域
        # 根据数据分布选择最佳图例位置
        legend_loc = 'upper right'
        
        # 检查右上角区域是否有较大的数据值，如果有则调整图例位置
        time_mask_right = times > 0.5  # 右半部分时间
        max_val_right = max(
            np.max(np.abs(rest1_avg[time_mask_right])),
            np.max(np.abs(rest3_avg[time_mask_right])),
            np.max(np.abs(diff_rest3_rest1[time_mask_right]))
        ) * 1e6
        
        # 如果右上角数据值较大，将图例移到左上角
        if max_val_right > 2.0:  # 如果右侧峰值超过2μV
            legend_loc = 'upper left'
        
        # 创建清晰的图例，显示颜色和线型对应关系
        legend = ax.legend(fontsize=9, loc=legend_loc, framealpha=0.95, 
                          fancybox=True, shadow=True, edgecolor='black')
        
        # 电极信息标注 - 调整位置避免与图例冲突
        electrode_text = f"电极: {', '.join(result['electrodes'])}"
        text_x = 0.02 if legend_loc == 'upper right' else 0.98
        text_ha = 'left' if legend_loc == 'upper right' else 'right'
        
        ax.text(text_x, 0.02, electrode_text, transform=ax.transAxes, 
                fontsize=8, verticalalignment='bottom', horizontalalignment=text_ha,
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.9, 
                         edgecolor='gray'))
    
    # 调整布局，确保2:1的比例得到保持
    plt.tight_layout()
    
    # 保存图片 - 文件名包含滤波参数
    filter_suffix = f"_{filter_params['low']:.1f}_{filter_params['high']:.0f}Hz"
    output_file = os.path.join(RESULT_DIR, f'hep_filtered_analysis{filter_suffix}{RESULT_SUFFIX}_2to1ratio.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"2:1比例滤波优化图表已保存: {output_file}")
    
    # 输出颜色方案信息以便用户了解
    print(f"\n颜色方案说明:")
    print(f"  Rest1: 深蓝色 ({phase_colors['Rest1']}) - 实线")
    print(f"  Rest3: 深黄色 ({phase_colors['Rest3']}) - 实线") 
    print(f"  差异: 深红色 ({diff_color}) - 实线")
    print(f"  线条粗细: {1.0}px (相比默认更细)")
    print(f"  图表比例: 2:1 (16×8英寸)")
    print(f"  时间窗口: {VIS_WINDOW[0]*1000:.0f}ms 到 {VIS_WINDOW[1]*1000:.0f}ms")

    plt.show()

    return fig

def check_data_quality(region_results, times):
    """增强版数据质量检查 - 科学标准"""
    print("\n" + "=" * 50)
    print("科学数据质量检查")
    print("=" * 50)

    quality_issues = []
    quality_metrics = {}

    for region_name, result in region_results.items():
        print(f"\n检查 {region_name}:")

        rest1_avg = result['rest1_avg']
        rest3_avg = result['rest3_avg']

        region_metrics = {}

        # 1. 基线稳定性检查（-200ms到0ms）
        baseline_mask = (times >= BASELINE_WINDOW[0]) & (times <= BASELINE_WINDOW[1])

        rest1_baseline_std = np.std(rest1_avg[baseline_mask]) * 1e6  # 转换为μV
        rest3_baseline_std = np.std(rest3_avg[baseline_mask]) * 1e6

        baseline_variation = max(rest1_baseline_std, rest3_baseline_std)
        region_metrics['baseline_std'] = baseline_variation

        print(f"  基线期标准差: Rest1={rest1_baseline_std:.2f}μV, Rest3={rest3_baseline_std:.2f}μV")

        if baseline_variation > QUALITY_STANDARDS['baseline_std_threshold']:
            quality_issues.append(f"{region_name}: 基线期变异性过大 ({baseline_variation:.2f}μV > {QUALITY_STANDARDS['baseline_std_threshold']}μV)")

        # 2. HEP成分检查（200-300ms）
        hep_mask = (times >= CLASSIC_WINDOW[0]) & (times <= CLASSIC_WINDOW[1])

        rest1_hep_mean = np.mean(rest1_avg[hep_mask]) * 1e6
        rest3_hep_mean = np.mean(rest3_avg[hep_mask]) * 1e6

        hep_difference = abs(rest3_hep_mean - rest1_hep_mean)
        region_metrics['hep_amplitude'] = hep_difference

        print(f"  HEP窗口幅度: Rest1={rest1_hep_mean:.2f}μV, Rest3={rest3_hep_mean:.2f}μV")
        print(f"  HEP差异幅度: {hep_difference:.2f}μV")

        if hep_difference < QUALITY_STANDARDS['hep_min_amplitude']:
            quality_issues.append(f"{region_name}: HEP差异过小 ({hep_difference:.2f}μV < {QUALITY_STANDARDS['hep_min_amplitude']}μV)")

        # 3. 信号平滑度检查
        rest1_gradient = np.gradient(rest1_avg) * 1e6
        rest3_gradient = np.gradient(rest3_avg) * 1e6

        max_gradient_rest1 = np.max(np.abs(rest1_gradient))
        max_gradient_rest3 = np.max(np.abs(rest3_gradient))
        max_gradient = max(max_gradient_rest1, max_gradient_rest3)

        region_metrics['max_gradient'] = max_gradient

        print(f"  信号梯度峰值: Rest1={max_gradient_rest1:.2f}μV/点, Rest3={max_gradient_rest3:.2f}μV/点")

        if max_gradient > QUALITY_STANDARDS['max_gradient_threshold']:
            quality_issues.append(f"{region_name}: 信号过于抖动 ({max_gradient:.2f}μV/点 > {QUALITY_STANDARDS['max_gradient_threshold']}μV/点)")

        # 4. 200ms后HEP成分可见性检查
        post_hep_mask = (times >= 0.2) & (times <= 0.6)  # 200-600ms窗口

        rest1_post_hep = rest1_avg[post_hep_mask] * 1e6
        rest3_post_hep = rest3_avg[post_hep_mask] * 1e6

        # 检查是否有明显的HEP成分
        rest1_hep_peak = np.max(np.abs(rest1_post_hep))
        rest3_hep_peak = np.max(np.abs(rest3_post_hep))

        region_metrics['hep_peak'] = max(rest1_hep_peak, rest3_hep_peak)

        print(f"  200ms后HEP峰值: Rest1={rest1_hep_peak:.2f}μV, Rest3={rest3_hep_peak:.2f}μV")

        if max(rest1_hep_peak, rest3_hep_peak) < 1.0:  # 1μV阈值
            quality_issues.append(f"{region_name}: 200ms后HEP成分不明显 (峰值 < 1.0μV)")

        # 5. 信噪比估计
        # 使用基线期作为噪声估计，HEP窗口作为信号估计
        noise_level = baseline_variation
        signal_level = hep_difference

        if noise_level > 0:
            snr = signal_level / noise_level
            region_metrics['snr'] = snr
            print(f"  估计信噪比: {snr:.2f}")

            if snr < 2.0:  # SNR < 2认为质量较差
                quality_issues.append(f"{region_name}: 信噪比过低 (SNR={snr:.2f} < 2.0)")

        quality_metrics[region_name] = region_metrics

    # 输出质量检查总结
    print(f"\n" + "=" * 30)
    print("质量检查总结")
    print("=" * 30)

    if quality_issues:
        print(f"发现 {len(quality_issues)} 个质量问题:")
        for issue in quality_issues:
            print(f"  ❌ {issue}")

        # 提供具体的优化建议
        print(f"\n优化建议:")
        if any("抖动" in issue for issue in quality_issues):
            print(f"  📊 信号抖动问题: 建议使用 CURRENT_FILTER = 'smooth' (1.0-30Hz)")
        if any("基线期变异性过大" in issue for issue in quality_issues):
            print(f"  📈 基线不稳定: 建议检查伪迹去除参数或使用更严格的滤波")
        if any("HEP差异过小" in issue for issue in quality_issues):
            print(f"  📉 HEP信号微弱: 建议增加epochs数量或检查数据预处理")
        if any("信噪比过低" in issue for issue in quality_issues):
            print(f"  🔊 信噪比低: 建议使用 CURRENT_FILTER = 'ultra_smooth' (0.5-20Hz)")
    else:
        print("✅ 数据质量检查通过，所有指标符合科学标准")

    return quality_issues, quality_metrics

def save_comprehensive_results(region_results, times, quality_issues, quality_metrics):
    """保存完整的分析结果 - 包含质量指标"""
    print("\n" + "=" * 50)
    print("保存分析结果")
    print("=" * 50)

    # 保存区域统计摘要（增强版）
    summary_file = os.path.join(RESULT_DIR, f'analysis_summary{RESULT_SUFFIX}.csv')

    with open(summary_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow([
            '脑区', '电极数量', '电极列表', 'Rest1均值(μV)', 'Rest3均值(μV)',
            'Rest3-Rest1差值(μV)', '最大差异(μV)', '基线标准差(μV)', 'HEP峰值(μV)',
            '信噪比', '最大梯度(μV/点)', '滤波设置', '参考方式', '质量状态'
        ])

        for region_name, result in region_results.items():
            electrodes = result['electrodes']
            rest1_avg = result['rest1_avg']
            rest3_avg = result['rest3_avg']

            # 计算HEP窗口内的平均值
            hep_mask = (times >= CLASSIC_WINDOW[0]) & (times <= CLASSIC_WINDOW[1])

            rest1_hep_mean = np.mean(rest1_avg[hep_mask]) * 1e6  # 转换为μV
            rest3_hep_mean = np.mean(rest3_avg[hep_mask]) * 1e6

            # 计算差异
            diff_rest3_rest1 = rest3_hep_mean - rest1_hep_mean
            max_diff = abs(diff_rest3_rest1)

            # 获取质量指标
            metrics = quality_metrics.get(region_name, {})
            baseline_std = metrics.get('baseline_std', 0)
            hep_peak = metrics.get('hep_peak', 0)
            snr = metrics.get('snr', 0)
            max_gradient = metrics.get('max_gradient', 0)

            # 判断质量状态
            region_issues = [issue for issue in quality_issues if region_name in issue]
            quality_status = "良好" if not region_issues else f"问题({len(region_issues)}个)"

            filter_setting = f"{FILTER_CONFIG[CURRENT_FILTER]['low']}-{FILTER_CONFIG[CURRENT_FILTER]['high']}Hz"

            writer.writerow([
                region_name,
                len(electrodes),
                ', '.join(electrodes),
                f"{rest1_hep_mean:.2f}",
                f"{rest3_hep_mean:.2f}",
                f"{diff_rest3_rest1:.2f}",
                f"{max_diff:.2f}",
                f"{baseline_std:.2f}",
                f"{hep_peak:.2f}",
                f"{snr:.2f}",
                f"{max_gradient:.2f}",
                filter_setting,
                'TP9/TP10双侧乳突参考',
                quality_status
            ])

    print(f"分析摘要已保存: {summary_file}")
    
    # 保存质量检查报告
    quality_file = os.path.join(RESULT_DIR, f'quality_report{RESULT_SUFFIX}.txt')
    
    with open(quality_file, 'w', encoding='utf-8') as f:
        f.write("HEP数据质量检查报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"分析时间: {times[0]*1000:.0f} 到 {times[-1]*1000:.0f} ms\n")
        f.write(f"基线矫正窗口: {BASELINE_WINDOW[0]*1000:.0f} 到 {BASELINE_WINDOW[1]*1000:.0f} ms\n")
        f.write(f"HEP分析窗口: {CLASSIC_WINDOW[0]*1000:.0f} 到 {CLASSIC_WINDOW[1]*1000:.0f} ms\n")
        f.write(f"滤波设置: {CURRENT_FILTER} ({FILTER_CONFIG[CURRENT_FILTER]['low']}-{FILTER_CONFIG[CURRENT_FILTER]['high']}Hz)\n")
        f.write(f"参考方式: TP9/TP10双侧乳突参考\n\n")
        
        if quality_issues:
            f.write("发现的质量问题:\n")
            for issue in quality_issues:
                f.write(f"- {issue}\n")
        else:
            f.write("数据质量检查通过，无明显问题。\n")
        
        f.write(f"\n分析的脑区数量: {len(region_results)}\n")
        for region_name in region_results.keys():
            f.write(f"- {region_name}\n")
    
    print(f"质量检查报告已保存: {quality_file}")

def create_filter_comparison_analysis():
    """创建滤波前后对比分析"""
    print("\n" + "=" * 50)
    print("滤波效果对比分析")
    print("=" * 50)
    
    # 查找并加载最新的数据文件
    rest1_file, rest3_file = find_latest_files()
    
    # 加载原始数据（未滤波）
    rest1_data_raw, ch_names, times, rest1_subject_ids, sampling_freq = load_hep_data(rest1_file)
    rest3_data_raw, _, _, rest3_subject_ids, _ = load_hep_data(rest3_file)
    
    # 应用不同滤波参数
    original_params = FILTER_CONFIG['original']
    optimized_params = FILTER_CONFIG['default']
    
    print(f"\n对比滤波参数:")
    print(f"  原始滤波: {original_params['low']}-{original_params['high']}Hz")
    print(f"  优化滤波: {optimized_params['low']}-{optimized_params['high']}Hz")
    
    # 应用原始滤波
    print("\n应用原始滤波...")
    rest1_data_original = apply_bandpass_filter(rest1_data_raw.copy(), sampling_freq, 
                                              original_params['low'], original_params['high'])
    rest3_data_original = apply_bandpass_filter(rest3_data_raw.copy(), sampling_freq, 
                                              original_params['low'], original_params['high'])
    
    # 应用优化滤波
    print("应用优化滤波...")
    rest1_data_optimized = apply_bandpass_filter(rest1_data_raw.copy(), sampling_freq, 
                                                optimized_params['low'], optimized_params['high'])
    rest3_data_optimized = apply_bandpass_filter(rest3_data_raw.copy(), sampling_freq, 
                                                optimized_params['low'], optimized_params['high'])
    
    # 对两组数据分别进行基线矫正
    rest1_orig_corrected, rest3_orig_corrected = apply_unified_baseline_correction(
        rest1_data_original, rest3_data_original, times)
    rest1_opt_corrected, rest3_opt_corrected = apply_unified_baseline_correction(
        rest1_data_optimized, rest3_data_optimized, times)
    
    # 选择一个代表性脑区进行对比（右前额叶区域）
    target_region = '右前额叶区域'
    target_electrodes = BRAIN_REGIONS[target_region]
    
    print(f"\n分析 {target_region} 的滤波效果...")
    
    # 提取目标区域数据
    # 原始滤波数据
    rest1_orig_region, valid_electrodes = extract_region_data(
        rest1_orig_corrected, ch_names, rest1_subject_ids, target_electrodes)
    rest3_orig_region, _ = extract_region_data(
        rest3_orig_corrected, ch_names, rest3_subject_ids, target_electrodes)
    
    # 优化滤波数据
    rest1_opt_region, _ = extract_region_data(
        rest1_opt_corrected, ch_names, rest1_subject_ids, target_electrodes)
    rest3_opt_region, _ = extract_region_data(
        rest3_opt_corrected, ch_names, rest3_subject_ids, target_electrodes)
    
    # 计算区域平均
    rest1_orig_avg = calculate_region_average(rest1_orig_region)
    rest3_orig_avg = calculate_region_average(rest3_orig_region)
    rest1_opt_avg = calculate_region_average(rest1_opt_region)
    rest3_opt_avg = calculate_region_average(rest3_opt_region)
    
    # 创建对比图 - 使用2:1比例和优化的视觉样式
    fig, axes = plt.subplots(2, 2, figsize=(16, 8))  # 2:1宽高比
    fig.suptitle(f'{target_region} 滤波效果对比分析\n双侧乳突参考 (TP9/TP10)', 
                 fontsize=16, fontweight='bold')
    
    # 高对比度颜色配置 - 与主分析图保持一致
    color_rest1 = '#000080'     # 深蓝色
    color_rest3 = '#B8860B'     # 深黄色
    color_diff_orig = '#FF8C00'  # 深橙色 - 原始滤波差异
    color_diff_opt = '#8B0000'   # 深红色 - 优化滤波差异
    color_noise_orig = '#FF8C00' # 深橙色 - 原始滤波噪声
    color_noise_opt = '#800080'  # 深紫色 - 优化滤波噪声
    
    # 子图1: 原始滤波 - Rest1 vs Rest3
    ax1 = axes[0, 0]
    ax1.plot(times * 1000, rest1_orig_avg * 1e6, color=color_rest1, linewidth=1.0, 
             label='Rest1', alpha=0.95)
    ax1.plot(times * 1000, rest3_orig_avg * 1e6, color=color_rest3, linewidth=1.0, 
             label='Rest3', alpha=0.95)
    ax1.set_title(f'原始滤波 ({original_params["low"]}-{original_params["high"]}Hz)', fontsize=12)
    ax1.set_xlabel('时间 (ms)')
    ax1.set_ylabel('幅值 (μV)')
    ax1.grid(True, alpha=0.3)
    ax1.axhline(0, color='black', linewidth=0.5, alpha=0.6)
    ax1.legend(framealpha=0.95, edgecolor='black')
    ax1.set_xlim(VIS_WINDOW[0]*1000, VIS_WINDOW[1]*1000)
    
    # 子图2: 优化滤波 - Rest1 vs Rest3
    ax2 = axes[0, 1]
    ax2.plot(times * 1000, rest1_opt_avg * 1e6, color=color_rest1, linewidth=1.0, 
             label='Rest1', alpha=0.95)
    ax2.plot(times * 1000, rest3_opt_avg * 1e6, color=color_rest3, linewidth=1.0, 
             label='Rest3', alpha=0.95)
    ax2.set_title(f'优化滤波 ({optimized_params["low"]}-{optimized_params["high"]}Hz)', fontsize=12)
    ax2.set_xlabel('时间 (ms)')
    ax2.set_ylabel('幅值 (μV)')
    ax2.grid(True, alpha=0.3)
    ax2.axhline(0, color='black', linewidth=0.5, alpha=0.6)
    ax2.legend(framealpha=0.95, edgecolor='black')
    ax2.set_xlim(VIS_WINDOW[0]*1000, VIS_WINDOW[1]*1000)
    
    # 子图3: 差异波形对比
    diff_orig = rest3_orig_avg - rest1_orig_avg
    diff_opt = rest3_opt_avg - rest1_opt_avg
    
    ax3 = axes[1, 0]
    ax3.plot(times * 1000, diff_orig * 1e6, color=color_diff_orig, linewidth=1.0, 
             label=f'原始滤波差异', alpha=0.9)
    ax3.plot(times * 1000, diff_opt * 1e6, color=color_diff_opt, linewidth=1.0, 
             label=f'优化滤波差异', alpha=0.9)
    ax3.set_title('差异波形对比 (Rest3 - Rest1)', fontsize=12)
    ax3.set_xlabel('时间 (ms)')
    ax3.set_ylabel('差异幅值 (μV)')
    ax3.grid(True, alpha=0.3)
    ax3.axhline(0, color='black', linewidth=0.5, alpha=0.6)
    ax3.legend(framealpha=0.95, edgecolor='black')
    ax3.set_xlim(VIS_WINDOW[0]*1000, VIS_WINDOW[1]*1000)
    
    # 子图4: 噪声分析 - 计算高频成分的标准差
    ax4 = axes[1, 1]
    
    # 计算滑动窗口标准差来评估噪声
    window_size = int(sampling_freq * 0.1)  # 100ms窗口
    
    std_orig_rest1 = []
    std_opt_rest1 = []
    std_times = []
    
    for i in range(0, len(times) - window_size, window_size//2):
        window_orig = rest1_orig_avg[i:i+window_size]
        window_opt = rest1_opt_avg[i:i+window_size]
        
        std_orig_rest1.append(np.std(window_orig) * 1e6)
        std_opt_rest1.append(np.std(window_opt) * 1e6)
        std_times.append(times[i + window_size//2] * 1000)
    
    ax4.plot(std_times, std_orig_rest1, 'o-', color=color_noise_orig, linewidth=1.0, 
             label=f'原始滤波噪声', alpha=0.9, markersize=2)
    ax4.plot(std_times, std_opt_rest1, 'o-', color=color_noise_opt, linewidth=1.0, 
             label=f'优化滤波噪声', alpha=0.9, markersize=2)
    ax4.set_title('噪声水平对比 (滑动窗口标准差)', fontsize=12)
    ax4.set_xlabel('时间 (ms)')
    ax4.set_ylabel('标准差 (μV)')
    ax4.grid(True, alpha=0.3)
    ax4.legend(framealpha=0.95, edgecolor='black')
    ax4.set_xlim(VIS_WINDOW[0]*1000, VIS_WINDOW[1]*1000)
    
    plt.tight_layout()
    
    # 保存对比图 - 更新文件名以反映新的视觉优化
    comparison_file = os.path.join(RESULT_DIR, f'filter_comparison_analysis{RESULT_SUFFIX}_2to1ratio.png')
    plt.savefig(comparison_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"2:1比例滤波对比图已保存: {comparison_file}")
    
    plt.show()
    
    # 计算量化的改善指标
    noise_reduction = (np.mean(std_orig_rest1) - np.mean(std_opt_rest1)) / np.mean(std_orig_rest1) * 100
    print(f"\n滤波效果评估:")
    print(f"  平均噪声降低: {noise_reduction:.1f}%")
    print(f"  原始滤波平均噪声: {np.mean(std_orig_rest1):.2f} μV")
    print(f"  优化滤波平均噪声: {np.mean(std_opt_rest1):.2f} μV")
    
    return fig

def create_multi_filter_comparison():
    """创建多种滤波参数的对比分析"""
    print("\n" + "=" * 50)
    print("多滤波参数对比分析")
    print("=" * 50)

    # 查找并加载最新的数据文件
    rest1_file, rest3_file = find_latest_files()

    # 加载原始数据（未滤波）
    rest1_data_raw, ch_names, times, rest1_subject_ids, sampling_freq = load_hep_data(rest1_file)
    rest3_data_raw, _, _, rest3_subject_ids, _ = load_hep_data(rest3_file)

    # 选择一个代表性脑区进行对比（右前额叶区域）
    target_region = '右前额叶区域'
    target_electrodes = BRAIN_REGIONS[target_region]

    print(f"\n分析 {target_region} 的多滤波参数效果...")

    # 存储不同滤波参数的结果
    filter_results = {}

    for filter_name, filter_params in FILTER_CONFIG.items():
        print(f"\n处理滤波配置: {filter_name} ({filter_params['low']}-{filter_params['high']}Hz)")

        # 应用滤波
        rest1_filtered = apply_bandpass_filter(rest1_data_raw.copy(), sampling_freq,
                                             filter_params['low'], filter_params['high'])
        rest3_filtered = apply_bandpass_filter(rest3_data_raw.copy(), sampling_freq,
                                             filter_params['low'], filter_params['high'])

        # 基线矫正
        rest1_corrected, rest3_corrected = apply_unified_baseline_correction(
            rest1_filtered, rest3_filtered, times)

        # 提取目标区域数据
        rest1_region, _ = extract_region_data(
            rest1_corrected, ch_names, rest1_subject_ids, target_electrodes)
        rest3_region, _ = extract_region_data(
            rest3_corrected, ch_names, rest3_subject_ids, target_electrodes)

        # 计算区域平均
        rest1_avg = calculate_region_average(rest1_region)
        rest3_avg = calculate_region_average(rest3_region)

        # 计算质量指标
        baseline_mask = (times >= BASELINE_WINDOW[0]) & (times <= BASELINE_WINDOW[1])
        baseline_std = np.std(rest1_avg[baseline_mask]) * 1e6

        gradient = np.gradient(rest1_avg) * 1e6
        max_gradient = np.max(np.abs(gradient))

        hep_mask = (times >= CLASSIC_WINDOW[0]) & (times <= CLASSIC_WINDOW[1])
        hep_amplitude = abs(np.mean(rest3_avg[hep_mask]) - np.mean(rest1_avg[hep_mask])) * 1e6

        snr = hep_amplitude / baseline_std if baseline_std > 0 else 0

        filter_results[filter_name] = {
            'rest1_avg': rest1_avg,
            'rest3_avg': rest3_avg,
            'baseline_std': baseline_std,
            'max_gradient': max_gradient,
            'hep_amplitude': hep_amplitude,
            'snr': snr,
            'params': filter_params
        }

    # 创建对比图
    fig, axes = plt.subplots(2, 2, figsize=(16, 10))
    fig.suptitle(f'{target_region} 多滤波参数效果对比\n双侧乳突参考 (TP9/TP10)',
                 fontsize=16, fontweight='bold')

    # 颜色配置
    colors = ['#000080', '#B8860B', '#8B0000', '#006400']  # 深蓝、深黄、深红、深绿

    # 子图1: 波形对比
    ax1 = axes[0, 0]
    for i, (filter_name, result) in enumerate(filter_results.items()):
        params = result['params']
        label = f"{filter_name} ({params['low']}-{params['high']}Hz)"
        ax1.plot(times * 1000, result['rest1_avg'] * 1e6,
                color=colors[i % len(colors)], linewidth=1.0,
                label=label, alpha=0.8)

    ax1.set_title('Rest1波形对比', fontsize=12)
    ax1.set_xlabel('时间 (ms)')
    ax1.set_ylabel('幅值 (μV)')
    ax1.grid(True, alpha=0.3)
    ax1.axhline(0, color='black', linewidth=0.5, alpha=0.6)
    ax1.legend(fontsize=9)
    ax1.set_xlim(VIS_WINDOW[0]*1000, VIS_WINDOW[1]*1000)

    # 子图2: 质量指标对比
    ax2 = axes[0, 1]
    filter_names = list(filter_results.keys())
    baseline_stds = [filter_results[name]['baseline_std'] for name in filter_names]

    bars = ax2.bar(filter_names, baseline_stds, color=colors[:len(filter_names)], alpha=0.7)
    ax2.set_title('基线标准差对比', fontsize=12)
    ax2.set_ylabel('基线标准差 (μV)')
    ax2.tick_params(axis='x', rotation=45)

    # 添加数值标签
    for bar, value in zip(bars, baseline_stds):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.2f}', ha='center', va='bottom', fontsize=9)

    # 子图3: 信噪比对比
    ax3 = axes[1, 0]
    snrs = [filter_results[name]['snr'] for name in filter_names]

    bars = ax3.bar(filter_names, snrs, color=colors[:len(filter_names)], alpha=0.7)
    ax3.set_title('信噪比对比', fontsize=12)
    ax3.set_ylabel('信噪比')
    ax3.tick_params(axis='x', rotation=45)

    # 添加数值标签
    for bar, value in zip(bars, snrs):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.05,
                f'{value:.2f}', ha='center', va='bottom', fontsize=9)

    # 子图4: 梯度对比
    ax4 = axes[1, 1]
    gradients = [filter_results[name]['max_gradient'] for name in filter_names]

    bars = ax4.bar(filter_names, gradients, color=colors[:len(filter_names)], alpha=0.7)
    ax4.set_title('最大梯度对比', fontsize=12)
    ax4.set_ylabel('最大梯度 (μV/点)')
    ax4.tick_params(axis='x', rotation=45)

    # 添加数值标签
    for bar, value in zip(bars, gradients):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.2f}', ha='center', va='bottom', fontsize=9)

    plt.tight_layout()

    # 保存对比图
    comparison_file = os.path.join(RESULT_DIR, f'multi_filter_comparison{RESULT_SUFFIX}.png')
    plt.savefig(comparison_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"多滤波参数对比图已保存: {comparison_file}")

    plt.show()

    # 输出推荐的滤波参数
    print(f"\n滤波参数推荐:")
    best_snr_filter = max(filter_results.keys(), key=lambda x: filter_results[x]['snr'])
    best_smooth_filter = min(filter_results.keys(), key=lambda x: filter_results[x]['max_gradient'])

    print(f"  最佳信噪比: {best_snr_filter} (SNR={filter_results[best_snr_filter]['snr']:.2f})")
    print(f"  最平滑信号: {best_smooth_filter} (梯度={filter_results[best_smooth_filter]['max_gradient']:.2f}μV/点)")

    return fig, filter_results

def main():
    """主函数 - 滤波优化的两个静息阶段HEP分析"""
    print("=" * 60)
    print("HEP静息阶段分析：Rest1 vs Rest3（滤波优化版）")
    print("双侧乳突参考 (TP9/TP10)")
    print("=" * 60)
    
    print(f"时间窗口: {VIS_WINDOW[0]*1000:.0f} 到 {VIS_WINDOW[1]*1000:.0f} ms")
    print(f"基线矫正: {BASELINE_WINDOW[0]*1000:.0f} 到 {BASELINE_WINDOW[1]*1000:.0f} ms")
    print(f"HEP分析窗口: {CLASSIC_WINDOW[0]*1000:.0f} 到 {CLASSIC_WINDOW[1]*1000:.0f} ms")
    print(f"滤波设置: {CURRENT_FILTER} ({FILTER_CONFIG[CURRENT_FILTER]['low']}-{FILTER_CONFIG[CURRENT_FILTER]['high']}Hz)")
    print("数据处理流程: 加载→滤波→基线矫正→脑区分组→可视化→对比分析")

    try:
        # 1. 执行脑区分析
        print("\n1. 执行脑区分析...")
        region_results, times = create_region_analysis()

        if not region_results:
            print("未找到有效的脑区数据")
            return

        # 2. 创建优化可视化
        print("\n2. 创建滤波优化可视化图表...")
        fig = create_dual_visualization(region_results, times)

        # 3. 检查数据质量
        print("\n3. 检查数据质量...")
        quality_issues, quality_metrics = check_data_quality(region_results, times)

        # 4. 保存分析结果
        print("\n4. 保存分析结果...")
        save_comprehensive_results(region_results, times, quality_issues, quality_metrics)

        # 5. 创建滤波效果对比分析
        print("\n5. 创建滤波效果对比分析...")
        create_filter_comparison_analysis()

        # 6. 创建多滤波参数对比分析
        print("\n6. 创建多滤波参数对比分析...")
        multi_fig, filter_comparison_results = create_multi_filter_comparison()

        # 输出分析总结
        print("\n" + "=" * 60)
        print("科学滤波优化分析完成！")
        print("=" * 60)
        print(f"分析的脑区数量: {len(region_results)}")
        for region_name, result in region_results.items():
            electrode_count = len(result['electrodes'])
            print(f"  {region_name}: {electrode_count}个电极 ({', '.join(result['electrodes'])})")

        print(f"\n结果保存目录: {RESULT_DIR}")
        print("参考方式: TP9/TP10双侧乳突参考")

        # 输出当前滤波配置
        current_params = FILTER_CONFIG[CURRENT_FILTER]
        print(f"当前滤波配置: {CURRENT_FILTER} ({current_params['low']}-{current_params['high']}Hz)")

        # 输出生成的图表
        print("\n生成的图表:")
        print("  📊 主分析图: HEP静息阶段对比")
        print("  📈 滤波对比图: 原始vs优化滤波效果")
        print("  📉 多参数对比图: 所有滤波配置的性能对比")

        # 输出数据质量建议
        if quality_issues:
            print(f"\n⚠️  发现 {len(quality_issues)} 个数据质量问题")
            print("详细信息请查看质量报告文件")

            # 基于多滤波对比结果提供智能建议
            if 'filter_comparison_results' in locals():
                best_snr_filter = max(filter_comparison_results.keys(),
                                    key=lambda x: filter_comparison_results[x]['snr'])
                best_smooth_filter = min(filter_comparison_results.keys(),
                                       key=lambda x: filter_comparison_results[x]['max_gradient'])

                print(f"\n🔧 智能优化建议:")
                print(f"  最佳信噪比配置: {best_snr_filter}")
                print(f"  最平滑信号配置: {best_smooth_filter}")

                if any("抖动" in issue for issue in quality_issues):
                    print(f"  针对信号抖动: 建议使用 CURRENT_FILTER = '{best_smooth_filter}'")
                if any("信噪比" in issue for issue in quality_issues):
                    print(f"  针对信噪比低: 建议使用 CURRENT_FILTER = '{best_snr_filter}'")
        else:
            print("\n✅ 数据质量检查通过，所有指标符合科学标准")
            print("当前滤波配置已达到最佳效果")

        # 输出科学处理流程总结
        print(f"\n🔬 科学处理流程:")
        print(f"  1. 伪迹检测与移除 (幅度<100μV, 基线稳定性, 梯度检查)")
        print(f"  2. 带通滤波 ({current_params['low']}-{current_params['high']}Hz)")
        print(f"  3. 统一基线矫正 ({BASELINE_WINDOW[0]*1000:.0f}ms到{BASELINE_WINDOW[1]*1000:.0f}ms)")
        print(f"  4. 移动平均平滑 (窗口大小: {QUALITY_STANDARDS['smoothing_window']}点)")
        print(f"  5. 质量控制验证 (基线<{QUALITY_STANDARDS['baseline_std_threshold']}μV, HEP>{QUALITY_STANDARDS['hep_min_amplitude']}μV)")

        print(f"\n📋 质量控制标准:")
        print(f"  基线标准差阈值: {QUALITY_STANDARDS['baseline_std_threshold']}μV")
        print(f"  HEP最小幅度: {QUALITY_STANDARDS['hep_min_amplitude']}μV")
        print(f"  最大梯度阈值: {QUALITY_STANDARDS['max_gradient_threshold']}μV/点")
        print(f"  最小心跳周期数: {RR_INTERVAL_LIMITS['min_cycles']}个")

    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
