#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
心跳诱发电位(HEP)分析脚本

本脚本用于分析EEG和ECG数据，提取心跳诱发电位(HEP)，并进行可视化和统计分析。
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.ticker import AutoMinorLocator
import mne
from mne.preprocessing import ICA
import scipy.stats as stats
import neurokit2 as nk
from collections import defaultdict
# 不使用warnings.filterwarnings以便及时发现错误

# 设置matplotlib参数，使用IEEE Transactions风格
plt.rcParams.update({
    'font.family': 'LXGW Wenkai',
    'font.size': 10,
    'axes.labelsize': 10,
    'axes.titlesize': 10,
    'xtick.labelsize': 8,
    'ytick.labelsize': 8,
    'legend.fontsize': 8,
    'figure.titlesize': 12,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.format': 'svg',
    'axes.grid': True,
    'grid.alpha': 0.3,
    'axes.axisbelow': True,
    'axes.linewidth': 0.8,
    'xtick.major.width': 0.8,
    'ytick.major.width': 0.8,
    'xtick.minor.width': 0.6,
    'ytick.minor.width': 0.6
})

# 设置数据路径
DATA_DIR = r"C:\Users\<USER>\Desktop\ecgeeg\19-eegecg手动预处理6-ICA3"
OUTPUT_DIR = r"C:\Users\<USER>\Desktop\ecgeeg\30-数据分析\5-NeuroKit2\results"
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 定义EEG和ECG通道
EEG_CHANNELS = ['Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2',
                'F7', 'F8', 'T7', 'T8', 'P7', 'P8', 'Fz', 'Cz', 'Pz', 'FC1',
                'FC2', 'CP1', 'CP2', 'FC5', 'FC6', 'CP5', 'CP6', 'FT9', 'FT10',
                'TP9', 'TP10', 'F1', 'F2', 'C1', 'C2', 'P1', 'P2', 'AF3', 'AF4',
                'FC3', 'FC4', 'CP3', 'CP4', 'PO3', 'PO4', 'F5', 'F6', 'C5', 'C6',
                'P5', 'P6', 'AF7', 'AF8', 'FT7', 'FT8', 'TP7', 'TP8', 'PO7', 'PO8',
                'Fpz', 'CPz', 'POz', 'Oz']

ECG_CHANNELS = [f'ECG{i}' for i in range(1, 59)]

# 定义感兴趣的通道，特别是Fz, F7, F8, Cz (根据Schandry等人的研究)
CHANNELS_OF_INTEREST = ['Fz', 'F7', 'F8', 'Cz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4']

# 定义实验阶段
STAGES = {
    'prac': '练习阶段',
    'rest1': '静息态1',
    'test1': '刺激态1',
    'rest2': '静息态2',
    'test2': '刺激态2',
    'rest3': '静息态3',
    'test3': '刺激态3'
}

# 定义HEP时间窗口
HEP_TMIN = -0.2  # R波前200ms
HEP_TMAX = 1.0   # R波后1000ms
HEP_INTEREST_TMIN = 0.5  # 感兴趣的时间窗口开始（R波后500ms）
HEP_INTEREST_TMAX = 0.7  # 感兴趣的时间窗口结束（R波后700ms）

# 定义颜色方案
COLORS = {
    'prac': '#1f77b4',
    'rest1': '#2ca02c',
    'test1': '#d62728',
    'rest2': '#9467bd',
    'test2': '#ff7f0e',
    'rest3': '#8c564b',
    'test3': '#e377c2'
}

def load_data(subject_id, stage):
    """
    加载指定被试和阶段的数据

    参数:
    subject_id (int): 被试ID
    stage (str): 实验阶段 ('prac', 'rest1', 'test1', 'rest2', 'test2', 'rest3', 'test3')

    返回:
    mne.Raw: 加载的数据
    """
    # 根据阶段确定文件名模式
    if stage == 'prac':
        # 练习阶段
        file_pattern = f"{subject_id:02d}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_prac.fif"
    elif stage == 'rest1':
        # 静息态1
        file_pattern = f"{subject_id:02d}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_rest.fif"
    elif stage == 'test1':
        # 刺激态1
        file_pattern = f"{subject_id:02d}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_test.fif"
    elif stage == 'rest2':
        # 静息态2
        file_pattern = f"{subject_id:02d}_02_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_rest.fif"
        # 如果上面的模式不存在，尝试另一种模式
        alt_pattern = f"{subject_id:02d}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_rest.fif"
    elif stage == 'test2':
        # 刺激态2
        file_pattern = f"{subject_id:02d}_02_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_test.fif"
        # 如果上面的模式不存在，尝试另一种模式
        alt_pattern = f"{subject_id:02d}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_test.fif"
    elif stage == 'rest3':
        # 静息态3
        file_pattern = f"{subject_id:02d}_03_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_rest.fif"
        # 如果上面的模式不存在，尝试另一种模式
        alt_pattern = f"{subject_id:02d}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_rest.fif"
    elif stage == 'test3':
        # 刺激态3
        file_pattern = f"{subject_id:02d}_03_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_test.fif"
        # 如果上面的模式不存在，尝试另一种模式
        alt_pattern = f"{subject_id:02d}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_test.fif"
    else:
        print(f"未知阶段: {stage}")
        return None

    # 构建文件路径
    file_path = os.path.join(DATA_DIR, file_pattern)

    # 如果文件不存在且有替代模式，尝试替代模式
    if not os.path.exists(file_path) and stage in ['rest2', 'test2', 'rest3', 'test3']:
        file_path = os.path.join(DATA_DIR, alt_pattern)

    # 加载数据
    if os.path.exists(file_path):
        raw = mne.io.read_raw_fif(file_path, preload=True)
        print(f"成功加载数据: {file_path}")
        return raw
    else:
        print(f"文件不存在: {file_path}")
        return None

def extract_r_peaks(raw, ecg_channel='ECG1'):
    """
    从ECG信号中提取R峰

    参数:
    raw (mne.Raw): 原始数据
    ecg_channel (str): ECG通道名称

    返回:
    numpy.ndarray: R峰的采样点索引
    """
    # 提取ECG信号
    ecg_data, times = raw.copy().pick_channels([ecg_channel]).get_data(return_times=True)
    ecg_data = ecg_data.flatten()

    # 使用NeuroKit2检测R峰
    sampling_rate = raw.info['sfreq']
    _, info = nk.ecg_process(ecg_data, sampling_rate=sampling_rate)
    r_peaks = info['ECG_R_Peaks']

    return r_peaks

def extract_hep(raw, r_peaks, tmin=HEP_TMIN, tmax=HEP_TMAX):
    """
    提取心跳诱发电位

    参数:
    raw (mne.Raw): 原始数据
    r_peaks (numpy.ndarray): R峰的采样点索引
    tmin (float): 相对于R峰的起始时间（秒）
    tmax (float): 相对于R峰的结束时间（秒）

    返回:
    mne.Epochs: 提取的HEP epochs
    """
    # 创建事件数组
    events = np.column_stack([r_peaks, np.zeros_like(r_peaks), np.ones_like(r_peaks)])

    # 提取EEG通道
    raw_eeg = raw.copy().pick_channels(EEG_CHANNELS)

    # 创建epochs
    epochs = mne.Epochs(raw_eeg, events, event_id=1, tmin=tmin, tmax=tmax,
                        baseline=None, preload=True, reject=None)

    return epochs

def analyze_hep(subjects, stages):
    """
    分析多个被试的HEP数据

    参数:
    subjects (list): 被试ID列表
    stages (list): 实验阶段列表

    返回:
    dict: 各阶段的HEP数据
    """
    all_hep_data = {}

    for stage in stages:
        all_hep_data[stage] = []

    for subject_id in subjects:
        print(f"\n处理被试 {subject_id}")

        for stage in stages:
            print(f"  处理阶段: {stage}")

            # 加载数据
            raw = load_data(subject_id, stage)
            if raw is None:
                continue

            # 提取R峰
            r_peaks = extract_r_peaks(raw)
            if len(r_peaks) == 0:
                print(f"  未检测到R峰，跳过")
                continue

            # 提取HEP
            epochs = extract_hep(raw, r_peaks)

            # 存储数据
            all_hep_data[stage].append(epochs)

    # 合并各被试的数据
    for stage in stages:
        if all_hep_data[stage]:
            all_hep_data[stage] = mne.concatenate_epochs(all_hep_data[stage])
        else:
            all_hep_data[stage] = None

    return all_hep_data

def calculate_statistics(data1, data2, times, interest_tmin, interest_tmax):
    """
    计算两组数据在感兴趣时间窗口内的统计差异

    参数:
    data1, data2 (numpy.ndarray): 两组数据
    times (numpy.ndarray): 时间点
    interest_tmin, interest_tmax (float): 感兴趣的时间窗口

    返回:
    tuple: (p值数组, 显著性掩码, 显著性时间点)
    """
    # 找到感兴趣时间窗口的索引
    time_mask = (times >= interest_tmin) & (times <= interest_tmax)

    # 初始化p值数组
    p_values = np.ones_like(times)

    # 确保两组数据的长度相同
    min_len = min(data1.shape[0], data2.shape[0])
    data1_subset = data1[:min_len]
    data2_subset = data2[:min_len]

    # 在感兴趣的时间窗口内计算每个时间点的p值
    for i, t in enumerate(times):
        if time_mask[i]:
            # 使用独立t检验，因为数据可能来自不同的被试
            _, p_values[i] = stats.ttest_ind(data1_subset[:, i], data2_subset[:, i], equal_var=False)

    # 创建显著性掩码
    sig_mask = p_values < 0.05

    # 找到显著性时间点
    sig_times = times[sig_mask]

    return p_values, sig_mask, sig_times

def plot_hep_comparison(hep_data, output_dir):
    """
    绘制HEP对比图

    参数:
    hep_data (dict): 各阶段的HEP数据
    output_dir (str): 输出目录
    """
    # 定义对比组
    comparisons = [
        ('rest1', 'test1', "静息态1 vs 刺激态1"),
        ('rest2', 'test2', "静息态2 vs 刺激态2"),
        ('rest3', 'test3', "静息态3 vs 刺激态3")
    ]

    # 绘制前三个对比图
    for i, (stage1, stage2, title) in enumerate(comparisons):
        # 创建新的图形 - IEEE Transactions风格
        _, ax = plt.subplots(figsize=(8, 6), dpi=300, facecolor='white')
        if hep_data[stage1] is None or hep_data[stage2] is None:
            ax.text(0.5, 0.5, f"数据不可用: {stage1} vs {stage2}",
                    ha='center', va='center', transform=ax.transAxes)
            continue

        # 获取感兴趣通道数据 (Fz, F7, F8, Cz)
        channels_to_plot = [ch for ch in ['Fz', 'F7', 'F8', 'Cz'] if ch in hep_data[stage1].ch_names]
        data1 = hep_data[stage1].copy().pick_channels(channels_to_plot).get_data()
        data2 = hep_data[stage2].copy().pick_channels(channels_to_plot).get_data()
        times = hep_data[stage1].times

        # 计算平均值和标准误
        # 如果有多个通道，先对通道取平均
        if len(channels_to_plot) > 1:
            data1_avg = np.mean(data1, axis=1)
            data2_avg = np.mean(data2, axis=1)
        else:
            data1_avg = data1.squeeze(axis=1)
            data2_avg = data2.squeeze(axis=1)

        mean1 = np.mean(data1_avg, axis=0)
        sem1 = np.std(data1_avg, axis=0) / np.sqrt(data1_avg.shape[0])
        mean2 = np.mean(data2_avg, axis=0)
        sem2 = np.std(data2_avg, axis=0) / np.sqrt(data2_avg.shape[0])

        # 计算统计量
        p_values, sig_mask, _ = calculate_statistics(
            data1_avg, data2_avg, times, HEP_INTEREST_TMIN, HEP_INTEREST_TMAX
        )

        # 绘制曲线
        ax.plot(times, mean1, color=COLORS[stage1], label=STAGES[stage1], linewidth=1.5)
        ax.fill_between(times, mean1-sem1, mean1+sem1, color=COLORS[stage1], alpha=0.2)
        ax.plot(times, mean2, color=COLORS[stage2], label=STAGES[stage2], linewidth=1.5)
        ax.fill_between(times, mean2-sem2, mean2+sem2, color=COLORS[stage2], alpha=0.2)

        # 调整y轴范围，使差异更明显
        # 找到感兴趣时间窗口内的数据
        interest_mask = (times >= HEP_INTEREST_TMIN) & (times <= HEP_INTEREST_TMAX)
        interest_mean1 = mean1[interest_mask]
        interest_mean2 = mean2[interest_mask]

        # 计算感兴趣区域的平均值和最大差异
        overall_mean = (np.mean(interest_mean1) + np.mean(interest_mean2)) / 2
        max_diff = np.max(np.abs(interest_mean1 - interest_mean2))

        # 设置y轴范围，使差异更明显
        y_range = max(max_diff * 4, 1)  # 至少1μV的范围
        ax.set_ylim(overall_mean - y_range, overall_mean + y_range)

        # 标记显著性区域
        if np.any(sig_mask):
            # 找到连续的显著性区域
            from scipy.ndimage import label
            labeled_array, num_features = label(sig_mask)

            for j in range(1, num_features + 1):
                # 获取当前区域的索引
                region_indices = np.where(labeled_array == j)[0]
                region_start = times[region_indices[0]]
                region_end = times[region_indices[-1]]

                # 计算区域中的最小p值
                min_p = np.min(p_values[region_indices])

                # 标记显著性区域
                y_min, y_max = ax.get_ylim()
                height = y_max - y_min
                y_pos = y_min + height * 0.95

                # 绘制显著性区域
                ax.axvspan(region_start, region_end, alpha=0.2, color='gray')

                # 添加p值标签
                p_text = f"p={min_p:.3f}" if min_p >= 0.001 else "p<0.001"
                ax.text((region_start + region_end) / 2, y_pos, p_text,
                        ha='center', va='center', fontsize=8,
                        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))

        # 设置坐标轴
        ax.set_xlabel('时间 (s)')
        ax.set_ylabel('振幅 (μV)')
        ax.set_title(title)

        # 添加垂直线标记R波位置
        ax.axvline(x=0, color='k', linestyle='--', alpha=0.5)

        # 标记感兴趣的时间窗口
        ax.axvspan(HEP_INTEREST_TMIN, HEP_INTEREST_TMAX, alpha=0.1, color='blue')

        # 添加图例
        ax.legend(loc='upper right')

        # 设置网格
        ax.grid(True, linestyle='--', alpha=0.3)

        # 添加次要刻度
        ax.xaxis.set_minor_locator(AutoMinorLocator())
        ax.yaxis.set_minor_locator(AutoMinorLocator())

        # 调整布局
        plt.tight_layout()

        # 保存图像 - 使用SVG格式以便后续编辑
        output_path = os.path.join(output_dir, f'hep_comparison_{stage1}_{stage2}.svg')
        plt.savefig(output_path, format='svg', bbox_inches='tight', transparent=True)
        print(f"图像已保存至: {output_path}")

        # 显示图像 - 设置窗口大小以避免超出屏幕
        mng = plt.get_current_fig_manager()
        if hasattr(mng, 'window'):
            mng.window.showMaximized()
        plt.show()





    # 绘制第四个对比图：练习阶段 vs 静息态1 vs 刺激态1
    stages_to_compare = ['prac', 'rest1', 'test1']

    # 创建新的图形 - IEEE Transactions风格
    _, ax = plt.subplots(figsize=(8, 6), dpi=300, facecolor='white')

    valid_stages = [s for s in stages_to_compare if hep_data[s] is not None]

    if len(valid_stages) < 2:
        ax.text(0.5, 0.5, "数据不足以进行对比", ha='center', va='center', transform=ax.transAxes)
    else:
        for stage in valid_stages:
            # 获取感兴趣通道数据
            channels_to_plot = [ch for ch in ['Fz', 'F7', 'F8', 'Cz'] if ch in hep_data[stage].ch_names]
            data = hep_data[stage].copy().pick_channels(channels_to_plot).get_data()
            times = hep_data[stage].times

            # 如果有多个通道，先对通道取平均
            if len(channels_to_plot) > 1:
                data_avg = np.mean(data, axis=1)
            else:
                data_avg = data.squeeze(axis=1)

            # 计算平均值和标准误
            mean = np.mean(data_avg, axis=0)
            sem = np.std(data_avg, axis=0) / np.sqrt(data_avg.shape[0])

            # 绘制曲线
            ax.plot(times, mean, color=COLORS[stage], label=STAGES[stage], linewidth=1.5)
            ax.fill_between(times, mean-sem, mean+sem, color=COLORS[stage], alpha=0.2)

        # 如果有三个阶段都有数据，计算ANOVA
        if len(valid_stages) == 3:
            # 在感兴趣的时间窗口内进行ANOVA分析
            time_mask = (times >= HEP_INTEREST_TMIN) & (times <= HEP_INTEREST_TMAX)

            # 初始化p值数组
            p_values = np.ones_like(times)

            # 在感兴趣的时间窗口内计算每个时间点的ANOVA
            for i, _ in enumerate(times):
                if time_mask[i]:
                    groups = []
                    for stage in valid_stages:
                        channels_to_plot = [ch for ch in ['Fz', 'F7', 'F8', 'Cz'] if ch in hep_data[stage].ch_names]
                        data = hep_data[stage].copy().pick_channels(channels_to_plot).get_data()
                        if len(channels_to_plot) > 1:
                            data_avg = np.mean(data, axis=1)
                        else:
                            data_avg = data.squeeze(axis=1)
                        groups.append(data_avg[:, i])
                    _, p_values[i] = stats.f_oneway(*groups)

            # 创建显著性掩码
            sig_mask = p_values < 0.05

            # 标记显著性区域
            if np.any(sig_mask):
                # 找到连续的显著性区域
                from scipy.ndimage import label
                labeled_array, num_features = label(sig_mask)

                for i in range(1, num_features + 1):
                    # 获取当前区域的索引
                    region_indices = np.where(labeled_array == i)[0]
                    region_start = times[region_indices[0]]
                    region_end = times[region_indices[-1]]

                    # 计算区域中的最小p值
                    min_p = np.min(p_values[region_indices])

                    # 标记显著性区域
                    y_min, y_max = ax.get_ylim()
                    height = y_max - y_min
                    y_pos = y_min + height * 0.95

                    # 绘制显著性区域
                    ax.axvspan(region_start, region_end, alpha=0.2, color='gray')

                    # 添加p值标签
                    p_text = f"p={min_p:.3f}" if min_p >= 0.001 else "p<0.001"
                    ax.text((region_start + region_end) / 2, y_pos, p_text,
                            ha='center', va='center', fontsize=8,
                            bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', pad=1))

        # 设置坐标轴
        ax.set_xlabel('时间 (s)')
        ax.set_ylabel('振幅 (μV)')
        ax.set_title("练习阶段 vs 静息态1 vs 刺激态1")

        # 添加垂直线标记R波位置
        ax.axvline(x=0, color='k', linestyle='--', alpha=0.5)

        # 标记感兴趣的时间窗口
        ax.axvspan(HEP_INTEREST_TMIN, HEP_INTEREST_TMAX, alpha=0.1, color='blue')

        # 添加图例
        ax.legend(loc='upper right')

        # 设置网格
        ax.grid(True, linestyle='--', alpha=0.3)

        # 添加次要刻度
        ax.xaxis.set_minor_locator(AutoMinorLocator())
        ax.yaxis.set_minor_locator(AutoMinorLocator())

        # 调整布局
        plt.tight_layout()

        # 保存图像 - 使用SVG格式以便后续编辑
        output_path = os.path.join(output_dir, 'hep_comparison_prac_rest1_test1.svg')
        plt.savefig(output_path, format='svg', bbox_inches='tight', transparent=True)
        print(f"图像已保存至: {output_path}")

        # 显示图像 - 设置窗口大小以避免超出屏幕
        mng = plt.get_current_fig_manager()
        if hasattr(mng, 'window'):
            mng.window.showMaximized()
        plt.show()

def plot_topographic_hep(hep_data, output_dir):
    """
    绘制HEP的地形图

    参数:
    hep_data (dict): 各阶段的HEP数据
    output_dir (str): 输出目录
    """
    # 定义对比组
    comparisons = [
        ('rest1', 'test1', "静息态1 vs 刺激态1"),
        ('rest2', 'test2', "静息态2 vs 刺激态2"),
        ('rest3', 'test3', "静息态3 vs 刺激态3"),
        ('prac', 'rest1', "练习阶段 vs 静息态1")
    ]

    # 定义感兴趣的时间窗口
    time_window = (HEP_INTEREST_TMIN, HEP_INTEREST_TMAX)

    # 创建图形
    fig, axes = plt.subplots(len(comparisons), 3, figsize=(12, 12), gridspec_kw={'width_ratios': [1, 1, 1]})

    for i, (stage1, stage2, _) in enumerate(comparisons):
        if hep_data[stage1] is None or hep_data[stage2] is None:
            for j in range(3):
                axes[i, j].text(0.5, 0.5, f"数据不可用: {stage1} vs {stage2}",
                        ha='center', va='center', transform=axes[i, j].transAxes)
            continue

        # 提取感兴趣时间窗口的数据
        data1 = hep_data[stage1].copy().crop(tmin=time_window[0], tmax=time_window[1]).average()
        data2 = hep_data[stage2].copy().crop(tmin=time_window[0], tmax=time_window[1]).average()

        # 计算差异
        diff = mne.combine_evoked([data2, data1], weights=[1, -1])

        # 绘制地形图

        # 第一个阶段 - 只使用一个时间点（时间窗口的中点）
        mid_time = (time_window[0] + time_window[1]) / 2
        data1.plot_topomap(times=mid_time,
                          ch_type='eeg',
                          axes=axes[i, 0], colorbar=False, show=False)
        axes[i, 0].set_title(STAGES[stage1])

        # 第二个阶段 - 只使用一个时间点（时间窗口的中点）
        mid_time = (time_window[0] + time_window[1]) / 2
        data2.plot_topomap(times=mid_time,
                          ch_type='eeg',
                          axes=axes[i, 1], colorbar=False, show=False)
        axes[i, 1].set_title(STAGES[stage2])

        # 差异 - 只使用一个时间点（时间窗口的中点）
        mid_time = (time_window[0] + time_window[1]) / 2
        im, _ = diff.plot_topomap(times=mid_time,
                                ch_type='eeg',
                                axes=axes[i, 2], colorbar=False, show=False)
        axes[i, 2].set_title(f"差异 ({STAGES[stage2]} - {STAGES[stage1]})")

        # 添加颜色条
        cb_ax = fig.add_axes([0.92, 0.1 + i * 0.22, 0.01, 0.18])
        cbar = plt.colorbar(im, cax=cb_ax)
        cbar.set_label('振幅 (μV)')

    # 调整布局
    plt.tight_layout(rect=[0, 0, 0.9, 1])

    # 保存图像 - 使用SVG格式以便后续编辑
    output_path = os.path.join(output_dir, 'hep_topographic.svg')
    plt.savefig(output_path, format='svg', bbox_inches='tight', transparent=True)
    print(f"地形图已保存至: {output_path}")

    # 显示图像 - 设置窗口大小以避免超出屏幕
    mng = plt.get_current_fig_manager()
    if hasattr(mng, 'window'):
        mng.window.showMaximized()
    plt.show()

def main():
    """主函数"""
    # 设置被试ID列表
    subjects = list(range(9, 33))  # 从09到32号被试

    # 设置实验阶段
    stages = ['prac', 'rest1', 'test1', 'rest2', 'test2', 'rest3', 'test3']

    # 分析HEP数据
    hep_data = analyze_hep(subjects, stages)

    # 绘制HEP对比图
    plot_hep_comparison(hep_data, OUTPUT_DIR)

    # 绘制HEP地形图
    plot_topographic_hep(hep_data, OUTPUT_DIR)

if __name__ == "__main__":
    main()
