import os
import numpy as np
import pandas as pd
import h5py
from datetime import datetime
from pathlib import Path
from eeg_feature_extractor import extract_eeg_features

# 配置
input_files = [
    "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/raw_epochs/rest3_raw_epochs_20250519_020228.h5",
    "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/raw_epochs/test1_raw_epochs_20250519_015828.h5",
    "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/raw_epochs/test2_raw_epochs_20250519_020103.h5",
    "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/raw_epochs/test3_raw_epochs_20250519_020327.h5",
    "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/raw_epochs/prac_raw_epochs_20250519_015707.h5",
    "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/raw_epochs/rest1_raw_epochs_20250519_015731.h5",
    "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/raw_epochs/rest2_raw_epochs_20250519_020005.h5"
]
output_dir = "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/features"
time_window = (0.5, 0.6)
group_channels = ['FCz', 'FC1', 'FC2', 'FC5', 'FC6', 'Cz', 'C3', 'C4', 'T7', 'T8']

os.makedirs(output_dir, exist_ok=True)

all_rows = []
for file_path in input_files:
    file_name = Path(file_path).stem
    stage = file_name.split('_')[0]
    print(f"处理文件: {file_path}")
    with h5py.File(file_path, 'r') as f:
        data = f['data'][:]
        times = f['times'][:]
        ch_names = [ch.decode('utf-8') if isinstance(ch, bytes) else ch for ch in f['ch_names'][:]]
        subject_ids = [subj.decode('utf-8') if isinstance(subj, bytes) else subj for subj in f['subject_ids'][:]]
        stages = [stg.decode('utf-8') if isinstance(stg, bytes) else stg for stg in f['stages'][:]]
    # 找到时间窗口索引
    start_idx = np.where(times >= time_window[0])[0][0]
    end_idx = np.where(times <= time_window[1])[0][-1]
    sfreq = 1.0 / (times[1] - times[0])
    window_times = times[start_idx:end_idx+1]
    # 只分析中央区通道
    valid_channels = [ch for ch in group_channels if ch in ch_names]
    ch_indices = [ch_names.index(ch) for ch in valid_channels]
    for i in range(data.shape[0]):
        subject_id = subject_ids[i]
        this_stage = stages[i]
        if this_stage not in ['rest1', 'test3']:
            continue
        for ch, ch_idx in zip(valid_channels, ch_indices):
            signal = data[i, ch_idx, start_idx:end_idx+1]
            feats = extract_eeg_features(signal, sfreq, times=window_times)
            row = {'subject_id': subject_id, 'stage': this_stage, 'channel': ch}
            row.update(feats)
            all_rows.append(row)

# 保存为Excel
features_df = pd.DataFrame(all_rows)
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
output_file = os.path.join(output_dir, f'central_eeg_features_0.5_0.6_{timestamp}.xlsx')
features_df.to_excel(output_file, index=False)
print(f"已保存特征表: {output_file}")
print(f"特征列: {features_df.columns.tolist()}") 