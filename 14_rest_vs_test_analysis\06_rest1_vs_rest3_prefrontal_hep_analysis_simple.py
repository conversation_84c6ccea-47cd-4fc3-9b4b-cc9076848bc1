#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Rest1 vs Rest3 前额叶HEP电位对比分析 (简化版)

本脚本专门分析rest1和rest3阶段前额叶区域的HEP电位差异，
探索压力实验前后静息态的脑-心交互作用变化。

作者: AI Assistant
日期: 2025-01-27
"""

import os
import sys
import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-NeuroKit2'
DATA_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs')
RESULT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\14_rest_vs_test_analysis"
os.makedirs(RESULT_DIR, exist_ok=True)

# 定义前额叶电极
PREFRONTAL_ELECTRODES = ['Fp1', 'Fp2', 'Fpz', 'AF3', 'AF4', 'AF7', 'AF8', 'F3', 'F4', 'Fz']

# 感兴趣的时间窗口
TIME_WINDOWS = [
    (0.2, 0.4),   # 早期HEP成分
    (0.4, 0.6),   # 中期HEP成分  
    (0.5, 0.7),   # 晚期HEP成分
]

# 颜色设置
COLORS = {
    'rest1': '#2ca02c',  # 绿色
    'rest3': '#8c564b',  # 棕色
    'diff': '#d62728'    # 红色
}

def load_hep_data(h5_path):
    """加载HEP数据"""
    print(f"正在加载数据: {h5_path}")
    
    with h5py.File(h5_path, 'r') as f:
        data = f['data'][:]
        ch_names = [ch.decode('utf-8').strip() if isinstance(ch, bytes) else str(ch).strip() 
                   for ch in f['ch_names'][:]]
        times = f['times'][:]
        
        if 'subject_ids' in f:
            subject_ids = f['subject_ids'][:]
            subject_ids = [s.decode('utf-8') if isinstance(s, bytes) else str(s) 
                          for s in subject_ids]
        else:
            subject_ids = ['unknown'] * data.shape[0]
    
    print(f"数据形状: {data.shape}")
    print(f"通道数: {len(ch_names)}")
    print(f"时间点数: {len(times)}")
    print(f"被试数: {len(set(subject_ids))}")
    
    return data, ch_names, times, subject_ids

def extract_prefrontal_data(data, ch_names, subject_ids, electrodes):
    """提取前额叶电极数据"""
    # 找到实际存在的前额叶电极
    valid_electrodes = [e for e in electrodes if e in ch_names]
    print(f"实际存在的前额叶电极: {valid_electrodes}")
    
    # 获取电极索引
    ch_indices = [ch_names.index(e) for e in valid_electrodes]
    
    # 按被试组织数据
    subject_data = {}
    unique_subjects = sorted(set(subject_ids))
    
    for subj in unique_subjects:
        subj_indices = [i for i, s in enumerate(subject_ids) if s == subj]
        if subj_indices:
            subj_data = data[subj_indices][:, ch_indices, :]  # (n_epochs, n_channels, n_times)
            subject_data[subj] = subj_data
    
    print(f"成功提取 {len(subject_data)} 个被试的前额叶数据")
    
    return subject_data, valid_electrodes

def calculate_hep_features(data, times, time_windows):
    """计算HEP特征"""
    features = {}
    
    # 计算平均HEP波形
    mean_hep = np.mean(data, axis=0)  # (n_channels, n_times)
    features['mean_hep'] = mean_hep
    
    # 计算各时间窗口的平均振幅
    for i, (tmin, tmax) in enumerate(time_windows):
        time_mask = (times >= tmin) & (times <= tmax)
        window_amp = np.mean(mean_hep[:, time_mask], axis=1)  # (n_channels,)
        features[f'window_{i+1}_amplitude'] = window_amp
    
    # 计算峰值特征
    pos_peak_amp = np.max(mean_hep, axis=1)
    neg_peak_amp = np.min(mean_hep, axis=1)
    rms = np.sqrt(np.mean(mean_hep**2, axis=1))
    
    features['pos_peak_amplitude'] = pos_peak_amp
    features['neg_peak_amplitude'] = neg_peak_amp
    features['rms'] = rms
    
    return features

def compare_stages(rest1_data, rest3_data, electrodes, times, time_windows):
    """比较rest1和rest3阶段的HEP特征"""
    print("开始比较rest1和rest3阶段...")
    
    # 找到共同的被试
    common_subjects = set(rest1_data.keys()) & set(rest3_data.keys())
    print(f"共同被试数: {len(common_subjects)}")
    
    results = []
    
    for subj in common_subjects:
        # 计算rest1特征
        rest1_features = calculate_hep_features(rest1_data[subj], times, time_windows)
        
        # 计算rest3特征
        rest3_features = calculate_hep_features(rest3_data[subj], times, time_windows)
        
        # 保存结果
        for ch_idx, electrode in enumerate(electrodes):
            result = {
                'subject_id': subj,
                'electrode': electrode,
                'rest1_pos_peak_amp': rest1_features['pos_peak_amplitude'][ch_idx],
                'rest1_neg_peak_amp': rest1_features['neg_peak_amplitude'][ch_idx],
                'rest1_rms': rest1_features['rms'][ch_idx],
                'rest3_pos_peak_amp': rest3_features['pos_peak_amplitude'][ch_idx],
                'rest3_neg_peak_amp': rest3_features['neg_peak_amplitude'][ch_idx],
                'rest3_rms': rest3_features['rms'][ch_idx],
            }
            
            # 添加时间窗口特征
            for i, (tmin, tmax) in enumerate(time_windows):
                result[f'rest1_window_{i+1}_amp'] = rest1_features[f'window_{i+1}_amplitude'][ch_idx]
                result[f'rest3_window_{i+1}_amp'] = rest3_features[f'window_{i+1}_amplitude'][ch_idx]
                result[f'window_{i+1}_diff'] = (rest3_features[f'window_{i+1}_amplitude'][ch_idx] - 
                                               rest1_features[f'window_{i+1}_amplitude'][ch_idx])
            
            results.append(result)
    
    return results

def statistical_analysis(results, electrodes, time_windows):
    """进行统计分析"""
    print("进行统计分析...")
    
    stats_results = []
    
    # 分析各个特征
    features_to_test = [
        ('pos_peak_amp', '正峰振幅'),
        ('neg_peak_amp', '负峰振幅'),
        ('rms', 'RMS')
    ]
    
    # 添加时间窗口特征
    for i, (tmin, tmax) in enumerate(time_windows):
        features_to_test.append((f'window_{i+1}_amp', f'时间窗{i+1}({tmin}-{tmax}s)平均振幅'))
    
    for electrode in electrodes:
        electrode_data = [r for r in results if r['electrode'] == electrode]
        
        for feature, feature_name in features_to_test:
            rest1_values = [r[f'rest1_{feature}'] for r in electrode_data]
            rest3_values = [r[f'rest3_{feature}'] for r in electrode_data]
            
            # 配对t检验
            t_stat, p_value = stats.ttest_rel(rest1_values, rest3_values)
            
            # 计算效应量 (Cohen's d)
            diff = np.array(rest3_values) - np.array(rest1_values)
            cohen_d = np.mean(diff) / np.std(diff) if np.std(diff) > 0 else 0
            
            # 计算描述性统计
            rest1_mean = np.mean(rest1_values)
            rest1_std = np.std(rest1_values)
            rest3_mean = np.mean(rest3_values)
            rest3_std = np.std(rest3_values)
            
            stats_results.append({
                'electrode': electrode,
                'feature': feature_name,
                'rest1_mean': rest1_mean,
                'rest1_std': rest1_std,
                'rest3_mean': rest3_mean,
                'rest3_std': rest3_std,
                'difference': rest3_mean - rest1_mean,
                't_statistic': t_stat,
                'p_value': p_value,
                'cohen_d': cohen_d,
                'significant': p_value < 0.05
            })
    
    return stats_results

def plot_waveform_comparison(rest1_data, rest3_data, electrodes, times):
    """绘制HEP波形对比图"""
    print("绘制HEP波形对比图...")
    
    # 计算总体平均波形
    common_subjects = set(rest1_data.keys()) & set(rest3_data.keys())
    
    # 计算rest1总体平均
    rest1_all = []
    for subj in common_subjects:
        rest1_all.append(np.mean(rest1_data[subj], axis=0))  # 对epochs求平均
    rest1_grand_mean = np.mean(rest1_all, axis=0)  # 对被试求平均
    
    # 计算rest3总体平均
    rest3_all = []
    for subj in common_subjects:
        rest3_all.append(np.mean(rest3_data[subj], axis=0))  # 对epochs求平均
    rest3_grand_mean = np.mean(rest3_all, axis=0)  # 对被试求平均
    
    # 创建子图
    n_electrodes = len(electrodes)
    n_cols = 3
    n_rows = (n_electrodes + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 4*n_rows))
    if n_rows == 1:
        axes = axes.reshape(1, -1)
    
    times_ms = times * 1000  # 转换为毫秒
    
    for i, electrode in enumerate(electrodes):
        row = i // n_cols
        col = i % n_cols
        ax = axes[row, col]
        
        # 绘制波形
        ax.plot(times_ms, rest1_grand_mean[i], label='Rest1 (实验前静息)',
                color=COLORS['rest1'], linewidth=2)
        ax.plot(times_ms, rest3_grand_mean[i], label='Rest3 (实验后静息)',
                color=COLORS['rest3'], linewidth=2)
        
        # 计算差值曲线
        diff_curve = rest3_grand_mean[i] - rest1_grand_mean[i]
        ax.plot(times_ms, diff_curve, label='差值 (Rest3-Rest1)',
                color=COLORS['diff'], linewidth=1.5, linestyle='--', alpha=0.8)
        
        # 添加零线
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax.axvline(x=0, color='black', linestyle='--', alpha=0.5, label='R波')
        
        # 标记时间窗口
        for j, (tmin, tmax) in enumerate(TIME_WINDOWS):
            ax.axvspan(tmin*1000, tmax*1000, alpha=0.1, color=f'C{j}')
            ax.text((tmin+tmax)*500, ax.get_ylim()[1]*0.9, f'窗口{j+1}',
                   ha='center', fontsize=8, color=f'C{j}')
        
        ax.set_title(f'{electrode}电极', fontweight='bold')
        ax.set_xlabel('时间 (ms)')
        ax.set_ylabel('振幅 (μV)')
        ax.legend(fontsize=8)
        ax.grid(True, alpha=0.3)
        ax.set_xlim(-200, 1000)
    
    # 隐藏多余的子图
    for i in range(n_electrodes, n_rows * n_cols):
        row = i // n_cols
        col = i % n_cols
        axes[row, col].set_visible(False)
    
    plt.tight_layout()
    save_path = os.path.join(RESULT_DIR, 'rest1_vs_rest3_waveform_comparison.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.savefig(save_path.replace('.png', '.svg'), bbox_inches='tight')
    print(f"波形对比图已保存: {save_path}")
    plt.close()

def save_results(results, stats_results):
    """保存结果到CSV文件"""
    print("保存分析结果...")
    
    # 保存比较结果
    with open(os.path.join(RESULT_DIR, 'comparison_results.csv'), 'w', encoding='utf-8') as f:
        if results:
            # 写入表头
            headers = list(results[0].keys())
            f.write(','.join(headers) + '\n')
            
            # 写入数据
            for result in results:
                row = [str(result[h]) for h in headers]
                f.write(','.join(row) + '\n')
    
    # 保存统计结果
    with open(os.path.join(RESULT_DIR, 'statistical_results.csv'), 'w', encoding='utf-8') as f:
        if stats_results:
            # 写入表头
            headers = list(stats_results[0].keys())
            f.write(','.join(headers) + '\n')
            
            # 写入数据
            for result in stats_results:
                row = [str(result[h]) for h in headers]
                f.write(','.join(row) + '\n')

def main():
    """主函数"""
    print("="*60)
    print("Rest1 vs Rest3 前额叶HEP电位对比分析")
    print("="*60)
    
    # 定义数据文件路径
    rest1_file = os.path.join(DATA_DIR, 'rest1_raw_epochs_20250523_204957.h5')
    rest3_file = os.path.join(DATA_DIR, 'rest3_raw_epochs_20250523_205355.h5')
    
    # 检查文件是否存在
    if not os.path.exists(rest1_file):
        print(f"错误: Rest1数据文件不存在: {rest1_file}")
        return
    
    if not os.path.exists(rest3_file):
        print(f"错误: Rest3数据文件不存在: {rest3_file}")
        return
    
    # 加载数据
    print("\n1. 加载HEP数据...")
    rest1_data, rest1_ch_names, times, rest1_subject_ids = load_hep_data(rest1_file)
    rest3_data, rest3_ch_names, _, rest3_subject_ids = load_hep_data(rest3_file)
    
    # 提取前额叶数据
    print("\n2. 提取前额叶电极数据...")
    rest1_prefrontal, valid_electrodes = extract_prefrontal_data(
        rest1_data, rest1_ch_names, rest1_subject_ids, PREFRONTAL_ELECTRODES)
    rest3_prefrontal, _ = extract_prefrontal_data(
        rest3_data, rest3_ch_names, rest3_subject_ids, PREFRONTAL_ELECTRODES)
    
    # 比较分析
    print("\n3. 进行比较分析...")
    results = compare_stages(rest1_prefrontal, rest3_prefrontal, 
                           valid_electrodes, times, TIME_WINDOWS)
    
    # 统计分析
    print("\n4. 进行统计分析...")
    stats_results = statistical_analysis(results, valid_electrodes, TIME_WINDOWS)
    
    # 保存结果
    print("\n5. 保存分析结果...")
    save_results(results, stats_results)
    
    # 生成可视化
    print("\n6. 生成可视化图表...")
    plot_waveform_comparison(rest1_prefrontal, rest3_prefrontal, valid_electrodes, times)
    
    # 打印主要结果
    print("\n7. 主要发现:")
    sig_results = [r for r in stats_results if r['significant']]
    print(f"总共发现 {len(sig_results)} 个显著差异 (p < 0.05)")
    
    if sig_results:
        print("\n显著差异详情:")
        for result in sig_results:
            print(f"- {result['electrode']}电极 - {result['feature']}: "
                  f"t={result['t_statistic']:.3f}, p={result['p_value']:.4f}, "
                  f"Cohen's d={result['cohen_d']:.3f}")
    
    print("\n" + "="*60)
    print("分析完成！")
    print(f"结果保存在: {RESULT_DIR}")
    print("="*60)

if __name__ == "__main__":
    main()
