#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
读取调节效应分析报告
"""

import os

# 定义报告文件路径
report_path = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\moderation_analysis\moderation_analysis_report_20250519_054907.md"

# 检查文件是否存在
if os.path.exists(report_path):
    print(f"报告文件存在: {report_path}")
    
    # 读取文件内容
    with open(report_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 打印文件内容
    print("\n===== 报告内容 =====\n")
    print(content)
else:
    print(f"报告文件不存在: {report_path}")
