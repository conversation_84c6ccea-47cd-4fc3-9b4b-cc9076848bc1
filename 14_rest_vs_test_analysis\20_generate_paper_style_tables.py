import pandas as pd
import os

# 需要合并的csv文件列表
csv_files = [
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/literature_features_detailed_rest1_vs_test1.csv',
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/prefrontal_region_results_rest1_vs_test1.csv',
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/electrode_features_rest1_vs_rest3.csv',
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/literature_features_detailed_rest1_vs_rest3.csv',
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/frequency_band_features_rest1_vs_rest3.csv',
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/prefrontal_region_results_rest1_vs_rest3.csv',
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/electrode_features_rest1_vs_rest2.csv',
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/frequency_band_features_rest1_vs_rest2.csv',
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/literature_features_detailed_rest1_vs_rest2.csv',
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/prefrontal_region_results_rest1_vs_rest2.csv',
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/optimal_windows_results_extended.csv',
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/electrode_features_rest1_vs_test2.csv',
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/frequency_band_features_rest1_vs_test2.csv',
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/literature_features_detailed_rest1_vs_test2.csv',
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/prefrontal_region_results_rest1_vs_test2.csv',
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/electrode_features_rest1_vs_test1.csv',
    r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/frequency_band_features_rest1_vs_test1.csv',
]

# 输出Excel路径
output_path = r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/14_rest_vs_test_analysis/学术论文风格统计表.xlsx'

# 读取所有csv，按文件名分sheet导出
with pd.ExcelWriter(output_path) as writer:
    for csv_file in csv_files:
        try:
            df = pd.read_csv(csv_file)
            # sheet名用文件名（去除路径和扩展名，过长可截断）
            sheet_name = os.path.splitext(os.path.basename(csv_file))[0][:31]
            df.to_excel(writer, sheet_name=sheet_name, index=False)
            print(f"已写入: {sheet_name}")
        except Exception as e:
            print(f"读取或写入失败: {csv_file}, 错误: {e}")
print(f"已导出Excel: {output_path}") 