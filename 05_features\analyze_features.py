#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HEP分析实验 - 特征分析模块

功能：
- 加载提取的HEP特征
- 比较静息态和刺激态的HEP差异
- 确定最佳时间窗口和通道
- 进行统计分析
- 保存分析结果

作者：AI助手
日期：2024年
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import argparse
from datetime import datetime
import glob

# 设置matplotlib参数
plt.rcParams.update({
    'font.family': 'LXGW WenKai',  # 使用LXGW WenKai字体显示中文
    'font.size': 10,
    'axes.titlesize': 12,
    'axes.labelsize': 11,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 9,
    'figure.titlesize': 14,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'axes.grid': True,
    'grid.alpha': 0.3,
    'grid.linestyle': '--',
    'axes.axisbelow': True
})

# 定义常量
ROOT_DIR = r"D:/ecgeeg/30-数据分析/5-NeuroKit2/result"
FEATURES_DIR = os.path.join(ROOT_DIR, 'hep_analysis', 'features')
OUTPUT_DIR = os.path.join(ROOT_DIR, 'hep_analysis', 'results')

# 定义关键通道
KEY_CHANNELS = ['Fz', 'Cz', 'Pz', 'FCz', 'CPz']

# 定义阶段分组
REST_STAGES = ['rest1', 'rest2', 'rest3']
TEST_STAGES = ['test1', 'test2', 'test3']

# 定义阶段显示名称
STAGE_DISPLAY = {
    'prac': '练习阶段',
    'rest1': '静息态1',
    'test1': '刺激态1',
    'rest2': '静息态2',
    'test2': '刺激态2',
    'rest3': '静息态3',
    'test3': '刺激态3',
    'rest': '静息态',
    'test': '刺激态'
}

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='HEP特征分析')
    parser.add_argument('--features_dir', type=str, default=FEATURES_DIR,
                        help='特征目录')
    parser.add_argument('--output_dir', type=str, default=OUTPUT_DIR,
                        help='输出目录')
    parser.add_argument('--channels', type=str, default=','.join(KEY_CHANNELS),
                        help='关键通道列表，用逗号分隔')
    return parser.parse_args()

def load_features(features_dir):
    """
    加载特征

    参数:
    features_dir (str): 特征目录

    返回:
    dict: 包含各阶段特征的字典
    """
    print("加载特征...")

    # 确保特征目录存在
    if not os.path.exists(features_dir):
        print(f"错误: 特征目录不存在: {features_dir}")
        exit(1)

    features = {}

    # 查找所有聚合特征文件
    agg_files = glob.glob(os.path.join(features_dir, '*_agg_features_*.csv'))

    if not agg_files:
        print(f"错误: 未找到特征文件")
        exit(1)

    for file_path in agg_files:
        # 从文件名中提取阶段
        file_name = os.path.basename(file_path)
        stage = file_name.split('_')[0]

        # 加载特征
        df = pd.read_csv(file_path)

        # 添加到字典
        features[stage] = df
        print(f"加载{STAGE_DISPLAY.get(stage, stage)}阶段特征，共{len(df)}条记录")

    return features

def combine_rest_test_features(features):
    """
    合并静息态和刺激态特征

    参数:
    features (dict): 包含各阶段特征的字典

    返回:
    tuple: (静息态特征, 刺激态特征)
    """
    print("合并静息态和刺激态特征...")

    # 合并静息态特征
    rest_features = []
    for stage in REST_STAGES:
        if stage in features:
            df = features[stage].copy()
            df['condition'] = 'rest'
            rest_features.append(df)

    if not rest_features:
        print("错误: 未找到静息态特征")
        exit(1)

    rest_df = pd.concat(rest_features, ignore_index=True)
    print(f"合并静息态特征，共{len(rest_df)}条记录")

    # 合并刺激态特征
    test_features = []
    for stage in TEST_STAGES:
        if stage in features:
            df = features[stage].copy()
            df['condition'] = 'test'
            test_features.append(df)

    if not test_features:
        print("错误: 未找到刺激态特征")
        exit(1)

    test_df = pd.concat(test_features, ignore_index=True)
    print(f"合并刺激态特征，共{len(test_df)}条记录")

    return rest_df, test_df

def analyze_time_windows(rest_df, test_df, channels):
    """
    分析不同时间窗口的HEP差异

    参数:
    rest_df (pd.DataFrame): 静息态特征
    test_df (pd.DataFrame): 刺激态特征
    channels (list): 通道列表

    返回:
    dict: 包含每个时间窗口分析结果的字典
    """
    print("分析不同时间窗口的HEP差异...")

    # 获取所有时间窗口
    time_windows = []
    for start in rest_df['window_start'].unique():
        for end in rest_df['window_end'].unique():
            if start < end:
                time_windows.append((start, end))

    # 按时间窗口排序
    time_windows.sort()

    # 创建结果字典
    results = {}

    # 对每个时间窗口进行分析
    for time_window in time_windows:
        window_start, window_end = time_window
        print(f"分析时间窗口 {window_start}-{window_end}s...")

        # 筛选该时间窗口的数据
        rest_window = rest_df[(rest_df['window_start'] == window_start) & (rest_df['window_end'] == window_end)]
        test_window = test_df[(test_df['window_start'] == window_start) & (test_df['window_end'] == window_end)]

        # 创建结果列表
        window_results = []

        # 对每个通道进行分析
        for channel in channels:
            # 筛选该通道的数据
            rest_channel = rest_window[rest_window['channel'] == channel]
            test_channel = test_window[test_window['channel'] == channel]

            # 获取所有被试ID
            subject_ids = set(rest_channel['subject_id'].unique()) & set(test_channel['subject_id'].unique())

            # 创建配对数据
            paired_data = []
            for subject_id in subject_ids:
                rest_subj = rest_channel[rest_channel['subject_id'] == subject_id]
                test_subj = test_channel[test_channel['subject_id'] == subject_id]

                if len(rest_subj) > 0 and len(test_subj) > 0:
                    # 计算平均值
                    rest_mean = rest_subj['mean_amplitude_mean'].mean()
                    test_mean = test_subj['mean_amplitude_mean'].mean()

                    paired_data.append({
                        'subject_id': subject_id,
                        'channel': channel,
                        'rest_mean': rest_mean,
                        'test_mean': test_mean,
                        'difference': test_mean - rest_mean
                    })

            if paired_data:
                # 转换为DataFrame
                paired_df = pd.DataFrame(paired_data)

                # 配对t检验
                t_stat, p_value = stats.ttest_rel(paired_df['test_mean'], paired_df['rest_mean'])

                # 计算效应量 (Cohen's d)
                d = (paired_df['test_mean'] - paired_df['rest_mean']).mean() / paired_df['difference'].std()

                # 添加结果
                window_results.append({
                    'channel': channel,
                    'rest_mean': paired_df['rest_mean'].mean(),
                    'rest_std': paired_df['rest_mean'].std(),
                    'test_mean': paired_df['test_mean'].mean(),
                    'test_std': paired_df['test_mean'].std(),
                    'diff_mean': paired_df['difference'].mean(),
                    'diff_std': paired_df['difference'].std(),
                    't_stat': t_stat,
                    'p_value': p_value,
                    'cohen_d': d,
                    'n': len(paired_df)
                })

        # 转换为DataFrame
        results[f"{window_start}_{window_end}"] = pd.DataFrame(window_results)

    return results

def determine_best_window(window_results):
    """
    确定最佳时间窗口

    参数:
    window_results (dict): 包含每个时间窗口分析结果的字典

    返回:
    tuple: (最佳时间窗口, 该窗口的结果DataFrame)
    """
    print("确定最佳时间窗口...")

    window_stats = {}

    for window, results_df in window_results.items():
        # 计算平均效应量和显著通道数
        mean_d = np.mean([abs(row['cohen_d']) for _, row in results_df.iterrows()])
        sig_channels = sum(1 for _, row in results_df.iterrows() if row['p_value'] < 0.05)

        window_stats[window] = {
            'mean_cohen_d': mean_d,
            'sig_channels': sig_channels,
            'results': results_df
        }

    # 选择最佳时间窗口 - 基于显著通道数和平均效应量
    best_window = max(window_stats.items(), key=lambda x: (x[1]['sig_channels'], x[1]['mean_cohen_d']))

    # 将最佳窗口的起止时间转换为浮点数
    start, end = map(float, best_window[0].split('_'))

    print(f"最佳时间窗口: {start}-{end}s")
    print(f"显著通道数: {best_window[1]['sig_channels']}")
    print(f"平均效应量: {best_window[1]['mean_cohen_d']:.3f}")

    # 打印每个通道的统计结果
    print("\n各通道统计结果:")
    for _, row in best_window[1]['results'].iterrows():
        sig = '*' if row['p_value'] < 0.05 else ''
        print(f"{row['channel']}: t={row['t_stat']:.3f}, p={row['p_value']:.3f}{sig}, d={row['cohen_d']:.3f}, n={row['n']}")

    return (start, end), best_window[1]['results']

def save_results(results, output_dir, filename):
    """
    保存结果

    参数:
    results (pd.DataFrame): 结果
    output_dir (str): 输出目录
    filename (str): 文件名
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 构建文件路径
    file_path = os.path.join(output_dir, filename)

    # 保存为CSV
    results.to_csv(file_path, index=False)
    print(f"已保存结果至: {file_path}")

def visualize_results(best_window, best_results, output_dir):
    """
    可视化结果

    参数:
    best_window (tuple): 最佳时间窗口
    best_results (pd.DataFrame): 最佳时间窗口的结果
    output_dir (str): 输出目录
    """
    print("可视化结果...")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 1. 绘制静息态和刺激态的HEP差异柱状图
    plt.figure(figsize=(12, 8))

    # 准备数据
    channels = best_results['channel'].tolist()
    rest_means = best_results['rest_mean'].tolist()
    test_means = best_results['test_mean'].tolist()
    rest_stds = best_results['rest_std'].tolist()
    test_stds = best_results['test_std'].tolist()

    # 设置x轴位置
    x = np.arange(len(channels))
    width = 0.35

    # 绘制柱状图
    plt.bar(x - width/2, rest_means, width, label='静息态', color='#457B9D', yerr=rest_stds, capsize=5)
    plt.bar(x + width/2, test_means, width, label='刺激态', color='#E63946', yerr=test_stds, capsize=5)

    # 添加p值标记
    for i, p in enumerate(best_results['p_value']):
        if p < 0.05:
            stars = '*' if p < 0.05 else '**' if p < 0.01 else '***' if p < 0.001 else ''
            plt.text(i, max(rest_means[i], test_means[i]) + max(rest_stds[i], test_stds[i]) + 0.1,
                     stars, ha='center', va='bottom', fontsize=12)

    # 设置图表元素
    plt.xlabel('通道')
    plt.ylabel('HEP振幅 (μV)')
    plt.title(f'静息态与刺激态HEP差异 (时间窗口: {best_window[0]}-{best_window[1]}s)')
    plt.xticks(x, channels)
    plt.legend()
    plt.tight_layout()

    # 保存图表
    plt.savefig(os.path.join(output_dir, f'hep_difference_{best_window[0]}_{best_window[1]}.png'))
    plt.close()

    # 2. 绘制效应量热图
    plt.figure(figsize=(10, 6))

    # 准备数据
    effect_sizes = pd.DataFrame({'channel': best_results['channel'], 'cohen_d': best_results['cohen_d']})
    effect_sizes = effect_sizes.set_index('channel')

    # 绘制热图
    sns.heatmap(effect_sizes, annot=True, cmap='RdBu_r', center=0, fmt='.2f')

    # 设置图表元素
    plt.title(f'HEP效应量 (Cohen\'s d) (时间窗口: {best_window[0]}-{best_window[1]}s)')
    plt.tight_layout()

    # 保存图表
    plt.savefig(os.path.join(output_dir, f'hep_effect_size_{best_window[0]}_{best_window[1]}.png'))
    plt.close()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 解析通道列表
    channels = args.channels.split(',')

    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)

    # 加载特征
    features = load_features(args.features_dir)

    # 合并静息态和刺激态特征
    rest_df, test_df = combine_rest_test_features(features)

    # 分析不同时间窗口的HEP差异
    window_results = analyze_time_windows(rest_df, test_df, channels)

    # 确定最佳时间窗口
    best_window, best_results = determine_best_window(window_results)

    # 保存结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    save_results(best_results, args.output_dir, f'best_window_results_{timestamp}.csv')

    # 可视化结果
    visualize_results(best_window, best_results, args.output_dir)

    print("特征分析完成")

if __name__ == "__main__":
    main()
