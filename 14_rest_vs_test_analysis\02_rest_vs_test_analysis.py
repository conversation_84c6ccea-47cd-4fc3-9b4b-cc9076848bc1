#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
静息态与刺激态对比分析 - 模块2：静息态vs刺激态差异分析

功能：
- 加载预处理后的数据
- 进行静息态与刺激态的HEP振幅差异分析
- 生成统计结果和可视化图表
- 保存分析结果

作者：AI助手
日期：2024年
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from matplotlib.font_manager import FontProperties
import matplotlib as mpl
from datetime import datetime

# 设置中文字体
try:
    font = FontProperties(fname=r"C:\Windows\Fonts\LXGW WenKai Mono.ttf")
    plt.rcParams['font.family'] = ['sans-serif']
    plt.rcParams['font.sans-serif'] = ['LXGW WenKai']
    plt.rcParams['axes.unicode_minus'] = False
    mpl.rcParams['font.size'] = 10
except:
    print("警告: 未找到LXGW WenKai字体，使用系统默认字体")

# 定义常量
DATA_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\14_rest_vs_test_analysis"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\14_rest_vs_test_analysis"

def find_latest_data_file(data_dir=DATA_DIR, prefix="hep_merged_"):
    """
    查找最新的数据文件
    
    参数:
    data_dir (str): 数据目录
    prefix (str): 文件前缀
    
    返回:
    str: 最新的数据文件路径
    """
    print(f"查找数据文件: {data_dir}")
    
    # 查找所有数据文件
    data_files = [f for f in os.listdir(data_dir) if f.startswith(prefix) and f.endswith('.csv')]
    
    if not data_files:
        raise FileNotFoundError(f"未找到数据文件")
    
    # 选择最新的文件
    latest_file = max([os.path.join(data_dir, f) for f in data_files], key=os.path.getmtime)
    
    print(f"找到最新的数据文件: {os.path.basename(latest_file)}")
    
    return latest_file

def load_data(file_path):
    """
    加载数据
    
    参数:
    file_path (str): 数据文件路径
    
    返回:
    pd.DataFrame: 数据
    """
    print(f"加载数据: {file_path}")
    
    # 读取CSV文件
    df = pd.read_csv(file_path)
    
    print(f"数据包含{len(df)}条记录，{df['subject_id'].nunique()}个被试")
    
    return df

def perform_paired_ttest(df):
    """
    进行配对t检验
    
    参数:
    df (pd.DataFrame): 数据
    
    返回:
    pd.DataFrame: t检验结果
    """
    print("进行配对t检验...")
    
    # 获取唯一的通道和时间窗口组合
    channels = df['channel'].unique()
    windows = df['window'].unique()
    
    # 存储t检验结果
    ttest_results = []
    
    # 对每个通道和时间窗口进行配对t检验
    for channel in channels:
        for window in windows:
            # 筛选数据
            subset = df[(df['channel'] == channel) & (df['window'] == window)]
            
            # 进行配对t检验
            t_stat, p_value = stats.ttest_rel(subset['rest_amplitude'], subset['test_amplitude'])
            
            # 计算效应量 (Cohen's d)
            d = (subset['rest_amplitude'].mean() - subset['test_amplitude'].mean()) / \
                np.sqrt((subset['rest_amplitude'].std()**2 + subset['test_amplitude'].std()**2) / 2)
            
            # 添加结果
            ttest_results.append({
                'channel': channel,
                'window': window,
                'rest_mean': subset['rest_amplitude'].mean(),
                'test_mean': subset['test_amplitude'].mean(),
                'diff_mean': subset['diff_amplitude'].mean(),
                't_stat': t_stat,
                'p_value': p_value,
                'cohen_d': d,
                'n': len(subset)
            })
    
    # 转换为DataFrame
    ttest_df = pd.DataFrame(ttest_results)
    
    # 添加显著性标记
    ttest_df['significant'] = ttest_df['p_value'] < 0.05
    
    # 应用FDR校正
    from statsmodels.stats.multitest import fdrcorrection
    _, ttest_df['p_fdr'] = fdrcorrection(ttest_df['p_value'])
    ttest_df['significant_fdr'] = ttest_df['p_fdr'] < 0.05
    
    print(f"配对t检验完成，共{len(ttest_df)}个结果")
    
    return ttest_df

def plot_rest_vs_test_comparison(df, ttest_df, output_dir=OUTPUT_DIR):
    """
    绘制静息态vs刺激态对比图
    
    参数:
    df (pd.DataFrame): 数据
    ttest_df (pd.DataFrame): t检验结果
    output_dir (str): 输出目录
    
    返回:
    str: 图表文件路径
    """
    print("绘制静息态vs刺激态对比图...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建图表
    plt.figure(figsize=(15, 10))
    
    # 获取唯一的通道和时间窗口组合
    channels = df['channel'].unique()
    windows = df['window'].unique()
    
    # 设置子图布局
    n_channels = len(channels)
    n_windows = len(windows)
    
    # 对每个通道和时间窗口绘制箱线图
    for i, channel in enumerate(channels):
        for j, window in enumerate(windows):
            # 计算子图位置
            plt.subplot(n_channels, n_windows, i * n_windows + j + 1)
            
            # 筛选数据
            subset = df[(df['channel'] == channel) & (df['window'] == window)]
            
            # 准备数据
            rest_data = subset['rest_amplitude']
            test_data = subset['test_amplitude']
            
            # 绘制箱线图
            box_data = [rest_data, test_data]
            box_labels = ['静息态', '刺激态']
            box_colors = ['skyblue', 'lightcoral']
            
            plt.boxplot(box_data, labels=box_labels, patch_artist=True,
                       boxprops=dict(facecolor='white'),
                       medianprops=dict(color='black'),
                       flierprops=dict(marker='o', markersize=3))
            
            # 添加散点图
            for k, (data, label, color) in enumerate(zip(box_data, box_labels, box_colors)):
                # 添加抖动
                x = np.random.normal(k + 1, 0.04, size=len(data))
                plt.scatter(x, data, alpha=0.5, color=color, s=20)
            
            # 添加均值点和连线
            plt.plot([1, 2], [rest_data.mean(), test_data.mean()], 'o-', color='black', markersize=8)
            
            # 获取t检验结果
            result = ttest_df[(ttest_df['channel'] == channel) & (ttest_df['window'] == window)].iloc[0]
            
            # 添加t检验结果
            if result['significant']:
                sig_text = '*' if result['p_value'] < 0.05 else ''
                sig_text += '*' if result['p_value'] < 0.01 else ''
                sig_text += '*' if result['p_value'] < 0.001 else ''
                
                plt.title(f"{channel} {window}\nt={result['t_stat']:.2f}, p={result['p_value']:.3f}{sig_text}\nd={result['cohen_d']:.2f}")
            else:
                plt.title(f"{channel} {window}\nt={result['t_stat']:.2f}, p={result['p_value']:.3f}\nd={result['cohen_d']:.2f}")
            
            # 设置y轴标签
            if j == 0:
                plt.ylabel('HEP振幅 (μV)')
    
    # 调整布局
    plt.tight_layout()
    
    # 添加总标题
    plt.suptitle('静息态vs刺激态HEP振幅对比', fontsize=16, y=1.02)
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    fig_path = os.path.join(output_dir, f"rest_vs_test_comparison_{timestamp}.png")
    plt.savefig(fig_path, bbox_inches='tight', dpi=300)
    plt.close()
    
    print(f"静息态vs刺激态对比图已保存至: {fig_path}")
    
    return fig_path

def plot_effect_sizes(ttest_df, output_dir=OUTPUT_DIR):
    """
    绘制效应量图
    
    参数:
    ttest_df (pd.DataFrame): t检验结果
    output_dir (str): 输出目录
    
    返回:
    str: 图表文件路径
    """
    print("绘制效应量图...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建图表
    plt.figure(figsize=(10, 6))
    
    # 准备数据
    channels = ttest_df['channel'].unique()
    windows = ttest_df['window'].unique()
    
    # 设置x轴位置
    x = np.arange(len(channels))
    width = 0.35
    
    # 对每个时间窗口绘制条形图
    for i, window in enumerate(windows):
        # 筛选数据
        subset = ttest_df[ttest_df['window'] == window]
        
        # 获取效应量
        effect_sizes = [subset[subset['channel'] == ch]['cohen_d'].values[0] for ch in channels]
        
        # 设置颜色
        colors = []
        for es, p in zip(effect_sizes, [subset[subset['channel'] == ch]['p_value'].values[0] for ch in channels]):
            if p < 0.05:
                colors.append('green' if es > 0 else 'red')
            else:
                colors.append('lightgreen' if es > 0 else 'lightcoral')
        
        # 绘制条形图
        plt.bar(x + (i - 0.5) * width, effect_sizes, width, label=window, color=colors)
    
    # 添加水平线
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    # 添加标签和图例
    plt.xlabel('通道')
    plt.ylabel("效应量 (Cohen's d)")
    plt.title('静息态vs刺激态HEP振幅差异的效应量')
    plt.xticks(x, channels)
    plt.legend(title='时间窗口')
    
    # 添加网格
    plt.grid(True, alpha=0.3)
    
    # 添加效应量大小参考线
    plt.axhline(y=0.2, color='gray', linestyle='--', alpha=0.5)
    plt.axhline(y=0.5, color='gray', linestyle='--', alpha=0.5)
    plt.axhline(y=0.8, color='gray', linestyle='--', alpha=0.5)
    plt.text(len(channels) - 1, 0.2, '小效应', ha='right', va='bottom', alpha=0.7)
    plt.text(len(channels) - 1, 0.5, '中效应', ha='right', va='bottom', alpha=0.7)
    plt.text(len(channels) - 1, 0.8, '大效应', ha='right', va='bottom', alpha=0.7)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    fig_path = os.path.join(output_dir, f"effect_sizes_{timestamp}.png")
    plt.savefig(fig_path, dpi=300)
    plt.close()
    
    print(f"效应量图已保存至: {fig_path}")
    
    return fig_path

def save_results(ttest_df, output_dir=OUTPUT_DIR):
    """
    保存分析结果
    
    参数:
    ttest_df (pd.DataFrame): t检验结果
    output_dir (str): 输出目录
    
    返回:
    str: 保存的文件路径
    """
    print("保存分析结果...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存t检验结果
    ttest_path = os.path.join(output_dir, f"rest_vs_test_ttest_{timestamp}.csv")
    ttest_df.to_csv(ttest_path, index=False)
    
    print(f"分析结果已保存至: {ttest_path}")
    
    return ttest_path

def generate_report(ttest_df, fig_paths, output_dir=OUTPUT_DIR):
    """
    生成分析报告
    
    参数:
    ttest_df (pd.DataFrame): t检验结果
    fig_paths (list): 图表文件路径列表
    output_dir (str): 输出目录
    
    返回:
    str: 报告文件路径
    """
    print("生成分析报告...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 创建报告
    report = """# 静息态vs刺激态HEP振幅差异分析报告

## 1. 分析概述

本报告分析了静息态（rest1-3）与刺激态（test1-3）条件下的HEP振幅差异，
重点关注Fz、Cz、Pz导联在0.4-0.6s和0.5-0.7s时间窗口的变化。

## 2. 统计结果

下表展示了各通道和时间窗口的配对t检验结果：

"""
    
    # 添加t检验结果表格
    ttest_table = ttest_df[['channel', 'window', 'rest_mean', 'test_mean', 'diff_mean', 
                           't_stat', 'p_value', 'p_fdr', 'cohen_d', 'n']].round(4)
    
    # 添加显著性标记
    ttest_table['p_value'] = ttest_table.apply(
        lambda row: f"{row['p_value']:.4f}{'*' if row['p_value'] < 0.05 else ''}{'*' if row['p_value'] < 0.01 else ''}{'*' if row['p_value'] < 0.001 else ''}", 
        axis=1
    )
    
    ttest_table['p_fdr'] = ttest_table.apply(
        lambda row: f"{row['p_fdr']:.4f}{'*' if row['p_fdr'] < 0.05 else ''}{'*' if row['p_fdr'] < 0.01 else ''}{'*' if row['p_fdr'] < 0.001 else ''}", 
        axis=1
    )
    
    # 重命名列
    ttest_table = ttest_table.rename(columns={
        'channel': '通道',
        'window': '时间窗口',
        'rest_mean': '静息态均值',
        'test_mean': '刺激态均值',
        'diff_mean': '差异均值',
        't_stat': 't值',
        'p_value': 'p值',
        'p_fdr': 'FDR校正p值',
        'cohen_d': "Cohen's d",
        'n': '样本量'
    })
    
    report += ttest_table.to_markdown(index=False)
    
    report += """

注：* p<0.05, ** p<0.01, *** p<0.001

## 3. 可视化结果

"""
    
    # 添加图表引用
    for path in fig_paths:
        fig_name = os.path.basename(path)
        report += f"![{fig_name}]({fig_name})\n\n"
    
    report += """
## 4. 结果解释

"""
    
    # 添加结果解释
    sig_results = ttest_df[ttest_df['significant']]
    
    if len(sig_results) > 0:
        report += "### 显著差异\n\n"
        
        for _, row in sig_results.iterrows():
            channel = row['channel']
            window = row['window']
            rest_mean = row['rest_mean']
            test_mean = row['test_mean']
            diff_mean = row['diff_mean']
            t_stat = row['t_stat']
            p_value = row['p_value']
            cohen_d = row['cohen_d']
            n = row['n']
            
            direction = "高于" if diff_mean > 0 else "低于"
            
            report += f"- **{channel}通道，{window}时间窗口**：静息态HEP振幅（{rest_mean:.4f}）显著{direction}刺激态（{test_mean:.4f}），"
            report += f"差异值为{diff_mean:.4f}。统计结果：t({n-1})={t_stat:.2f}, p={p_value:.4f}, d={cohen_d:.2f}。\n"
    else:
        report += "未发现静息态与刺激态之间的显著差异。\n"
    
    report += """
### 效应量分析

"""
    
    # 添加效应量分析
    large_effects = ttest_df[abs(ttest_df['cohen_d']) > 0.8]
    medium_effects = ttest_df[(abs(ttest_df['cohen_d']) > 0.5) & (abs(ttest_df['cohen_d']) <= 0.8)]
    small_effects = ttest_df[(abs(ttest_df['cohen_d']) > 0.2) & (abs(ttest_df['cohen_d']) <= 0.5)]
    
    if len(large_effects) > 0:
        report += "**大效应（|d| > 0.8）**：\n"
        for _, row in large_effects.iterrows():
            report += f"- {row['channel']}通道，{row['window']}时间窗口：d = {row['cohen_d']:.2f}\n"
    
    if len(medium_effects) > 0:
        report += "\n**中等效应（0.5 < |d| ≤ 0.8）**：\n"
        for _, row in medium_effects.iterrows():
            report += f"- {row['channel']}通道，{row['window']}时间窗口：d = {row['cohen_d']:.2f}\n"
    
    if len(small_effects) > 0:
        report += "\n**小效应（0.2 < |d| ≤ 0.5）**：\n"
        for _, row in small_effects.iterrows():
            report += f"- {row['channel']}通道，{row['window']}时间窗口：d = {row['cohen_d']:.2f}\n"
    
    report += """
## 5. 结论

"""
    
    # 添加结论
    if len(sig_results) > 0:
        report += "本研究发现，静息态与刺激态条件下的HEP振幅存在显著差异，特别是在以下通道和时间窗口：\n\n"
        
        for _, row in sig_results.iterrows():
            report += f"- {row['channel']}通道，{row['window']}时间窗口\n"
        
        report += "\n这些差异表明，压力刺激可能影响了内感受信号的神经加工，特别是在"
        
        if 'Pz' in sig_results['channel'].values:
            report += "顶叶区域（Pz）"
            if 'Cz' in sig_results['channel'].values or 'Fz' in sig_results['channel'].values:
                report += "、"
        
        if 'Cz' in sig_results['channel'].values:
            report += "中央区域（Cz）"
            if 'Fz' in sig_results['channel'].values:
                report += "和"
        
        if 'Fz' in sig_results['channel'].values:
            report += "前额区域（Fz）"
        
        report += "。这与内感受加工的神经网络一致，支持了压力情境下内感受加工发生变化的假设。\n"
    else:
        report += "本研究未发现静息态与刺激态条件下的HEP振幅存在显著差异。这可能是由于样本量有限、个体差异较大或实验设计的限制。未来研究可以增加样本量，采用更敏感的分析方法，或考虑其他可能影响HEP振幅的因素。\n"
    
    # 保存报告
    report_path = os.path.join(output_dir, f"rest_vs_test_analysis_report_{timestamp}.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"分析报告已保存至: {report_path}")
    
    return report_path

def main():
    """主函数"""
    # 查找最新的数据文件
    data_file = find_latest_data_file()
    
    # 加载数据
    df = load_data(data_file)
    
    # 进行配对t检验
    ttest_df = perform_paired_ttest(df)
    
    # 绘制静息态vs刺激态对比图
    comparison_fig_path = plot_rest_vs_test_comparison(df, ttest_df)
    
    # 绘制效应量图
    effect_fig_path = plot_effect_sizes(ttest_df)
    
    # 保存分析结果
    result_path = save_results(ttest_df)
    
    # 生成分析报告
    report_path = generate_report(ttest_df, [comparison_fig_path, effect_fig_path])
    
    print("\n静息态vs刺激态差异分析完成")
    print(f"分析报告已保存至: {report_path}")

if __name__ == "__main__":
    main()
