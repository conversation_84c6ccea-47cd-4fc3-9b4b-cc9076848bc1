#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建全脑平均参考 vs 双侧乳突参考的HEP波形对比图表

功能：
1. 加载两种参考方式的HEP数据
2. 创建4行2列的对比图表
3. 左列：全脑平均参考，右列：双侧乳突参考
4. 每行对应一个脑区的Rest1 vs Rest3对比
"""

import os
import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-NeuroKit2'
AVGREF_DATA_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', '1-全脑平均')  # 全脑平均参考数据
TP9TP10_DATA_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', 'tp9tp10')  # 双侧乳突参考数据
RESULT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'comparison')

# 创建结果目录
os.makedirs(RESULT_DIR, exist_ok=True)

# 定义前额叶电极分组
PREFRONTAL_REGIONS = {
    '左侧前额叶': ['Fp1', 'AF3', 'AF7', 'F3'],
    '右侧前额叶': ['Fp2', 'AF4', 'AF8', 'F4'],
    '中线前额叶': ['Fpz', 'Fz'],
    '中央顶区': ['Cz', 'CPz', 'Pz', 'POz']
}

# 时间窗口设置
VIS_WINDOW = (-0.5, 1.0)       # 可视化窗口 -500-1000ms
BASELINE_WINDOW = (-0.2, 0.0)  # 基线矫正窗口 R波前200ms

def load_hep_data(h5_path, reference_type="unknown"):
    """加载HEP数据"""
    print(f"正在加载{reference_type}数据: {os.path.basename(h5_path)}")

    with h5py.File(h5_path, 'r') as f:
        data = f['data'][:]
        ch_names = [ch.decode('utf-8').strip() if isinstance(ch, bytes) else str(ch).strip()
                   for ch in f['ch_names'][:]]
        times = f['times'][:]

        if 'subject_ids' in f:
            subject_ids = f['subject_ids'][:]
            subject_ids = [s.decode('utf-8') if isinstance(s, bytes) else str(s)
                          for s in subject_ids]
        else:
            subject_ids = ['unknown'] * data.shape[0]

    sampling_freq = 1 / (times[1] - times[0])
    print(f"  数据形状: {data.shape}")
    print(f"  时间范围: {times[0]*1000:.1f} to {times[-1]*1000:.1f} ms")
    print(f"  通道数: {len(ch_names)}")

    return data, ch_names, times, subject_ids, sampling_freq

def apply_combined_baseline_correction(rest1_data, rest3_data, times, baseline_window=BASELINE_WINDOW):
    """对rest1和rest3数据进行合并基线矫正"""
    # 找到基线窗口的索引
    baseline_mask = (times >= baseline_window[0]) & (times <= baseline_window[1])

    # 提取基线窗口的数据
    rest1_baseline = rest1_data[..., baseline_mask]
    rest3_baseline = rest3_data[..., baseline_mask]

    # 计算每个条件的基线均值
    rest1_baseline_mean = np.mean(rest1_baseline, axis=-1, keepdims=True)
    rest3_baseline_mean = np.mean(rest3_baseline, axis=-1, keepdims=True)

    # 合并基线均值
    combined_baseline_values = np.concatenate([
        rest1_baseline_mean.reshape(-1, rest1_baseline_mean.shape[1]),
        rest3_baseline_mean.reshape(-1, rest3_baseline_mean.shape[1])
    ], axis=0)

    # 计算总体基线均值
    global_baseline_mean = np.mean(combined_baseline_values, axis=0, keepdims=True)
    global_baseline_mean = global_baseline_mean[..., np.newaxis]

    # 应用相同的基线矫正
    rest1_corrected = rest1_data - global_baseline_mean
    rest3_corrected = rest3_data - global_baseline_mean

    return rest1_corrected, rest3_corrected

def find_latest_files(data_dir, prefix1='rest1_raw_epochs_', prefix3='rest3_raw_epochs_'):
    """查找最新的HEP数据文件"""
    rest1_files = [f for f in os.listdir(data_dir) if f.startswith(prefix1) and f.endswith('.h5')]
    rest3_files = [f for f in os.listdir(data_dir) if f.startswith(prefix3) and f.endswith('.h5')]

    if not rest1_files or not rest3_files:
        raise FileNotFoundError(f"未找到数据文件在目录: {data_dir}")

    # 按时间戳排序，取最新的
    rest1_files.sort()
    rest3_files.sort()

    rest1_file = os.path.join(data_dir, rest1_files[-1])
    rest3_file = os.path.join(data_dir, rest3_files[-1])

    return rest1_file, rest3_file

def extract_region_data(data, ch_names, subject_ids, region_electrodes):
    """提取指定区域的电极数据"""
    valid_electrodes = [e for e in region_electrodes if e in ch_names]

    if not valid_electrodes:
        return None, []

    ch_indices = [ch_names.index(e) for e in valid_electrodes]

    subject_data = {}
    unique_subjects = sorted(set(subject_ids))

    for subj in unique_subjects:
        subj_indices = [i for i, s in enumerate(subject_ids) if s == subj]
        if subj_indices:
            subj_data = data[subj_indices][:, ch_indices, :]
            subject_data[subj] = subj_data

    return subject_data, valid_electrodes

def calculate_region_average(subject_data):
    """计算区域平均波形"""
    if subject_data is None:
        return None

    all_data = []
    for subj_data in subject_data.values():
        # 对每个被试的epochs求平均，然后对电极求平均
        subj_avg = np.mean(subj_data, axis=0)  # 对epochs求平均
        region_avg = np.mean(subj_avg, axis=0)  # 对电极求平均
        all_data.append(region_avg)

    # 对所有被试求平均
    grand_avg = np.mean(all_data, axis=0)
    return grand_avg

def load_reference_data(reference_type):
    """加载指定参考方式的数据"""

    if reference_type == "avgref":
        data_dir = AVGREF_DATA_DIR
        ref_name = "全脑平均参考"
    elif reference_type == "tp9tp10":
        data_dir = TP9TP10_DATA_DIR
        ref_name = "双侧乳突参考"
    else:
        raise ValueError(f"未知的参考类型: {reference_type}")

    print(f"\n加载{ref_name}数据...")

    # 查找并加载数据文件
    rest1_file, rest3_file = find_latest_files(data_dir)

    rest1_data, rest1_ch_names, times, rest1_subject_ids, sampling_freq = load_hep_data(rest1_file, ref_name)
    rest3_data, rest3_ch_names, _, rest3_subject_ids, _ = load_hep_data(rest3_file, ref_name)

    # 对数据进行合并基线矫正
    print(f"应用合并基线矫正...")
    rest1_data_corrected, rest3_data_corrected = apply_combined_baseline_correction(rest1_data, rest3_data, times)

    # 分析各区域
    region_results = {}

    for region_name, electrodes in PREFRONTAL_REGIONS.items():
        print(f"  分析 {region_name} 区域...")

        # 提取区域数据
        rest1_region, valid_electrodes = extract_region_data(
            rest1_data_corrected, rest1_ch_names, rest1_subject_ids, electrodes)
        rest3_region, _ = extract_region_data(
            rest3_data_corrected, rest3_ch_names, rest3_subject_ids, electrodes)

        if rest1_region is None or rest3_region is None:
            print(f"    {region_name} 区域数据不可用")
            continue

        # 计算区域平均
        rest1_avg = calculate_region_average(rest1_region)
        rest3_avg = calculate_region_average(rest3_region)

        if rest1_avg is None or rest3_avg is None:
            print(f"    {region_name} 区域平均计算失败")
            continue

        region_results[region_name] = {
            'electrodes': valid_electrodes,
            'rest1_avg': rest1_avg,
            'rest3_avg': rest3_avg
        }

    return region_results, times, ref_name

def create_reference_comparison_plot(avgref_results, tp9tp10_results, avgref_times, tp9tp10_times):
    """创建4行2列的参考方式对比图表"""

    # 创建图形 - 4行2列布局
    fig, axes = plt.subplots(4, 2, figsize=(16, 20))

    # 设置总标题
    fig.suptitle('HEP波形对比：全脑平均参考 vs 双侧乳突参考 (Rest1 vs Rest3)',
                 fontsize=16, fontweight='bold', y=0.98)

    # 定义区域顺序
    regions = ['左侧前额叶', '右侧前额叶', '中线前额叶', '中央顶区']

    # 颜色设置
    rest1_color = '#2E8B57'  # 深绿色
    rest3_color = '#DC143C'  # 深红色
    diff_color = '#000000'   # 黑色

    # 计算全局Y轴范围以确保左右对比的一致性
    all_y_values = []

    for region_name in regions:
        if region_name in avgref_results and region_name in tp9tp10_results:
            # 收集所有Y值用于确定全局范围
            avgref_rest1 = avgref_results[region_name]['rest1_avg'] * 1e6
            avgref_rest3 = avgref_results[region_name]['rest3_avg'] * 1e6
            tp9tp10_rest1 = tp9tp10_results[region_name]['rest1_avg'] * 1e6
            tp9tp10_rest3 = tp9tp10_results[region_name]['rest3_avg'] * 1e6

            all_y_values.extend([avgref_rest1.min(), avgref_rest1.max(),
                               avgref_rest3.min(), avgref_rest3.max(),
                               tp9tp10_rest1.min(), tp9tp10_rest1.max(),
                               tp9tp10_rest3.min(), tp9tp10_rest3.max()])

    # 分别计算两种参考方式的Y轴范围（统一单位为μV）
    avgref_y_values = []
    tp9tp10_y_values = []

    for region_name in regions:
        if region_name in avgref_results and region_name in tp9tp10_results:
            avgref_result = avgref_results[region_name]
            tp9tp10_result = tp9tp10_results[region_name]

            # 统一单位转换 - 与绘图部分保持一致
            # 全脑平均参考数据保持原单位（假设已经是μV）
            avgref_rest1 = avgref_result['rest1_avg']
            avgref_rest3 = avgref_result['rest3_avg']

            # 双侧乳突参考数据从V转换为μV
            tp9tp10_rest1 = tp9tp10_result['rest1_avg'] * 1e6
            tp9tp10_rest3 = tp9tp10_result['rest3_avg'] * 1e6

            avgref_y_values.extend([avgref_rest1.min(), avgref_rest1.max(),
                                   avgref_rest3.min(), avgref_rest3.max()])
            tp9tp10_y_values.extend([tp9tp10_rest1.min(), tp9tp10_rest1.max(),
                                    tp9tp10_rest3.min(), tp9tp10_rest3.max()])

    # 分别设置Y轴范围，确保双侧乳突参考的小幅值也能清晰显示
    if avgref_y_values:
        avgref_margin = (max(avgref_y_values) - min(avgref_y_values)) * 0.1
        avgref_ylim = (min(avgref_y_values) - avgref_margin, max(avgref_y_values) + avgref_margin)
    else:
        avgref_ylim = (-1, 1)

    if tp9tp10_y_values:
        tp9tp10_margin = (max(tp9tp10_y_values) - min(tp9tp10_y_values)) * 0.1
        tp9tp10_ylim = (min(tp9tp10_y_values) - tp9tp10_margin, max(tp9tp10_y_values) + tp9tp10_margin)
    else:
        tp9tp10_ylim = (-0.001, 0.001)

    print(f"全脑平均参考Y轴范围: {avgref_ylim[0]:.3f} to {avgref_ylim[1]:.3f} μV")
    print(f"双侧乳突参考Y轴范围: {tp9tp10_ylim[0]:.6f} to {tp9tp10_ylim[1]:.6f} μV")

    # 绘制每个区域的对比
    for row, region_name in enumerate(regions):
        if region_name not in avgref_results or region_name not in tp9tp10_results:
            # 如果数据不可用，显示空白图
            axes[row, 0].text(0.5, 0.5, f'{region_name}\n数据不可用',
                             ha='center', va='center', transform=axes[row, 0].transAxes)
            axes[row, 1].text(0.5, 0.5, f'{region_name}\n数据不可用',
                             ha='center', va='center', transform=axes[row, 1].transAxes)
            continue

        # 获取数据
        avgref_result = avgref_results[region_name]
        tp9tp10_result = tp9tp10_results[region_name]

        # 统一单位转换为微伏 (μV)
        # 根据MNE文档，EEG数据默认单位为伏特(V)，需要转换为μV进行对比
        # 双侧乳突参考数据保持MNE原始单位(V)，全脑平均参考数据可能已经转换过

        avgref_range = np.max(np.abs(avgref_result['rest1_avg']))
        tp9tp10_range = np.max(np.abs(tp9tp10_result['rest1_avg']))

        print(f"  {region_name} - 原始数据范围检查:")
        print(f"    全脑平均参考: {avgref_range:.6f}")
        print(f"    双侧乳突参考: {tp9tp10_range:.6f}")

        # 强制统一转换：双侧乳突参考数据从V转换为μV，全脑平均参考数据保持不变
        # 这样确保两种参考方式使用相同的单位进行对比
        avgref_rest1 = avgref_result['rest1_avg']  # 假设已经是μV
        avgref_rest3 = avgref_result['rest3_avg']
        tp9tp10_rest1 = tp9tp10_result['rest1_avg'] * 1e6  # 从V转换为μV
        tp9tp10_rest3 = tp9tp10_result['rest3_avg'] * 1e6

        print(f"  {region_name} - 全脑平均参考(保持原单位): {np.min(avgref_rest1):.3f} to {np.max(avgref_rest1):.3f} μV")
        print(f"  {region_name} - 双侧乳突参考(V→μV): {np.min(tp9tp10_rest1):.3f} to {np.max(tp9tp10_rest1):.3f} μV")

        # 计算差值曲线（Rest3 - Rest1，不取绝对值）
        avgref_diff = avgref_rest3 - avgref_rest1
        tp9tp10_diff = tp9tp10_rest3 - tp9tp10_rest1

        # 左列：全脑平均参考
        ax_left = axes[row, 0]
        ax_left.plot(avgref_times * 1000, avgref_rest1, color=rest1_color, linewidth=1.2,
                    label='Rest1', alpha=0.9)
        ax_left.plot(avgref_times * 1000, avgref_rest3, color=rest3_color, linewidth=1.2,
                    label='Rest3', alpha=0.9)
        ax_left.plot(avgref_times * 1000, avgref_diff, color=diff_color, linewidth=1.0,
                    linestyle='--', label='差值', alpha=0.7)

        # 右列：双侧乳突参考
        ax_right = axes[row, 1]
        ax_right.plot(tp9tp10_times * 1000, tp9tp10_rest1, color=rest1_color, linewidth=1.2,
                     label='Rest1', alpha=0.9)
        ax_right.plot(tp9tp10_times * 1000, tp9tp10_rest3, color=rest3_color, linewidth=1.2,
                     label='Rest3', alpha=0.9)
        ax_right.plot(tp9tp10_times * 1000, tp9tp10_diff, color=diff_color, linewidth=1.0,
                     linestyle='--', label='差值', alpha=0.7)

        # 设置子图属性
        for col, (ax, ref_type) in enumerate([(ax_left, '全脑平均参考'), (ax_right, '双侧乳突参考 (TP9/TP10)')]):
            # 设置坐标轴范围 - 根据参考方式使用不同的时间窗口和Y轴范围
            if col == 0:  # 左列：全脑平均参考
                ax.set_xlim(avgref_times[0]*1000, avgref_times[-1]*1000)
                ax.set_ylim(avgref_ylim)
            else:  # 右列：双侧乳突参考
                ax.set_xlim(tp9tp10_times[0]*1000, tp9tp10_times[-1]*1000)
                ax.set_ylim(tp9tp10_ylim)

            # 添加零线
            ax.axhline(0, color='black', linewidth=0.8, alpha=0.6)
            ax.axvline(0, color='black', linewidth=0.8, alpha=0.6)

            # 设置网格
            ax.grid(True, alpha=0.3, linewidth=0.5)

            # 设置标题
            if col == 0:  # 左列
                electrodes_str = ", ".join(avgref_result['electrodes'])
            else:  # 右列
                electrodes_str = ", ".join(tp9tp10_result['electrodes'])

            ax.set_title(f'{region_name} - {ref_type}\n电极: {electrodes_str}',
                        fontsize=11, fontweight='bold')

            # 设置坐标轴标签
            if row == 3:  # 最后一行
                ax.set_xlabel('时间 (ms)', fontsize=10)
            if col == 0:  # 左列
                ax.set_ylabel('幅值 (μV)', fontsize=10)

            # 添加图例（只在第一行显示）
            if row == 0:
                ax.legend(loc='upper right', fontsize=9, framealpha=0.9)

    # 调整子图间距
    plt.tight_layout()
    plt.subplots_adjust(top=0.95, hspace=0.3, wspace=0.2)

    # 保存图片
    output_file = os.path.join(RESULT_DIR, 'hep_reference_comparison_4x2.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"\n对比图表已保存: {output_file}")

    plt.show()

    return fig

def main():
    """主函数"""
    print("=" * 70)
    print("创建HEP波形参考方式对比图表 (4行2列)")
    print("=" * 70)

    try:
        # 加载全脑平均参考数据
        print("\n1. 加载全脑平均参考数据...")
        avgref_results, avgref_times, avgref_name = load_reference_data("avgref")

        # 加载双侧乳突参考数据
        print("\n2. 加载双侧乳突参考数据...")
        tp9tp10_results, tp9tp10_times, tp9tp10_name = load_reference_data("tp9tp10")

        # 创建对比图表
        print("\n3. 创建4行2列对比图表...")
        fig = create_reference_comparison_plot(avgref_results, tp9tp10_results, avgref_times, tp9tp10_times)

        # 输出总结
        print("\n" + "=" * 70)
        print("对比图表创建完成！")
        print("=" * 70)
        print(f"左列: {avgref_name}")
        print(f"右列: {tp9tp10_name}")
        print(f"分析区域: {list(PREFRONTAL_REGIONS.keys())}")
        print(f"时间窗口: {VIS_WINDOW[0]*1000:.0f} 到 {VIS_WINDOW[1]*1000:.0f} ms")
        print(f"结果保存: {RESULT_DIR}")

    except Exception as e:
        print(f"创建对比图表时出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
