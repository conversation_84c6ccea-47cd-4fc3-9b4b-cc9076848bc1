#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建4行4列综合HEP分析图表

功能：
1. 第一列：Rest阶段原始波形对比 (Rest1, Rest2, Rest3)
2. 第二列：Rest阶段差异分析 (Rest2-Rest1, Rest3-Rest1, Rest3-Rest2)
3. 第三列：Test阶段原始波形对比 (Test1, Test2, Test3)
4. 第四列：Test阶段差异分析 (Test2-Test1, Test3-Test1, Test3-Test2)
每行对应一个脑区：左侧前额叶、右侧前额叶、中线前额叶、中央顶区
"""

import os
import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 9
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-NeuroKit2'
DATA_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', '1-全脑平均')
RESULT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'comprehensive')

# 创建结果目录
os.makedirs(RESULT_DIR, exist_ok=True)

# 定义脑区电极分组
BRAIN_REGIONS = {
    '左侧前额叶': ['Fp1', 'AF3', 'AF7', 'F3'],
    '右侧前额叶': ['Fp2', 'AF4', 'AF8', 'F4'],
    '中线前额叶': ['Fpz', 'Fz'],
    '中央顶区': ['Cz', 'CPz', 'Pz', 'POz']
}

# 实验阶段定义
STAGES = ['rest1', 'rest2', 'rest3', 'test1', 'test2', 'test3']
REST_STAGES = ['rest1', 'rest2', 'rest3']
TEST_STAGES = ['test1', 'test2', 'test3']

# 时间窗口设置
VIS_WINDOW = (-0.2, 0.8)       # 可视化窗口
BASELINE_WINDOW = (-0.2, 0.0)  # 基线矫正窗口

# 颜色设置
REST_COLORS = {'rest1': '#2E8B57', 'rest2': '#4169E1', 'rest3': '#DC143C'}  # 绿、蓝、红
TEST_COLORS = {'test1': '#FF8C00', 'test2': '#9932CC', 'test3': '#B22222'}  # 橙、紫、深红
DIFF_COLORS = {
    'rest2-rest1': '#1E90FF', 'rest3-rest1': '#FF6347', 'rest3-rest2': '#32CD32',
    'test2-test1': '#FF69B4', 'test3-test1': '#8A2BE2', 'test3-test2': '#00CED1'
}

def load_hep_data(h5_path, stage_name):
    """加载HEP数据"""
    print(f"正在加载{stage_name}数据: {os.path.basename(h5_path)}")

    with h5py.File(h5_path, 'r') as f:
        data = f['data'][:]
        ch_names = [ch.decode('utf-8').strip() if isinstance(ch, bytes) else str(ch).strip()
                   for ch in f['ch_names'][:]]
        times = f['times'][:]

        if 'subject_ids' in f:
            subject_ids = f['subject_ids'][:]
            subject_ids = [s.decode('utf-8') if isinstance(s, bytes) else str(s)
                          for s in subject_ids]
        else:
            subject_ids = ['unknown'] * data.shape[0]

    print(f"  数据形状: {data.shape}")
    print(f"  时间范围: {times[0]*1000:.1f} to {times[-1]*1000:.1f} ms")
    print(f"  通道数: {len(ch_names)}")

    return data, ch_names, times, subject_ids

def find_latest_file(data_dir, stage):
    """查找指定阶段的最新数据文件"""
    files = [f for f in os.listdir(data_dir) if f.startswith(f'{stage}_raw_epochs_') and f.endswith('.h5')]
    if not files:
        raise FileNotFoundError(f"未找到{stage}阶段的数据文件在目录: {data_dir}")

    files.sort()
    return os.path.join(data_dir, files[-1])

def apply_combined_baseline_correction(stage_data_dict, times):
    """对所有阶段数据应用合并基线矫正（优化版本）"""
    print("应用合并基线矫正...")

    # 找到基线窗口的索引
    baseline_mask = (times >= BASELINE_WINDOW[0]) & (times <= BASELINE_WINDOW[1])
    baseline_indices = np.where(baseline_mask)[0]

    print(f"  基线窗口: {BASELINE_WINDOW[0]*1000:.0f} 到 {BASELINE_WINDOW[1]*1000:.0f} ms")
    print(f"  基线时间点数: {len(baseline_indices)}")

    # 使用采样方式计算基线，避免内存过载
    all_baseline_means = []

    for stage, data in stage_data_dict.items():
        print(f"  处理{stage}阶段基线...")

        # 计算该阶段的基线均值
        baseline_data = data[:, :, baseline_indices]  # (epochs, channels, baseline_times)
        stage_baseline_mean = np.mean(baseline_data, axis=2)  # (epochs, channels)

        # 随机采样一部分epochs来计算全局基线（避免内存问题）
        n_epochs = stage_baseline_mean.shape[0]
        if n_epochs > 1000:
            # 如果epochs太多，随机采样1000个
            sample_indices = np.random.choice(n_epochs, 1000, replace=False)
            sampled_baseline = stage_baseline_mean[sample_indices]
        else:
            sampled_baseline = stage_baseline_mean

        all_baseline_means.append(sampled_baseline)

    # 合并所有采样的基线均值并计算总体基线
    print("  计算全局基线均值...")
    combined_baseline_values = np.concatenate(all_baseline_means, axis=0)
    global_baseline_mean = np.mean(combined_baseline_values, axis=0, keepdims=True)  # (1, channels)
    global_baseline_mean = global_baseline_mean[..., np.newaxis]  # (1, channels, 1)

    print(f"  全局基线形状: {global_baseline_mean.shape}")

    # 应用相同的基线矫正到所有阶段
    corrected_data = {}
    for stage, data in stage_data_dict.items():
        print(f"  应用基线矫正到{stage}阶段...")
        corrected_data[stage] = data - global_baseline_mean

    print("  基线矫正完成")
    return corrected_data

def extract_region_data(data, ch_names, subject_ids, electrodes):
    """提取指定区域的电极数据"""
    # 找到有效的电极
    valid_electrodes = [ch for ch in electrodes if ch in ch_names]
    if not valid_electrodes:
        return None, []

    # 获取电极索引
    electrode_indices = [ch_names.index(ch) for ch in valid_electrodes]

    # 按被试分组数据
    unique_subjects = sorted(set(subject_ids))
    subject_data = {}

    for subj in unique_subjects:
        subj_indices = [i for i, s in enumerate(subject_ids) if s == subj]
        if subj_indices:
            subj_epochs = data[subj_indices][:, electrode_indices, :]
            subject_data[subj] = subj_epochs

    return subject_data, valid_electrodes

def calculate_region_average(subject_data):
    """计算区域平均波形"""
    if subject_data is None:
        return None

    all_data = []
    for subj_data in subject_data.values():
        # 对每个被试的epochs求平均，然后对电极求平均
        subj_avg = np.mean(subj_data, axis=0)  # 对epochs求平均
        region_avg = np.mean(subj_avg, axis=0)  # 对电极求平均
        all_data.append(region_avg)

    # 对所有被试求平均
    grand_avg = np.mean(all_data, axis=0)
    return grand_avg

def load_all_stages_data():
    """加载所有6个阶段的数据"""
    print("=" * 70)
    print("加载所有阶段的HEP数据")
    print("=" * 70)

    # 加载所有阶段的原始数据
    stage_raw_data = {}
    stage_ch_names = {}
    stage_times = {}
    stage_subject_ids = {}

    for stage in STAGES:
        try:
            file_path = find_latest_file(DATA_DIR, stage)
            data, ch_names, times, subject_ids = load_hep_data(file_path, stage)

            stage_raw_data[stage] = data
            stage_ch_names[stage] = ch_names
            stage_times[stage] = times
            stage_subject_ids[stage] = subject_ids

        except Exception as e:
            print(f"警告：无法加载{stage}阶段数据: {str(e)}")
            continue

    if not stage_raw_data:
        raise ValueError("未能加载任何阶段的数据")

    # 使用第一个成功加载的阶段的时间轴作为参考
    reference_times = list(stage_times.values())[0]

    # 应用合并基线矫正
    stage_corrected_data = apply_combined_baseline_correction(stage_raw_data, reference_times)

    return stage_corrected_data, stage_ch_names, reference_times, stage_subject_ids

def analyze_all_regions(stage_corrected_data, stage_ch_names, stage_subject_ids):
    """分析所有脑区的数据"""
    print("\n分析各脑区数据...")

    region_results = {}

    for region_name, electrodes in BRAIN_REGIONS.items():
        print(f"  分析 {region_name} 区域...")

        region_stage_data = {}

        # 对每个阶段提取该区域的数据
        for stage in STAGES:
            if stage not in stage_corrected_data:
                continue

            # 提取区域数据
            region_data, valid_electrodes = extract_region_data(
                stage_corrected_data[stage],
                stage_ch_names[stage],
                stage_subject_ids[stage],
                electrodes
            )

            if region_data is None:
                print(f"    {stage} 阶段数据不可用")
                continue

            # 计算区域平均
            region_avg = calculate_region_average(region_data)
            if region_avg is None:
                print(f"    {stage} 阶段平均计算失败")
                continue

            region_stage_data[stage] = region_avg

        if region_stage_data:
            region_results[region_name] = {
                'electrodes': valid_electrodes,
                'stage_data': region_stage_data
            }
            print(f"    成功分析 {len(region_stage_data)} 个阶段")
        else:
            print(f"    {region_name} 区域无有效数据")

    return region_results

def create_comprehensive_plot(region_results, times):
    """创建4行4列综合分析图表"""
    print("\n创建4行4列综合分析图表...")

    # 创建图形 - 4行4列布局
    fig, axes = plt.subplots(4, 4, figsize=(20, 16))

    # 设置总标题
    fig.suptitle('HEP波形综合分析：Rest vs Test阶段对比',
                 fontsize=16, fontweight='bold', y=0.98)

    # 列标题
    col_titles = [
        'Rest阶段原始波形\n(Rest1, Rest2, Rest3)',
        'Rest阶段差异分析\n(Rest2-Rest1, Rest3-Rest1, Rest3-Rest2)',
        'Test阶段原始波形\n(Test1, Test2, Test3)',
        'Test阶段差异分析\n(Test2-Test1, Test3-Test1, Test3-Test2)'
    ]

    for col, title in enumerate(col_titles):
        axes[0, col].set_title(title, fontsize=12, fontweight='bold', pad=20)

    # 计算各列的Y轴范围
    rest_orig_y_values = []
    rest_diff_y_values = []
    test_orig_y_values = []
    test_diff_y_values = []

    for region_name in BRAIN_REGIONS.keys():
        if region_name not in region_results:
            continue

        stage_data = region_results[region_name]['stage_data']

        # Rest原始波形数据
        for stage in REST_STAGES:
            if stage in stage_data:
                rest_orig_y_values.extend([stage_data[stage].min(), stage_data[stage].max()])

        # Test原始波形数据
        for stage in TEST_STAGES:
            if stage in stage_data:
                test_orig_y_values.extend([stage_data[stage].min(), stage_data[stage].max()])

        # Rest差异数据
        if 'rest1' in stage_data and 'rest2' in stage_data:
            diff = stage_data['rest2'] - stage_data['rest1']
            rest_diff_y_values.extend([diff.min(), diff.max()])
        if 'rest1' in stage_data and 'rest3' in stage_data:
            diff = stage_data['rest3'] - stage_data['rest1']
            rest_diff_y_values.extend([diff.min(), diff.max()])
        if 'rest2' in stage_data and 'rest3' in stage_data:
            diff = stage_data['rest3'] - stage_data['rest2']
            rest_diff_y_values.extend([diff.min(), diff.max()])

        # Test差异数据
        if 'test1' in stage_data and 'test2' in stage_data:
            diff = stage_data['test2'] - stage_data['test1']
            test_diff_y_values.extend([diff.min(), diff.max()])
        if 'test1' in stage_data and 'test3' in stage_data:
            diff = stage_data['test3'] - stage_data['test1']
            test_diff_y_values.extend([diff.min(), diff.max()])
        if 'test2' in stage_data and 'test3' in stage_data:
            diff = stage_data['test3'] - stage_data['test2']
            test_diff_y_values.extend([diff.min(), diff.max()])

    # 计算Y轴范围（添加10%边距）
    def calc_ylim(values):
        if not values:
            return (-1, 1)
        margin = (max(values) - min(values)) * 0.1
        return (min(values) - margin, max(values) + margin)

    rest_orig_ylim = calc_ylim(rest_orig_y_values)
    rest_diff_ylim = calc_ylim(rest_diff_y_values)
    test_orig_ylim = calc_ylim(test_orig_y_values)
    test_diff_ylim = calc_ylim(test_diff_y_values)

    print(f"Y轴范围设置:")
    print(f"  Rest原始波形: {rest_orig_ylim[0]:.3f} to {rest_orig_ylim[1]:.3f} μV")
    print(f"  Rest差异分析: {rest_diff_ylim[0]:.3f} to {rest_diff_ylim[1]:.3f} μV")
    print(f"  Test原始波形: {test_orig_ylim[0]:.3f} to {test_orig_ylim[1]:.3f} μV")
    print(f"  Test差异分析: {test_diff_ylim[0]:.3f} to {test_diff_ylim[1]:.3f} μV")

    # 绘制每个脑区的数据
    for row, region_name in enumerate(BRAIN_REGIONS.keys()):
        if region_name not in region_results:
            # 如果该区域没有数据，显示空白图
            for col in range(4):
                axes[row, col].text(0.5, 0.5, f'{region_name}\n数据不可用',
                                   ha='center', va='center', transform=axes[row, col].transAxes)
                axes[row, col].set_xlim(times[0]*1000, times[-1]*1000)
            continue

        stage_data = region_results[region_name]['stage_data']

        # 第一列：Rest阶段原始波形
        ax = axes[row, 0]
        for stage in REST_STAGES:
            if stage in stage_data:
                ax.plot(times * 1000, stage_data[stage],
                       color=REST_COLORS[stage], linewidth=1.2,
                       label=stage.upper(), alpha=0.9)

        ax.set_xlim(times[0]*1000, times[-1]*1000)
        ax.set_ylim(rest_orig_ylim)
        ax.axhline(0, color='black', linewidth=0.5, alpha=0.5)
        ax.axvline(0, color='black', linewidth=0.5, alpha=0.5)
        ax.grid(True, alpha=0.3)

        if row == 0:
            ax.legend(loc='upper right', fontsize=8)
        if row == len(BRAIN_REGIONS) - 1:
            ax.set_xlabel('时间 (ms)')
        ax.set_ylabel(f'{region_name}\n幅值 (μV)')

        # 第二列：Rest阶段差异分析
        ax = axes[row, 1]
        if 'rest1' in stage_data and 'rest2' in stage_data:
            diff = stage_data['rest2'] - stage_data['rest1']
            ax.plot(times * 1000, diff, color=DIFF_COLORS['rest2-rest1'],
                   linewidth=1.0, linestyle='--', label='Rest2-Rest1', alpha=0.8)

        if 'rest1' in stage_data and 'rest3' in stage_data:
            diff = stage_data['rest3'] - stage_data['rest1']
            ax.plot(times * 1000, diff, color=DIFF_COLORS['rest3-rest1'],
                   linewidth=1.0, linestyle='--', label='Rest3-Rest1', alpha=0.8)

        if 'rest2' in stage_data and 'rest3' in stage_data:
            diff = stage_data['rest3'] - stage_data['rest2']
            ax.plot(times * 1000, diff, color=DIFF_COLORS['rest3-rest2'],
                   linewidth=1.0, linestyle='--', label='Rest3-Rest2', alpha=0.8)

        ax.set_xlim(times[0]*1000, times[-1]*1000)
        ax.set_ylim(rest_diff_ylim)
        ax.axhline(0, color='black', linewidth=0.5, alpha=0.5)
        ax.axvline(0, color='black', linewidth=0.5, alpha=0.5)
        ax.grid(True, alpha=0.3)

        if row == 0:
            ax.legend(loc='upper right', fontsize=8)
        if row == len(BRAIN_REGIONS) - 1:
            ax.set_xlabel('时间 (ms)')
        ax.set_ylabel('差异幅值 (μV)')

        # 第三列：Test阶段原始波形
        ax = axes[row, 2]
        for stage in TEST_STAGES:
            if stage in stage_data:
                ax.plot(times * 1000, stage_data[stage],
                       color=TEST_COLORS[stage], linewidth=1.2,
                       label=stage.upper(), alpha=0.9)

        ax.set_xlim(times[0]*1000, times[-1]*1000)
        ax.set_ylim(test_orig_ylim)
        ax.axhline(0, color='black', linewidth=0.5, alpha=0.5)
        ax.axvline(0, color='black', linewidth=0.5, alpha=0.5)
        ax.grid(True, alpha=0.3)

        if row == 0:
            ax.legend(loc='upper right', fontsize=8)
        if row == len(BRAIN_REGIONS) - 1:
            ax.set_xlabel('时间 (ms)')
        ax.set_ylabel('幅值 (μV)')

        # 第四列：Test阶段差异分析
        ax = axes[row, 3]
        if 'test1' in stage_data and 'test2' in stage_data:
            diff = stage_data['test2'] - stage_data['test1']
            ax.plot(times * 1000, diff, color=DIFF_COLORS['test2-test1'],
                   linewidth=1.0, linestyle='--', label='Test2-Test1', alpha=0.8)

        if 'test1' in stage_data and 'test3' in stage_data:
            diff = stage_data['test3'] - stage_data['test1']
            ax.plot(times * 1000, diff, color=DIFF_COLORS['test3-test1'],
                   linewidth=1.0, linestyle='--', label='Test3-Test1', alpha=0.8)

        if 'test2' in stage_data and 'test3' in stage_data:
            diff = stage_data['test3'] - stage_data['test2']
            ax.plot(times * 1000, diff, color=DIFF_COLORS['test3-test2'],
                   linewidth=1.0, linestyle='--', label='Test3-Test2', alpha=0.8)

        ax.set_xlim(times[0]*1000, times[-1]*1000)
        ax.set_ylim(test_diff_ylim)
        ax.axhline(0, color='black', linewidth=0.5, alpha=0.5)
        ax.axvline(0, color='black', linewidth=0.5, alpha=0.5)
        ax.grid(True, alpha=0.3)

        if row == 0:
            ax.legend(loc='upper right', fontsize=8)
        if row == len(BRAIN_REGIONS) - 1:
            ax.set_xlabel('时间 (ms)')
        ax.set_ylabel('差异幅值 (μV)')

    # 调整布局
    plt.subplots_adjust(top=0.94, hspace=0.3, wspace=0.3)

    # 保存图片
    output_file = os.path.join(RESULT_DIR, 'hep_comprehensive_analysis_4x4.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"\n综合分析图表已保存: {output_file}")

    plt.show()

    return fig

def create_single_analysis_plot(region_results, times, analysis_type):
    """创建单个分析图表
    
    参数:
    analysis_type: 'rest_orig', 'rest_diff', 'test_orig', 'test_diff'
    """
    print(f"\n创建{analysis_type}分析图表...")

    # 创建图形 - 4行1列布局
    fig, axes = plt.subplots(4, 1, figsize=(8, 16))
    
    # 设置标题
    titles = {
        'rest_orig': 'Rest阶段原始波形对比 (Rest1, Rest2, Rest3)',
        'rest_diff': 'Rest阶段差异分析 (Rest2-Rest1, Rest3-Rest1, Rest3-Rest2)',
        'test_orig': 'Test阶段原始波形对比 (Test1, Test2, Test3)',
        'test_diff': 'Test阶段差异分析 (Test2-Test1, Test3-Test1, Test3-Test2)'
    }
    fig.suptitle(titles[analysis_type], fontsize=14, fontweight='bold', y=0.98)

    # 计算Y轴范围
    y_values = []
    for region_name in BRAIN_REGIONS.keys():
        if region_name not in region_results:
            continue

        stage_data = region_results[region_name]['stage_data']

        if analysis_type == 'rest_orig':
            for stage in REST_STAGES:
                if stage in stage_data:
                    y_values.extend([stage_data[stage].min(), stage_data[stage].max()])
        elif analysis_type == 'test_orig':
            for stage in TEST_STAGES:
                if stage in stage_data:
                    y_values.extend([stage_data[stage].min(), stage_data[stage].max()])
        elif analysis_type == 'rest_diff':
            if 'rest1' in stage_data and 'rest2' in stage_data:
                diff = stage_data['rest2'] - stage_data['rest1']
                y_values.extend([diff.min(), diff.max()])
            if 'rest1' in stage_data and 'rest3' in stage_data:
                diff = stage_data['rest3'] - stage_data['rest1']
                y_values.extend([diff.min(), diff.max()])
            if 'rest2' in stage_data and 'rest3' in stage_data:
                diff = stage_data['rest3'] - stage_data['rest2']
                y_values.extend([diff.min(), diff.max()])
        elif analysis_type == 'test_diff':
            if 'test1' in stage_data and 'test2' in stage_data:
                diff = stage_data['test2'] - stage_data['test1']
                y_values.extend([diff.min(), diff.max()])
            if 'test1' in stage_data and 'test3' in stage_data:
                diff = stage_data['test3'] - stage_data['test1']
                y_values.extend([diff.min(), diff.max()])
            if 'test2' in stage_data and 'test3' in stage_data:
                diff = stage_data['test3'] - stage_data['test2']
                y_values.extend([diff.min(), diff.max()])

    # 计算Y轴范围（添加10%边距）
    margin = (max(y_values) - min(y_values)) * 0.1
    ylim = (min(y_values) - margin, max(y_values) + margin)

    # 绘制每个脑区的数据
    for row, region_name in enumerate(BRAIN_REGIONS.keys()):
        if region_name not in region_results:
            axes[row].text(0.5, 0.5, f'{region_name}\n数据不可用',
                          ha='center', va='center', transform=axes[row].transAxes)
            axes[row].set_xlim(times[0]*1000, times[-1]*1000)
            continue

        stage_data = region_results[region_name]['stage_data']
        ax = axes[row]

        if analysis_type == 'rest_orig':
            for stage in REST_STAGES:
                if stage in stage_data:
                    ax.plot(times * 1000, stage_data[stage],
                           color=REST_COLORS[stage], linewidth=1.2,
                           label=stage.upper(), alpha=0.9)
            if row == 0:
                ax.legend(loc='upper right', fontsize=8)

        elif analysis_type == 'test_orig':
            for stage in TEST_STAGES:
                if stage in stage_data:
                    ax.plot(times * 1000, stage_data[stage],
                           color=TEST_COLORS[stage], linewidth=1.2,
                           label=stage.upper(), alpha=0.9)
            if row == 0:
                ax.legend(loc='upper right', fontsize=8)

        elif analysis_type == 'rest_diff':
            if 'rest1' in stage_data and 'rest2' in stage_data:
                diff = stage_data['rest2'] - stage_data['rest1']
                ax.plot(times * 1000, diff, color=DIFF_COLORS['rest2-rest1'],
                       linewidth=1.0, label='Rest2-Rest1', alpha=0.8)
            if 'rest1' in stage_data and 'rest3' in stage_data:
                diff = stage_data['rest3'] - stage_data['rest1']
                ax.plot(times * 1000, diff, color=DIFF_COLORS['rest3-rest1'],
                       linewidth=1.0, label='Rest3-Rest1', alpha=0.8)
            if 'rest2' in stage_data and 'rest3' in stage_data:
                diff = stage_data['rest3'] - stage_data['rest2']
                ax.plot(times * 1000, diff, color=DIFF_COLORS['rest3-rest2'],
                       linewidth=1.0, label='Rest3-Rest2', alpha=0.8)
            if row == 0:
                ax.legend(loc='upper right', fontsize=8)

        elif analysis_type == 'test_diff':
            if 'test1' in stage_data and 'test2' in stage_data:
                diff = stage_data['test2'] - stage_data['test1']
                ax.plot(times * 1000, diff, color=DIFF_COLORS['test2-test1'],
                       linewidth=1.0, label='Test2-Test1', alpha=0.8)
            if 'test1' in stage_data and 'test3' in stage_data:
                diff = stage_data['test3'] - stage_data['test1']
                ax.plot(times * 1000, diff, color=DIFF_COLORS['test3-test1'],
                       linewidth=1.0, label='Test3-Test1', alpha=0.8)
            if 'test2' in stage_data and 'test3' in stage_data:
                diff = stage_data['test3'] - stage_data['test2']
                ax.plot(times * 1000, diff, color=DIFF_COLORS['test3-test2'],
                       linewidth=1.0, label='Test3-Test2', alpha=0.8)
            if row == 0:
                ax.legend(loc='upper right', fontsize=8)

        ax.set_xlim(times[0]*1000, times[-1]*1000)
        ax.set_ylim(ylim)
        ax.axhline(0, color='black', linewidth=0.5, alpha=0.5)
        ax.axvline(0, color='black', linewidth=0.5, alpha=0.5)
        ax.grid(True, alpha=0.3)

        if row == len(BRAIN_REGIONS) - 1:
            ax.set_xlabel('时间 (ms)')
        ax.set_ylabel(f'{region_name}\n幅值 (μV)')

    # 调整布局
    plt.subplots_adjust(top=0.94, hspace=0.3)

    # 保存图片
    output_file = os.path.join(RESULT_DIR, f'hep_{analysis_type}_analysis.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"\n{analysis_type}分析图表已保存: {output_file}")

    plt.show()
    return fig

def main():
    """主函数"""
    print("=" * 70)
    print("创建HEP波形分析图表")
    print("=" * 70)

    try:
        # 1. 加载所有阶段数据
        stage_corrected_data, stage_ch_names, times, stage_subject_ids = load_all_stages_data()

        # 2. 分析所有脑区
        region_results = analyze_all_regions(stage_corrected_data, stage_ch_names, stage_subject_ids)

        if not region_results:
            print("错误：没有成功分析任何脑区的数据")
            return

        # 3. 创建各个分析图表
        analysis_types = ['rest_orig', 'rest_diff', 'test_orig', 'test_diff']
        for analysis_type in analysis_types:
            fig = create_single_analysis_plot(region_results, times, analysis_type)

        # 4. 输出总结
        print("\n" + "=" * 70)
        print("分析图表创建完成！")
        print("=" * 70)
        print(f"分析的脑区: {list(region_results.keys())}")
        print(f"包含的阶段: {STAGES}")
        print(f"时间窗口: {times[0]*1000:.0f} 到 {times[-1]*1000:.0f} ms")
        print(f"结果保存: {RESULT_DIR}")
        print("\n生成的图表:")
        print("  1. Rest阶段原始波形对比")
        print("  2. Rest阶段差异分析")
        print("  3. Test阶段原始波形对比")
        print("  4. Test阶段差异分析")

    except Exception as e:
        print(f"创建分析图表时出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
