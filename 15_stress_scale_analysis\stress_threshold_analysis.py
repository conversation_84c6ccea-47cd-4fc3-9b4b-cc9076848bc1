#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HEP分析实验 - 压力状态评估模块

功能：
- 基于HEP特征建立压力状态评估标准
- 使用ROC曲线确定最佳阈值
- 开发个体压力状态评估算法
- 使用留一法交叉验证评估算法性能
- 生成个体压力状态评估报告

作者：AI助手
日期：2024年
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.metrics import roc_curve, auc, confusion_matrix, classification_report
from sklearn.model_selection import LeaveOneOut
from sklearn.linear_model import LogisticRegression
import argparse
from datetime import datetime
import glob

# 设置matplotlib参数
plt.rcParams.update({
    'font.family': 'LXGW WenKai',  # 使用LXGW WenKai字体显示中文
    'font.size': 10,
    'axes.titlesize': 12,
    'axes.labelsize': 11,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 9,
    'figure.titlesize': 14,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'axes.grid': True,
    'grid.alpha': 0.3,
    'grid.linestyle': '--',
    'axes.axisbelow': True
})

# 定义常量
ROOT_DIR = r"D:/ecgeeg/30-数据分析/5-NeuroKit2/result"
FEATURES_DIR = os.path.join(ROOT_DIR, 'hep_analysis', 'features')
RESULTS_DIR = os.path.join(ROOT_DIR, 'hep_analysis', 'results')
OUTPUT_DIR = os.path.join(ROOT_DIR, 'hep_analysis', 'stress_assessment')

# 定义关键通道
KEY_CHANNELS = ['Fz', 'Cz', 'Pz', 'FCz', 'CPz']

# 定义阶段分组
REST_STAGES = ['rest1', 'rest2', 'rest3']
TEST_STAGES = ['test1', 'test2', 'test3']

# 定义阶段显示名称
STAGE_DISPLAY = {
    'prac': '练习阶段',
    'rest1': '静息态1',
    'test1': '刺激态1',
    'rest2': '静息态2',
    'test2': '刺激态2',
    'rest3': '静息态3',
    'test3': '刺激态3',
    'rest': '静息态',
    'test': '刺激态'
}

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='HEP压力状态评估')
    parser.add_argument('--features_dir', type=str, default=FEATURES_DIR,
                        help='特征目录')
    parser.add_argument('--results_dir', type=str, default=RESULTS_DIR,
                        help='结果目录')
    parser.add_argument('--output_dir', type=str, default=OUTPUT_DIR,
                        help='输出目录')
    parser.add_argument('--best_window', type=str, default=None,
                        help='最佳时间窗口，格式为"start-end"，如"0.2-0.4"')
    parser.add_argument('--channels', type=str, default=','.join(KEY_CHANNELS),
                        help='关键通道列表，用逗号分隔')
    return parser.parse_args()

def load_best_window_results(results_dir):
    """
    加载最佳时间窗口的结果

    参数:
    results_dir (str): 结果目录

    返回:
    tuple: (最佳时间窗口, 结果DataFrame)
    """
    print("加载最佳时间窗口的结果...")

    # 确保结果目录存在
    if not os.path.exists(results_dir):
        print(f"错误: 结果目录不存在: {results_dir}")
        exit(1)

    # 查找最新的结果文件
    result_files = glob.glob(os.path.join(results_dir, 'best_window_results_*.csv'))

    if not result_files:
        print(f"错误: 未找到最佳时间窗口的结果文件")
        exit(1)

    # 按修改时间排序，选择最新的文件
    latest_file = max(result_files, key=os.path.getmtime)

    # 加载结果
    results = pd.read_csv(latest_file)

    if results.empty:
        print(f"错误: 结果文件为空: {latest_file}")
        exit(1)

    # 从结果中提取时间窗口
    if 'window_start' not in results.columns or 'window_end' not in results.columns:
        print(f"错误: 结果文件缺少必要的列: {latest_file}")
        exit(1)

    window_start = results['window_start'].iloc[0]
    window_end = results['window_end'].iloc[0]

    print(f"加载最佳时间窗口: {window_start}-{window_end}s")

    return (window_start, window_end), results

def load_features_for_window(features_dir, window):
    """
    加载指定时间窗口的特征

    参数:
    features_dir (str): 特征目录
    window (tuple): 时间窗口

    返回:
    tuple: (静息态特征, 刺激态特征)
    """
    print(f"加载时间窗口 {window[0]}-{window[1]}s 的特征...")

    window_start, window_end = window

    # 查找所有聚合特征文件
    agg_files = glob.glob(os.path.join(features_dir, '*_agg_features_*.csv'))

    # 加载静息态特征
    rest_features = []
    for stage in REST_STAGES:
        stage_files = [f for f in agg_files if f.startswith(os.path.join(features_dir, f'{stage}_agg_features'))]

        if stage_files:
            # 按修改时间排序，选择最新的文件
            latest_file = max(stage_files, key=os.path.getmtime)

            # 加载特征
            df = pd.read_csv(latest_file)

            # 筛选指定时间窗口的数据
            window_df = df[(df['window_start'] == window_start) & (df['window_end'] == window_end)]

            if not window_df.empty:
                window_df['condition'] = 'rest'
                rest_features.append(window_df)

    if not rest_features:
        print("错误: 未找到静息态特征")
        exit(1)

    rest_df = pd.concat(rest_features, ignore_index=True)
    print(f"加载静息态特征，共{len(rest_df)}条记录")

    # 加载刺激态特征
    test_features = []
    for stage in TEST_STAGES:
        stage_files = [f for f in agg_files if f.startswith(os.path.join(features_dir, f'{stage}_agg_features'))]

        if stage_files:
            # 按修改时间排序，选择最新的文件
            latest_file = max(stage_files, key=os.path.getmtime)

            # 加载特征
            df = pd.read_csv(latest_file)

            # 筛选指定时间窗口的数据
            window_df = df[(df['window_start'] == window_start) & (df['window_end'] == window_end)]

            if not window_df.empty:
                window_df['condition'] = 'test'
                test_features.append(window_df)

    if not test_features:
        print("错误: 未找到刺激态特征")
        exit(1)

    test_df = pd.concat(test_features, ignore_index=True)
    print(f"加载刺激态特征，共{len(test_df)}条记录")

    return rest_df, test_df

def prepare_paired_data(rest_df, test_df, channels):
    """
    准备配对数据

    参数:
    rest_df (pd.DataFrame): 静息态特征
    test_df (pd.DataFrame): 刺激态特征
    channels (list): 通道列表

    返回:
    pd.DataFrame: 配对数据
    """
    print("准备配对数据...")

    # 创建配对数据列表
    paired_data = []

    # 获取所有被试ID
    subject_ids = set(rest_df['subject_id'].unique()) & set(test_df['subject_id'].unique())

    for subject_id in subject_ids:
        # 筛选该被试的数据
        rest_subj = rest_df[rest_df['subject_id'] == subject_id]
        test_subj = test_df[test_df['subject_id'] == subject_id]

        # 对每个通道进行处理
        for channel in channels:
            # 筛选该通道的数据
            rest_channel = rest_subj[rest_subj['channel'] == channel]
            test_channel = test_subj[test_subj['channel'] == channel]

            if not rest_channel.empty and not test_channel.empty:
                # 计算平均值
                rest_mean = rest_channel['mean_amplitude_mean'].mean()
                test_mean = test_channel['mean_amplitude_mean'].mean()

                paired_data.append({
                    'subject_id': subject_id,
                    'channel': channel,
                    'rest_mean': rest_mean,
                    'test_mean': test_mean,
                    'difference': test_mean - rest_mean
                })

    # 转换为DataFrame
    paired_df = pd.DataFrame(paired_data)

    print(f"准备配对数据，共{len(paired_df)}条记录，来自{len(subject_ids)}个被试")

    return paired_df

def establish_threshold(paired_df):
    """
    建立阈值标准

    参数:
    paired_df (pd.DataFrame): 配对数据

    返回:
    dict: 包含阈值标准的字典
    """
    print("建立阈值标准...")

    # 计算每个被试的平均差异值
    subject_diffs = paired_df.groupby('subject_id')['difference'].mean().reset_index()

    # 使用ROC曲线确定最佳阈值
    # 假设差异值大于阈值表示处于刺激态
    y_true = np.ones(len(subject_diffs))  # 理想情况下，所有被试都应该有差异
    y_scores = subject_diffs['difference'].values

    fpr, tpr, thresholds = roc_curve(y_true, y_scores)
    roc_auc = auc(fpr, tpr)

    # 找到最佳阈值 - 最大化(真阳性率-假阳性率)
    optimal_idx = np.argmax(tpr - fpr)
    optimal_threshold = thresholds[optimal_idx]

    # 计算分类准确率
    predicted = (subject_diffs['difference'] > optimal_threshold).astype(int)
    accuracy = (predicted == y_true).mean()

    threshold_results = {
        'optimal_threshold': optimal_threshold,
        'accuracy': accuracy,
        'roc_auc': roc_auc,
        'fpr': fpr,
        'tpr': tpr,
        'thresholds': thresholds,
        'subject_diffs': subject_diffs
    }

    print(f"最佳阈值: {optimal_threshold:.6f}")
    print(f"分类准确率: {accuracy:.2%}")
    print(f"ROC AUC: {roc_auc:.3f}")

    return threshold_results

def develop_assessment_algorithm(paired_df, channels):
    """
    开发压力状态评估算法

    参数:
    paired_df (pd.DataFrame): 配对数据
    channels (list): 通道列表

    返回:
    dict: 包含算法参数和性能指标的字典
    """
    print("开发压力状态评估算法...")

    # 准备特征矩阵
    X = []
    y = []
    subject_ids = []

    # 获取所有被试ID
    for subject_id in paired_df['subject_id'].unique():
        # 筛选该被试的数据
        subj_data = paired_df[paired_df['subject_id'] == subject_id]

        # 创建特征向量
        features = []

        for channel in channels:
            # 筛选该通道的数据
            channel_data = subj_data[subj_data['channel'] == channel]

            if not channel_data.empty:
                # 添加特征
                features.append(channel_data['difference'].values[0])
            else:
                # 如果没有该通道的数据，使用0填充
                features.append(0)

        # 添加到特征矩阵
        X.append(features)
        y.append(1)  # 1表示能够区分静息态和刺激态
        subject_ids.append(subject_id)

    # 转换为numpy数组
    X = np.array(X)
    y = np.array(y)

    # 使用留一法交叉验证评估算法性能
    loo = LeaveOneOut()
    predictions = []
    probabilities = []

    for train_idx, test_idx in loo.split(X):
        X_train, X_test = X[train_idx], X[test_idx]
        y_train, y_test = y[train_idx], y[test_idx]

        # 训练逻辑回归模型
        model = LogisticRegression(random_state=42)
        model.fit(X_train, y_train)

        # 预测
        y_pred = model.predict(X_test)
        y_prob = model.predict_proba(X_test)[:, 1]

        predictions.append(y_pred[0])
        probabilities.append(y_prob[0])

    # 计算准确率
    accuracy = np.mean(predictions == y)

    # 训练最终模型
    final_model = LogisticRegression(random_state=42)
    final_model.fit(X, y)

    # 计算ROC曲线
    y_scores = final_model.predict_proba(X)[:, 1]
    fpr, tpr, thresholds = roc_curve(y, y_scores)
    roc_auc = auc(fpr, tpr)

    # 计算混淆矩阵
    y_pred = final_model.predict(X)
    cm = confusion_matrix(y, y_pred)

    # 计算分类报告
    report = classification_report(y, y_pred, output_dict=True)

    # 创建结果DataFrame
    results_df = pd.DataFrame({
        'subject_id': subject_ids,
        'true_label': y,
        'predicted_label': predictions,
        'probability': probabilities
    })

    algorithm_results = {
        'model': final_model,
        'channels': channels,
        'accuracy': accuracy,
        'roc_auc': roc_auc,
        'fpr': fpr,
        'tpr': tpr,
        'thresholds': thresholds,
        'confusion_matrix': cm,
        'classification_report': report,
        'results': results_df,
        'feature_importance': final_model.coef_[0]
    }

    print(f"算法准确率: {accuracy:.2%}")
    print(f"ROC AUC: {roc_auc:.3f}")

    return algorithm_results

def visualize_threshold(threshold_results, output_dir):
    """
    可视化阈值标准

    参数:
    threshold_results (dict): 阈值结果
    output_dir (str): 输出目录
    """
    print("可视化阈值标准...")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 1. 绘制ROC曲线
    plt.figure(figsize=(10, 8))

    # 绘制ROC曲线
    plt.plot(threshold_results['fpr'], threshold_results['tpr'], color='#E63946', lw=2,
             label=f"ROC曲线 (AUC = {threshold_results['roc_auc']:.3f})")

    # 绘制对角线
    plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')

    # 标记最佳阈值点
    optimal_idx = np.argmax(threshold_results['tpr'] - threshold_results['fpr'])
    optimal_threshold = threshold_results['thresholds'][optimal_idx]
    optimal_fpr = threshold_results['fpr'][optimal_idx]
    optimal_tpr = threshold_results['tpr'][optimal_idx]

    plt.plot(optimal_fpr, optimal_tpr, 'o', markersize=10, color='#457B9D',
             label=f"最佳阈值: {optimal_threshold:.6f}")

    # 设置图表元素
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('假阳性率')
    plt.ylabel('真阳性率')
    plt.title('HEP差异阈值的ROC曲线')
    plt.legend(loc="lower right")
    plt.grid(True)
    plt.tight_layout()

    # 保存图表
    plt.savefig(os.path.join(output_dir, 'hep_threshold_roc.png'))
    plt.close()

    # 2. 绘制被试差异值分布
    plt.figure(figsize=(12, 8))

    # 绘制直方图
    sns.histplot(threshold_results['subject_diffs']['difference'], bins=15, kde=True, color='#457B9D')

    # 绘制阈值线
    plt.axvline(x=optimal_threshold, color='#E63946', linestyle='--', linewidth=2,
                label=f"阈值: {optimal_threshold:.6f}")

    # 设置图表元素
    plt.xlabel('HEP差异值 (刺激态 - 静息态)')
    plt.ylabel('被试数量')
    plt.title('被试HEP差异值分布')
    plt.legend()
    plt.grid(True)
    plt.tight_layout()

    # 保存图表
    plt.savefig(os.path.join(output_dir, 'hep_difference_distribution.png'))
    plt.close()

def visualize_algorithm(algorithm_results, output_dir):
    """
    可视化评估算法

    参数:
    algorithm_results (dict): 算法结果
    output_dir (str): 输出目录
    """
    print("可视化评估算法...")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 1. 绘制ROC曲线
    plt.figure(figsize=(10, 8))

    # 绘制ROC曲线
    plt.plot(algorithm_results['fpr'], algorithm_results['tpr'], color='#E63946', lw=2,
             label=f"ROC曲线 (AUC = {algorithm_results['roc_auc']:.3f})")

    # 绘制对角线
    plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')

    # 设置图表元素
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('假阳性率')
    plt.ylabel('真阳性率')
    plt.title('压力状态评估算法的ROC曲线')
    plt.legend(loc="lower right")
    plt.grid(True)
    plt.tight_layout()

    # 保存图表
    plt.savefig(os.path.join(output_dir, 'assessment_algorithm_roc.png'))
    plt.close()

    # 2. 绘制特征重要性
    plt.figure(figsize=(12, 8))

    # 准备数据
    channels = algorithm_results['channels']
    importance = algorithm_results['feature_importance']

    # 绘制条形图
    plt.bar(channels, importance, color='#457B9D')

    # 设置图表元素
    plt.xlabel('通道')
    plt.ylabel('特征重要性')
    plt.title('压力状态评估算法的特征重要性')
    plt.grid(True)
    plt.tight_layout()

    # 保存图表
    plt.savefig(os.path.join(output_dir, 'feature_importance.png'))
    plt.close()

    # 3. 绘制混淆矩阵
    plt.figure(figsize=(8, 6))

    # 绘制混淆矩阵
    sns.heatmap(algorithm_results['confusion_matrix'], annot=True, fmt='d', cmap='Blues',
                xticklabels=['非压力', '压力'], yticklabels=['非压力', '压力'])

    # 设置图表元素
    plt.xlabel('预测标签')
    plt.ylabel('真实标签')
    plt.title('压力状态评估算法的混淆矩阵')
    plt.tight_layout()

    # 保存图表
    plt.savefig(os.path.join(output_dir, 'confusion_matrix.png'))
    plt.close()

def save_results(results, output_dir, filename):
    """
    保存结果

    参数:
    results (pd.DataFrame): 结果
    output_dir (str): 输出目录
    filename (str): 文件名
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 构建文件路径
    file_path = os.path.join(output_dir, filename)

    # 保存为CSV
    results.to_csv(file_path, index=False)
    print(f"已保存结果至: {file_path}")

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 解析通道列表
    channels = args.channels.split(',')

    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)

    # 加载最佳时间窗口的结果
    if args.best_window:
        # 使用命令行参数指定的时间窗口
        start, end = map(float, args.best_window.split('-'))
        best_window = (start, end)
        print(f"使用指定的时间窗口: {start}-{end}s")
    else:
        # 加载最佳时间窗口的结果
        best_window, _ = load_best_window_results(args.results_dir)

    # 加载指定时间窗口的特征
    rest_df, test_df = load_features_for_window(args.features_dir, best_window)

    # 准备配对数据
    paired_df = prepare_paired_data(rest_df, test_df, channels)

    # 建立阈值标准
    threshold_results = establish_threshold(paired_df)

    # 开发压力状态评估算法
    algorithm_results = develop_assessment_algorithm(paired_df, channels)

    # 可视化阈值标准
    visualize_threshold(threshold_results, args.output_dir)

    # 可视化评估算法
    visualize_algorithm(algorithm_results, args.output_dir)

    # 保存结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    save_results(paired_df, args.output_dir, f'paired_data_{timestamp}.csv')
    save_results(threshold_results['subject_diffs'], args.output_dir, f'subject_differences_{timestamp}.csv')
    save_results(algorithm_results['results'], args.output_dir, f'algorithm_results_{timestamp}.csv')

    print("压力状态评估完成")

if __name__ == "__main__":
    main()
