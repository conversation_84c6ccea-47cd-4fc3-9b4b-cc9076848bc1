#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证新生成的双侧乳突参考HEP数据文件
"""

import os
import h5py
import numpy as np
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 文件路径
TP9TP10_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\13_raw_epochs\tp9tp10"

def verify_file_structure(file_path):
    """验证单个文件的结构"""
    logging.info(f"\n验证文件: {os.path.basename(file_path)}")
    
    with h5py.File(file_path, 'r') as f:
        # 检查数据集
        logging.info("数据集:")
        for key in f.keys():
            dataset = f[key]
            logging.info(f"  {key}: {dataset.shape}, {dataset.dtype}")
            
            if key == 'data':
                logging.info(f"    数据范围: {np.min(dataset[:])} 到 {np.max(dataset[:])}")
            elif key == 'times':
                times = dataset[:]
                logging.info(f"    时间范围: {times[0]:.3f}s 到 {times[-1]:.3f}s")
                logging.info(f"    时间点数: {len(times)}")
                if len(times) > 1:
                    logging.info(f"    采样间隔: {times[1] - times[0]:.6f}s")
            elif key == 'ch_names':
                ch_names = [ch.decode('utf-8') if isinstance(ch, bytes) else str(ch) for ch in dataset[:]]
                logging.info(f"    通道数: {len(ch_names)}")
                logging.info(f"    前5个通道: {ch_names[:5]}")
            elif key == 'stages':
                stages = [s.decode('utf-8') if isinstance(s, bytes) else str(s) for s in dataset[:5]]
                logging.info(f"    前5个阶段: {stages}")
            elif key == 'subject_ids':
                subjects = [s.decode('utf-8') if isinstance(s, bytes) else str(s) for s in dataset[:5]]
                logging.info(f"    前5个被试ID: {subjects}")
        
        # 检查属性
        logging.info("根级别属性:")
        for attr_name in f.attrs.keys():
            attr_value = f.attrs[attr_name]
            logging.info(f"  {attr_name}: {attr_value} ({type(attr_value).__name__})")

def main():
    """主函数"""
    logging.info("验证新生成的双侧乳突参考HEP数据文件")
    
    # 获取所有新生成的文件（排除测试文件）
    files = [f for f in os.listdir(TP9TP10_DIR) 
             if f.endswith('.h5') and not f.startswith('test_')]
    
    if not files:
        logging.error("未找到新生成的文件")
        return False
    
    logging.info(f"找到 {len(files)} 个文件:")
    for f in sorted(files):
        logging.info(f"  - {f}")
    
    # 验证每个文件
    for file_name in sorted(files):
        file_path = os.path.join(TP9TP10_DIR, file_name)
        try:
            verify_file_structure(file_path)
        except Exception as e:
            logging.error(f"验证文件 {file_name} 时出错: {str(e)}")
    
    # 统计总结
    logging.info("\n" + "=" * 60)
    logging.info("验证总结")
    logging.info("=" * 60)
    
    # 统计总epochs数
    total_epochs = 0
    for file_name in sorted(files):
        file_path = os.path.join(TP9TP10_DIR, file_name)
        with h5py.File(file_path, 'r') as f:
            n_epochs = f.attrs['n_epochs']
            stage = f.attrs['stage']
            total_epochs += n_epochs
            logging.info(f"{stage}: {n_epochs} epochs")
    
    logging.info(f"总epochs数: {total_epochs}")
    logging.info("验证完成！")
    
    return True

if __name__ == "__main__":
    main()
