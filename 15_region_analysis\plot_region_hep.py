#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
脑区HEP波形分析脚本

功能：
1. 读取所有阶段的HEP数据
2. 按脑区分组绘制波形图
3. 每个脑区一张图，包含该脑区所有电极的波形
4. 7个阶段分别绘制7张图
"""

import os
import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-NeuroKit2'
DATA_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', 'tp9tp10')
RESULT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '15_region_analysis')

# 创建结果目录
os.makedirs(RESULT_DIR, exist_ok=True)

# 定义脑区电极分组
REGIONS = {
    '前额叶': ['Fp1', 'Fp2', 'Fpz', 'AF3', 'AF4', 'AF7', 'AF8', 'F3', 'F4', 'Fz'],
    '额叶': ['F1', 'F2', 'F5', 'F6', 'F7', 'F8', 'FC1', 'FC2', 'FC3', 'FC4', 'FC5', 'FC6'],
    '中央区': ['C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'Cz', 'CP1', 'CP2', 'CP3', 'CP4', 'CP5', 'CP6', 'CPz'],
    '顶叶': ['P1', 'P2', 'P3', 'P4', 'P5', 'P6', 'P7', 'P8', 'Pz', 'PO3', 'PO4', 'PO7', 'PO8', 'POz'],
    '颞叶': ['T7', 'T8', 'FT7', 'FT8', 'TP7', 'TP8'],
    '枕叶': ['O1', 'O2', 'Oz']
}

# 实验阶段
STAGES = ['prac', 'rest1', 'test1', 'rest2', 'test2', 'rest3', 'test3']

# 颜色设置
COLORS = {
    'prac': '#1f77b4',    # 蓝色
    'rest1': '#2ca02c',   # 绿色
    'test1': '#ff7f0e',   # 橙色
    'rest2': '#d62728',   # 红色
    'test2': '#9467bd',   # 紫色
    'rest3': '#8c564b',   # 棕色
    'test3': '#e377c2'    # 粉色
}

def load_hep_data(stage):
    """加载指定阶段的HEP数据"""
    # 查找最新的数据文件
    files = [f for f in os.listdir(DATA_DIR) if f.startswith(f'{stage}_raw_epochs_') and f.endswith('.h5')]
    if not files:
        raise FileNotFoundError(f"未找到{stage}阶段的数据文件")
    
    # 按时间戳排序，获取最新的文件
    latest_file = sorted(files)[-1]
    file_path = os.path.join(DATA_DIR, latest_file)
    
    print(f"正在加载{stage}阶段数据: {latest_file}")
    
    with h5py.File(file_path, 'r') as f:
        data = f['data'][:]
        ch_names = [ch.decode('utf-8') for ch in f['ch_names'][:]]
        times = f['times'][:]
        sfreq = f.attrs['sfreq']
    
    return data, ch_names, times, sfreq

def plot_region_hep(region_name, electrodes, stage_data, times, stage):
    """绘制指定脑区的HEP波形图"""
    plt.figure(figsize=(15, 8))
    
    # 获取该脑区所有电极的索引
    electrode_indices = [i for i, ch in enumerate(stage_data['ch_names']) if ch in electrodes]
    
    if not electrode_indices:
        print(f"警告: {region_name}区域未找到任何电极")
        return
    
    # 绘制每个电极的波形
    for idx in electrode_indices:
        ch_name = stage_data['ch_names'][idx]
        plt.plot(times * 1000, stage_data['data'][idx], 
                label=ch_name, alpha=0.7, linewidth=1.5)
    
    # 添加零线
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    plt.axvline(x=0, color='black', linestyle='--', alpha=0.5, label='R波')
    
    # 设置图表属性
    plt.title(f'{region_name}区域HEP波形 ({stage}阶段)', fontweight='bold')
    plt.xlabel('时间 (ms)')
    plt.ylabel('振幅 (μV)')
    plt.grid(True, alpha=0.3)
    plt.xlim(-800, 1000)
    
    # 添加图例
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', borderaxespad=0.)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(RESULT_DIR, f'{region_name}_{stage}_hep.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"已保存{region_name}区域{stage}阶段的HEP波形图: {save_path}")

def main():
    """主函数"""
    print("开始绘制脑区HEP波形图...")
    
    # 处理每个阶段
    for stage in STAGES:
        try:
            # 加载数据
            data, ch_names, times, sfreq = load_hep_data(stage)
            stage_data = {
                'data': data,
                'ch_names': ch_names,
                'times': times,
                'sfreq': sfreq
            }
            
            # 为每个脑区绘制波形图
            for region_name, electrodes in REGIONS.items():
                plot_region_hep(region_name, electrodes, stage_data, times, stage)
                
        except Exception as e:
            print(f"处理{stage}阶段时出错: {str(e)}")
            continue
    
    print("所有脑区HEP波形图绘制完成！")

if __name__ == "__main__":
    main() 