#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
心跳诱发电位(HEP)与主观心理量表关联分析脚本
分析HEP成分与主观心理指标之间的关系
"""

import os
import numpy as np
import pandas as pd
import mne
from scipy import stats
import matplotlib.pyplot as plt
from matplotlib.ticker import AutoMinorLocator
import matplotlib.font_manager as fm

# 设置matplotlib参数
plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (8, 6)
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

# 设置IEEE Transactions风格
plt.style.use('default')
plt.rcParams['axes.grid'] = True
plt.rcParams['grid.linestyle'] = '--'
plt.rcParams['grid.alpha'] = 0.3
plt.rcParams['lines.linewidth'] = 1.5
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['xtick.major.width'] = 0.8
plt.rcParams['ytick.major.width'] = 0.8
plt.rcParams['xtick.minor.width'] = 0.6
plt.rcParams['ytick.minor.width'] = 0.6

# 数据路径
PSYCH_DATA_PATH = r"D:\ecgeeg\stress0422.xlsx"
HEP_DATA_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_data"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_psychological_correlation"
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 定义感兴趣的时间窗口
HEP_INTEREST_TMIN = 0.5  # R波后500ms
HEP_INTEREST_TMAX = 0.7  # R波后700ms

# 定义阶段名称
STAGES = {
    'prac': '练习阶段',
    'rest1': '静息态1',
    'test1': '刺激态1',
    'rest2': '静息态2',
    'test2': '刺激态2',
    'rest3': '静息态3',
    'test3': '刺激态3'
}

# 定义颜色
COLORS = {
    '练习阶段': '#1f77b4',  # 蓝色
    '静息态1': '#ff7f0e',   # 橙色
    '刺激态1': '#2ca02c',   # 绿色
    '静息态2': '#d62728',   # 红色
    '刺激态2': '#9467bd',   # 紫色
    '静息态3': '#8c564b',   # 棕色
    '刺激态3': '#e377c2'    # 粉色
}

def load_psychological_data(file_path):
    """
    加载主观心理量表数据

    参数:
    file_path (str): 数据文件路径

    返回:
    pandas.DataFrame: 主观心理量表数据
    """
    # 加载Excel数据
    psych_data = pd.read_excel(file_path)

    # 将编号列转换为字符串，并确保两位数格式（与fif文件名匹配）
    psych_data['编号'] = psych_data['编号'].astype(str).str.zfill(2)

    print(f"已加载主观心理量表数据，共 {len(psych_data)} 名被试")
    print(f"数据列: {psych_data.columns.tolist()}")

    return psych_data

def load_hep_data(data_dir):
    """
    加载HEP数据

    参数:
    data_dir (str): 数据目录

    返回:
    dict: 各阶段的HEP数据
    """
    hep_data = {}

    # 加载每个阶段的数据
    for stage in STAGES.keys():
        file_path = os.path.join(data_dir, f'hep_{stage}.fif')
        if os.path.exists(file_path):
            epochs = mne.read_epochs(file_path)
            print(f"已加载 {stage} 阶段的HEP数据")
            print(f"  - 数据形状: {epochs.get_data().shape}")
            print(f"  - 通道数量: {len(epochs.ch_names)}")
            print(f"  - 时间点数量: {len(epochs.times)}")
            print(f"  - 时间范围: {epochs.times[0]:.2f}s 到 {epochs.times[-1]:.2f}s")
            hep_data[stage] = epochs
        else:
            hep_data[stage] = None
            print(f"未找到 {stage} 阶段的HEP数据")

    return hep_data

def extract_hep_features(hep_data, interest_tmin, interest_tmax):
    """
    提取HEP特征

    参数:
    hep_data (dict): 各阶段的HEP数据
    interest_tmin (float): 感兴趣时间窗口的起始时间
    interest_tmax (float): 感兴趣时间窗口的结束时间

    返回:
    dict: 各阶段的HEP特征
    """
    hep_features = {}

    # 对每个阶段提取特征
    for stage, epochs in hep_data.items():
        if epochs is None:
            hep_features[stage] = None
            continue

        # 获取感兴趣时间窗口的索引
        times = epochs.times
        interest_mask = (times >= interest_tmin) & (times <= interest_tmax)

        # 提取每个被试的数据
        data = epochs.get_data() * 1e6  # 转换为微伏

        # 计算感兴趣时间窗口内的平均振幅
        mean_amp = np.mean(data[:, :, interest_mask], axis=2)

        # 提取被试ID
        # 从文件名中提取被试ID
        # 假设每个epoch对应一个被试，我们需要从文件名中提取被试ID
        # 这里我们使用一个简单的方法：从epochs的描述中提取被试ID
        # 实际情况可能需要根据数据结构进行调整

        # 获取唯一的被试ID
        unique_ids = []
        for filename in epochs.info['filenames']:
            if isinstance(filename, str):
                # 从文件名中提取被试ID（前两位数字）
                basename = os.path.basename(filename)
                subject_id = basename[:2]  # 取前两位作为被试ID
                if subject_id not in unique_ids:
                    unique_ids.append(subject_id)

        # 如果无法从文件名中提取被试ID，则使用默认ID
        if not unique_ids:
            subject_id = "00"  # 默认ID
            unique_ids = [subject_id]

        # 使用第一个被试ID作为该阶段的被试ID
        subject_id = unique_ids[0]

        # 创建特征字典
        features = {
            'subject_id': subject_id,
            'mean_amp': mean_amp,
            'channels': epochs.ch_names
        }

        hep_features[stage] = features

    return hep_features

def match_data(psych_data, hep_features):
    """
    匹配主观心理量表数据和HEP特征

    参数:
    psych_data (pandas.DataFrame): 主观心理量表数据
    hep_features (dict): 各阶段的HEP特征

    返回:
    dict: 匹配后的数据
    """
    matched_data = {}

    # 对每个阶段进行匹配
    for stage, features in hep_features.items():
        if features is None:
            matched_data[stage] = None
            continue

        # 获取该阶段的被试ID和HEP特征
        subject_id = features['subject_id']
        mean_amp = features['mean_amp']
        channels = features['channels']

        # 在主观心理量表数据中查找该被试
        subj_psych = psych_data[psych_data['编号'] == subject_id]

        if len(subj_psych) > 0:
            # 找到匹配的被试
            # 创建匹配数据字典
            matched_data[stage] = {
                'subject_id': subject_id,
                'mean_amp': mean_amp,
                'psych_data': subj_psych.iloc[0],
                'channels': channels
            }
            print(f"{stage} 阶段匹配到被试 {subject_id}")
        else:
            matched_data[stage] = None
            print(f"{stage} 阶段未匹配到被试 {subject_id}")

    return matched_data

def analyze_correlations(matched_data, channel_indices=None):
    """
    分析HEP特征与主观心理量表指标之间的相关性

    参数:
    matched_data (dict): 匹配后的数据
    channel_indices (list): 要分析的通道索引，如果为None则分析所有通道

    返回:
    dict: 相关性分析结果
    """
    correlation_results = {}

    # 定义要分析的主观指标
    psych_measures = [
        '压力1', '压力2', '压力3',
        '成功1', '成功2', '成功3',
        '自信1', '自信2', '自信3',
        '疼痛1', '疼痛2', '疼痛3',
        '特质焦虑0', '状态焦虑1', '特质焦虑1', '状态焦虑2', '特质焦虑2',
        ' 心理韧性', '坚韧', '乐观', '力量'
    ]

    # 收集所有阶段的数据
    all_stages_data = {}
    for stage, data in matched_data.items():
        if data is not None:
            all_stages_data[stage] = data

    # 如果没有足够的数据进行相关性分析，则返回空结果
    if len(all_stages_data) < 2:
        print("警告: 没有足够的数据进行相关性分析")
        return correlation_results

    # 合并所有阶段的数据
    all_mean_amps = []
    all_psych_data = []
    all_channels = None

    for stage, data in all_stages_data.items():
        all_mean_amps.append(data['mean_amp'])
        all_psych_data.append(data['psych_data'])
        if all_channels is None:
            all_channels = data['channels']

    # 将所有阶段的数据堆叠起来
    mean_amp = np.vstack(all_mean_amps)
    psych_data = pd.DataFrame(all_psych_data)

    # 如果未指定通道索引，则使用所有通道
    if channel_indices is None and all_channels is not None:
        channel_indices = list(range(len(all_channels)))

    # 创建结果字典
    stage_results = {}

    # 对每个通道进行分析
    for ch_idx in channel_indices:
        ch_name = all_channels[ch_idx]
        ch_results = {}

        # 对每个主观指标进行分析
        for measure in psych_measures:
            if measure in psych_data.columns:
                # 提取该通道的HEP振幅和主观指标
                ch_amp = mean_amp[:, ch_idx]
                measure_values = psych_data[measure].values

                # 计算相关性
                # 由于样本量可能较小，这里使用斯皮尔曼相关系数
                r, p = stats.spearmanr(ch_amp, measure_values)

                # 存储结果
                ch_results[measure] = {
                    'r': r,
                    'p': p
                }

        stage_results[ch_name] = ch_results

    correlation_results['all'] = stage_results

    return correlation_results

def save_results(correlation_results, output_dir):
    """
    保存相关性分析结果

    参数:
    correlation_results (dict): 相关性分析结果
    output_dir (str): 输出目录
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 保存相关性分析结果
    for stage, results in correlation_results.items():
        if results is None:
            continue

        # 创建结果DataFrame
        rows = []
        for ch_name, ch_results in results.items():
            for measure, measure_results in ch_results.items():
                row = {
                    'Channel': ch_name,
                    'Measure': measure,
                    'r': measure_results['r'],
                    'p': measure_results['p']
                }
                rows.append(row)

        if rows:
            df = pd.DataFrame(rows)

            # 保存为CSV文件
            output_path = os.path.join(output_dir, f'correlation_results_{stage}.csv')
            df.to_csv(output_path, index=False)
            print(f"相关性分析结果已保存至: {output_path}")

def main():
    """主函数"""
    print("\n开始执行HEP与主观心理量表关联分析...")

    # 加载主观心理量表数据
    psych_data = load_psychological_data(PSYCH_DATA_PATH)

    # 加载HEP数据
    hep_data = load_hep_data(HEP_DATA_DIR)

    # 提取HEP特征
    hep_features = extract_hep_features(hep_data, HEP_INTEREST_TMIN, HEP_INTEREST_TMAX)

    # 匹配数据
    matched_data = match_data(psych_data, hep_features)

    # 分析相关性
    correlation_results = analyze_correlations(matched_data)

    # 保存结果
    save_results(correlation_results, OUTPUT_DIR)

    print("\nHEP与主观心理量表关联分析完成！")
    print(f"输出文件保存在: {OUTPUT_DIR}")

if __name__ == "__main__":
    main()
