#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
心理韧性调节效应分析 - 模块3：基础调节效应分析

功能：
- 加载预处理后的数据
- 进行分层回归分析，检验心理韧性的调节作用
- 计算简单斜率，分析不同心理韧性水平下的条件效应
- 保存分析结果

作者：AI助手
日期：2024年
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import statsmodels.api as sm
import statsmodels.formula.api as smf
from matplotlib.font_manager import FontProperties
import matplotlib as mpl
from datetime import datetime

# 设置中文字体
try:
    font = FontProperties(fname=r"C:\Windows\Fonts\LXGW WenKai Mono.ttf")
    plt.rcParams['font.family'] = ['sans-serif']
    plt.rcParams['font.sans-serif'] = ['LXGW WenKai']
    plt.rcParams['axes.unicode_minus'] = False
    mpl.rcParams['font.size'] = 10
except:
    print("警告: 未找到LXGW WenKai字体，使用系统默认字体")

# 定义常量
DATA_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\moderation_analysis"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\moderation_analysis"

def find_latest_data_file(data_dir=DATA_DIR):
    """
    查找最新的数据文件
    
    参数:
    data_dir (str): 数据目录
    
    返回:
    str: 最新的数据文件路径
    """
    print(f"查找数据文件: {data_dir}")
    
    # 查找所有数据文件
    data_files = [f for f in os.listdir(data_dir) if f.startswith("moderation_analysis_data_") and f.endswith('.csv')]
    
    if not data_files:
        raise FileNotFoundError(f"未找到数据文件")
    
    # 选择最新的文件
    latest_file = max([os.path.join(data_dir, f) for f in data_files], key=os.path.getmtime)
    
    print(f"找到最新的数据文件: {os.path.basename(latest_file)}")
    
    return latest_file

def load_data(file_path):
    """
    加载数据
    
    参数:
    file_path (str): 数据文件路径
    
    返回:
    pd.DataFrame: 数据
    """
    print(f"加载数据: {file_path}")
    
    # 读取CSV文件
    df = pd.read_csv(file_path)
    
    print(f"数据包含{len(df)}条记录，{df['subject_id'].nunique()}个被试")
    
    return df

def run_moderation_analysis(df, x_var, w_var, y_var, control_vars=None):
    """
    进行调节效应分析
    
    参数:
    df (pd.DataFrame): 数据
    x_var (str): 自变量
    w_var (str): 调节变量
    y_var (str): 因变量
    control_vars (list): 控制变量
    
    返回:
    dict: 分析结果
    """
    print(f"进行调节效应分析: {y_var} ~ {x_var} * {w_var}")
    
    # 确保变量存在
    required_vars = [x_var, w_var, y_var]
    missing_vars = [var for var in required_vars if var not in df.columns]
    
    if missing_vars:
        print(f"警告: 以下变量缺失: {missing_vars}")
        return None
    
    # 准备控制变量
    if control_vars is None:
        control_vars = []
    
    control_formula = " + ".join(control_vars) if control_vars else ""
    control_formula = " + " + control_formula if control_formula else ""
    
    # 构建回归公式
    formula = f"{y_var} ~ {x_var} + {w_var} + {x_var}:{w_var}{control_formula}"
    
    # 删除缺失值
    model_df = df[required_vars + control_vars].dropna()
    
    # 分层回归分析
    # 步骤1：只有主效应
    formula1 = f"{y_var} ~ {x_var} + {w_var}{control_formula}"
    model1 = smf.ols(formula1, data=model_df).fit()
    
    # 步骤2：加入交互项
    formula2 = formula
    model2 = smf.ols(formula2, data=model_df).fit()
    
    # 计算R²变化量
    r2_change = model2.rsquared - model1.rsquared
    f_change = ((model2.rsquared - model1.rsquared) / (model2.df_model - model1.df_model)) / \
               ((1 - model2.rsquared) / model2.df_resid)
    p_change = 1 - stats.f.cdf(f_change, model2.df_model - model1.df_model, model2.df_resid)
    
    # 提取结果
    results = {
        'x_var': x_var,
        'w_var': w_var,
        'y_var': y_var,
        'control_vars': control_vars,
        'n': len(model_df),
        'model1': model1,
        'model2': model2,
        'r2_model1': model1.rsquared,
        'r2_model2': model2.rsquared,
        'r2_change': r2_change,
        'f_change': f_change,
        'p_change': p_change,
        'interaction_coef': model2.params[f"{x_var}:{w_var}"],
        'interaction_p': model2.pvalues[f"{x_var}:{w_var}"]
    }
    
    print(f"调节效应分析完成: R²变化量 = {r2_change:.4f}, p = {p_change:.4f}")
    
    return results

def calculate_simple_slopes(results, df, x_var, w_var, y_var):
    """
    计算简单斜率
    
    参数:
    results (dict): 调节效应分析结果
    df (pd.DataFrame): 数据
    x_var (str): 自变量
    w_var (str): 调节变量
    y_var (str): 因变量
    
    返回:
    dict: 简单斜率结果
    """
    print("计算简单斜率...")
    
    # 获取模型
    model = results['model2']
    
    # 获取调节变量的均值和标准差
    w_mean = df[w_var].mean()
    w_sd = df[w_var].std()
    
    # 计算调节变量的不同水平
    w_levels = {
        'low': w_mean - w_sd,
        'mean': w_mean,
        'high': w_mean + w_sd
    }
    
    # 计算简单斜率
    simple_slopes = {}
    
    for level_name, w_value in w_levels.items():
        # 计算简单斜率
        slope = model.params[x_var] + model.params[f"{x_var}:{w_var}"] * w_value
        
        # 计算标准误
        x_var_idx = model.model.exog_names.index(x_var)
        interaction_idx = model.model.exog_names.index(f"{x_var}:{w_var}")
        
        var_x = model.cov_params().iloc[x_var_idx, x_var_idx]
        var_interaction = model.cov_params().iloc[interaction_idx, interaction_idx]
        cov_x_interaction = model.cov_params().iloc[x_var_idx, interaction_idx]
        
        se = np.sqrt(var_x + w_value**2 * var_interaction + 2 * w_value * cov_x_interaction)
        
        # 计算t值和p值
        t = slope / se
        p = 2 * (1 - stats.t.cdf(abs(t), model.df_resid))
        
        # 存储结果
        simple_slopes[level_name] = {
            'w_value': w_value,
            'slope': slope,
            'se': se,
            't': t,
            'p': p
        }
    
    print("简单斜率计算完成")
    
    return simple_slopes

def plot_interaction(results, simple_slopes, df, x_var, w_var, y_var, output_dir=OUTPUT_DIR):
    """
    绘制交互效应图
    
    参数:
    results (dict): 调节效应分析结果
    simple_slopes (dict): 简单斜率结果
    df (pd.DataFrame): 数据
    x_var (str): 自变量
    w_var (str): 调节变量
    y_var (str): 因变量
    output_dir (str): 输出目录
    
    返回:
    str: 图表文件路径
    """
    print("绘制交互效应图...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建图表
    plt.figure(figsize=(10, 8))
    
    # 获取自变量的范围
    x_min = df[x_var].min()
    x_max = df[x_var].max()
    x_range = np.linspace(x_min, x_max, 100)
    
    # 获取模型
    model = results['model2']
    
    # 绘制不同调节变量水平下的回归线
    colors = ['blue', 'green', 'red']
    labels = ['低心理韧性(-1SD)', '中等心理韧性(均值)', '高心理韧性(+1SD)']
    
    for (level_name, slope_results), color, label in zip(simple_slopes.items(), colors, labels):
        # 计算预测值
        w_value = slope_results['w_value']
        intercept = model.params['Intercept'] + model.params[w_var] * w_value
        slope = slope_results['slope']
        
        y_pred = intercept + slope * x_range
        
        # 绘制回归线
        plt.plot(x_range, y_pred, color=color, label=f"{label}: b = {slope:.3f}, p = {slope_results['p']:.3f}")
        
        # 如果斜率显著，添加显著性标记
        if slope_results['p'] < 0.05:
            plt.plot(x_range, y_pred, color=color, linewidth=3)
    
    # 添加散点图
    plt.scatter(df[x_var], df[y_var], alpha=0.3, color='gray')
    
    # 添加标题和标签
    plt.title(f'心理韧性调节HEP振幅与{y_var}的关系')
    plt.xlabel(x_var)
    plt.ylabel(y_var)
    
    # 添加交互效应信息
    interaction_text = f"交互效应: b = {results['interaction_coef']:.3f}, p = {results['interaction_p']:.3f}\n"
    interaction_text += f"R² 变化量: {results['r2_change']:.3f}, p = {results['p_change']:.3f}"
    
    plt.annotate(interaction_text, xy=(0.05, 0.95), xycoords='axes fraction',
                ha='left', va='top', bbox=dict(boxstyle='round', fc='white', alpha=0.8))
    
    # 添加图例
    plt.legend(loc='best')
    
    # 添加网格
    plt.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    fig_path = os.path.join(output_dir, f"interaction_{x_var}_{w_var}_{y_var}_{timestamp}.png")
    plt.savefig(fig_path)
    plt.close()
    
    print(f"交互效应图已保存至: {fig_path}")
    
    return fig_path

def run_multiple_moderation_analyses(df):
    """
    运行多个调节效应分析
    
    参数:
    df (pd.DataFrame): 数据
    
    返回:
    dict: 分析结果
    """
    print("运行多个调节效应分析...")
    
    # 定义分析配置
    analyses = [
        # 特质焦虑作为因变量
        {'x_var': 'mean_amplitude_z', 'w_var': 'resilience_z', 'y_var': 'trait_anxiety_z'},
        
        # 状态焦虑作为因变量
        {'x_var': 'mean_amplitude_z', 'w_var': 'resilience_z', 'y_var': 'state_anxiety_z'},
    ]
    
    # 存储结果
    all_results = {}
    all_simple_slopes = {}
    all_fig_paths = {}
    
    # 运行分析
    for config in analyses:
        x_var = config['x_var']
        w_var = config['w_var']
        y_var = config['y_var']
        
        # 进行调节效应分析
        results = run_moderation_analysis(df, x_var, w_var, y_var)
        
        if results is not None:
            # 计算简单斜率
            simple_slopes = calculate_simple_slopes(results, df, x_var, w_var, y_var)
            
            # 绘制交互效应图
            fig_path = plot_interaction(results, simple_slopes, df, x_var, w_var, y_var)
            
            # 存储结果
            all_results[y_var] = results
            all_simple_slopes[y_var] = simple_slopes
            all_fig_paths[y_var] = fig_path
    
    return {
        'results': all_results,
        'simple_slopes': all_simple_slopes,
        'fig_paths': all_fig_paths
    }

def analyze_by_channel_and_window(df):
    """
    按通道和时间窗口进行调节效应分析
    
    参数:
    df (pd.DataFrame): 数据
    
    返回:
    pd.DataFrame: 分析结果摘要
    """
    print("按通道和时间窗口进行调节效应分析...")
    
    # 获取唯一的通道和时间窗口
    channels = df['channel'].unique()
    windows = df['window'].unique()
    
    # 存储结果
    results_summary = []
    
    # 对每个通道和时间窗口进行分析
    for channel in channels:
        for window in windows:
            # 筛选数据
            subset = df[(df['channel'] == channel) & (df['window'] == window)]
            
            if len(subset) < 30:
                print(f"警告: {channel} {window}的数据点不足30个，跳过分析")
                continue
            
            # 进行调节效应分析
            # 特质焦虑
            trait_results = run_moderation_analysis(subset, 'mean_amplitude_z', 'resilience_z', 'trait_anxiety_z')
            
            if trait_results is not None:
                # 添加结果摘要
                results_summary.append({
                    'channel': channel,
                    'window': window,
                    'y_var': 'trait_anxiety_z',
                    'n': trait_results['n'],
                    'r2_model1': trait_results['r2_model1'],
                    'r2_model2': trait_results['r2_model2'],
                    'r2_change': trait_results['r2_change'],
                    'f_change': trait_results['f_change'],
                    'p_change': trait_results['p_change'],
                    'interaction_coef': trait_results['interaction_coef'],
                    'interaction_p': trait_results['interaction_p']
                })
                
                # 如果交互效应显著，绘制交互效应图
                if trait_results['p_change'] < 0.05:
                    # 计算简单斜率
                    simple_slopes = calculate_simple_slopes(trait_results, subset, 'mean_amplitude_z', 'resilience_z', 'trait_anxiety_z')
                    
                    # 绘制交互效应图
                    plot_interaction(trait_results, simple_slopes, subset, 'mean_amplitude_z', 'resilience_z', 'trait_anxiety_z')
            
            # 状态焦虑
            state_results = run_moderation_analysis(subset, 'mean_amplitude_z', 'resilience_z', 'state_anxiety_z')
            
            if state_results is not None:
                # 添加结果摘要
                results_summary.append({
                    'channel': channel,
                    'window': window,
                    'y_var': 'state_anxiety_z',
                    'n': state_results['n'],
                    'r2_model1': state_results['r2_model1'],
                    'r2_model2': state_results['r2_model2'],
                    'r2_change': state_results['r2_change'],
                    'f_change': state_results['f_change'],
                    'p_change': state_results['p_change'],
                    'interaction_coef': state_results['interaction_coef'],
                    'interaction_p': state_results['interaction_p']
                })
                
                # 如果交互效应显著，绘制交互效应图
                if state_results['p_change'] < 0.05:
                    # 计算简单斜率
                    simple_slopes = calculate_simple_slopes(state_results, subset, 'mean_amplitude_z', 'resilience_z', 'state_anxiety_z')
                    
                    # 绘制交互效应图
                    plot_interaction(state_results, simple_slopes, subset, 'mean_amplitude_z', 'resilience_z', 'state_anxiety_z')
    
    # 转换为DataFrame
    results_df = pd.DataFrame(results_summary)
    
    return results_df

def save_results(results_df, output_dir=OUTPUT_DIR):
    """
    保存分析结果
    
    参数:
    results_df (pd.DataFrame): 分析结果
    output_dir (str): 输出目录
    
    返回:
    str: 保存的文件路径
    """
    print("保存分析结果...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存为CSV文件
    output_path = os.path.join(output_dir, f"moderation_results_{timestamp}.csv")
    results_df.to_csv(output_path, index=False)
    
    print(f"分析结果已保存至: {output_path}")
    
    return output_path

def main():
    """主函数"""
    # 查找最新的数据文件
    data_file = find_latest_data_file()
    
    # 加载数据
    df = load_data(data_file)
    
    # 运行多个调节效应分析
    all_analyses = run_multiple_moderation_analyses(df)
    
    # 按通道和时间窗口进行调节效应分析
    results_df = analyze_by_channel_and_window(df)
    
    # 保存分析结果
    results_path = save_results(results_df)
    
    print("\n调节效应分析完成")
    print(f"分析结果已保存至: {results_path}")
    
    # 打印显著的调节效应
    sig_results = results_df[results_df['p_change'] < 0.05]
    
    if len(sig_results) > 0:
        print("\n===== 显著的调节效应 =====")
        print(sig_results[['channel', 'window', 'y_var', 'r2_change', 'p_change', 'interaction_coef']])
    else:
        print("\n未发现显著的调节效应")

if __name__ == "__main__":
    main()
