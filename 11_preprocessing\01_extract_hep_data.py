#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HEP数据提取脚本

功能：
- 从原始EEG/ECG数据中提取心跳诱发电位(HEP)数据
- 按被试ID、阶段和状态组织数据
- 保存处理后的数据到指定位置，以便后续分析

用法：
python 01_extract_hep_data.py --subject_ids "01,02,03" --stages "prac,test1,test2,test3,rest1,rest2,rest3"

参数：
--subject_ids: 要处理的被试ID，多个ID用逗号分隔
--stages: 要处理的实验阶段，多个阶段用逗号分隔
--raw_data_dir: 原始数据目录，默认为"D:/ecgeeg/19-eegecg手动预处理6-ICA3"
--output_dir: 输出目录，默认为"D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_data"
"""

import os
import argparse
import numpy as np
import pandas as pd
import mne
from mne.preprocessing import find_ecg_events
import matplotlib.pyplot as plt
from tqdm import tqdm
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 定义常量
SAMPLE_RATE = 1000  # 采样率，单位Hz
ECG_CH_NAMES = ['ECG1', 'ECG2', 'ECG3', 'ECG4', 'ECG5', 'ECG6', 'ECG7', 'ECG8', 'ECG9', 'ECG10',
                'ECG11', 'ECG12', 'ECG13', 'ECG14', 'ECG15', 'ECG16', 'ECG17', 'ECG18', 'ECG19', 'ECG20',
                'ECG21', 'ECG22', 'ECG23', 'ECG24', 'ECG25', 'ECG26', 'ECG27', 'ECG28', 'ECG29', 'ECG30',
                'ECG31', 'ECG32', 'ECG33', 'ECG34', 'ECG35', 'ECG36', 'ECG37', 'ECG38', 'ECG39', 'ECG40',
                'ECG41', 'ECG42', 'ECG43', 'ECG44', 'ECG45', 'ECG46', 'ECG47', 'ECG48', 'ECG49', 'ECG50',
                'ECG51', 'ECG52', 'ECG53', 'ECG54', 'ECG55', 'ECG56', 'ECG57', 'ECG58']

# 定义阶段和状态映射
STAGE_MAPPING = {
    'prac': '练习阶段',
    'test': '刺激态',
    'rest': '静息态'
}

# 定义轮次映射
ROUND_MAPPING = {
    '01': 1,  # 第一轮: prac, test1, rest1
    '02': 2,  # 第二轮: test2, rest2
    '03': 3   # 第三轮: test3, rest3
}

# 定义文件名模式
FILE_PATTERN = "{subject_id}_{round_id}_{condition}_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{stage}"

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='从原始EEG/ECG数据中提取HEP数据')
    parser.add_argument('--subject_ids', type=str, default="01,02,03,04,05",
                        help='要处理的被试ID，多个ID用逗号分隔')
    parser.add_argument('--stages', type=str, default="prac,test1,test2,test3,rest1,rest2,rest3",
                        help='要处理的实验阶段，多个阶段用逗号分隔')
    parser.add_argument('--raw_data_dir', type=str, default="D:/ecgeeg/19-eegecg手动预处理6-ICA3",
                        help='原始数据目录')
    parser.add_argument('--output_dir', type=str, default="D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_data",
                        help='输出目录')
    return parser.parse_args()

def find_data_files(raw_data_dir, subject_id, stage):
    """
    查找指定被试和阶段的数据文件

    参数:
    raw_data_dir (str): 原始数据目录
    subject_id (str): 被试ID
    stage (str): 实验阶段 (prac, test1, test2, test3, rest1, rest2, rest3)

    返回:
    list: 找到的文件路径列表
    """
    # 解析阶段和轮次
    if stage == 'prac':
        actual_stage = 'prac'
        round_id = '01'
    elif stage.startswith('test'):
        actual_stage = 'test'
        if stage == 'test1':
            round_id = '01'
        elif stage == 'test2':
            round_id = '02'
        elif stage == 'test3':
            round_id = '03'
        else:
            round_id = '*'
    elif stage.startswith('rest'):
        actual_stage = 'rest'
        if stage == 'rest1':
            round_id = '01'
        elif stage == 'rest2':
            round_id = '02'
        elif stage == 'rest3':
            round_id = '03'
        else:
            round_id = '*'
    else:
        actual_stage = stage
        round_id = '*'

    # 查找匹配的文件
    matching_files = []

    # 尝试多种可能的文件模式
    patterns = []

    # 模式1: {subject_id}_{round_id}_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{actual_stage}*.fif
    patterns.append(f"{subject_id}_{round_id}_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{actual_stage}")

    # 模式2: {subject_id}_{round_id}_rest_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{actual_stage}*.fif
    patterns.append(f"{subject_id}_{round_id}_rest_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{actual_stage}")

    # 模式3: {subject_id}_{round_id}_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{actual_stage}*.fif
    patterns.append(f"{subject_id}_{round_id}_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{actual_stage}")

    # 遍历目录查找匹配的文件
    for root, _, files in os.walk(raw_data_dir):
        for file in files:
            if file.endswith('.fif'):
                for pattern in patterns:
                    if pattern in file:
                        matching_files.append(os.path.join(root, file))
                        break

    # 如果没有找到文件，尝试更宽松的匹配
    if not matching_files:
        print(f"使用宽松匹配模式查找被试 {subject_id} 的 {stage} 阶段数据文件")
        for root, _, files in os.walk(raw_data_dir):
            for file in files:
                if file.endswith('.fif') and file.startswith(f"{subject_id}_{round_id}") and actual_stage in file:
                    matching_files.append(os.path.join(root, file))

    return matching_files

def extract_hep(raw_file, tmin=-0.2, tmax=1.0):
    """
    从原始数据中提取HEP

    参数:
    raw_file (str): 原始数据文件路径
    tmin (float): 事件前时间，单位秒
    tmax (float): 事件后时间，单位秒

    返回:
    mne.Epochs: HEP epochs
    """
    print(f"处理文件: {os.path.basename(raw_file)}")

    # 加载原始数据
    raw = mne.io.read_raw_fif(raw_file, preload=True)

    # 选择ECG通道
    ecg_picks = []
    for ch_name in ECG_CH_NAMES:
        if ch_name in raw.ch_names:
            ecg_picks.append(ch_name)

    if not ecg_picks:
        print(f"警告: 未找到ECG通道，尝试使用其他方法")
        # 尝试使用其他方法找到ECG通道
        ecg_picks = mne.pick_types(raw.info, meg=False, eeg=False, ecg=True)
        if len(ecg_picks) == 0:
            # 尝试查找可能的ECG通道
            potential_ecg_channels = []
            for ch_name in raw.ch_names:
                if 'ecg' in ch_name.lower() or 'ekg' in ch_name.lower() or 'cardiac' in ch_name.lower():
                    potential_ecg_channels.append(ch_name)

            if potential_ecg_channels:
                ecg_picks = [raw.ch_names.index(ch) for ch in potential_ecg_channels]
                print(f"找到可能的ECG通道: {potential_ecg_channels}")
            else:
                print(f"错误: 无法找到ECG通道")
                return None

    # 提取ECG数据
    ecg_data = raw.get_data(picks=ecg_picks)

    # 如果有多个ECG通道，取平均
    if ecg_data.shape[0] > 1:
        ecg_data = np.mean(ecg_data, axis=0, keepdims=True)

    # 尝试多种方法检测R峰
    r_peaks = None
    methods_tried = 0
    min_required_peaks = 20  # 至少需要检测到的R峰数量

    # 方法1: 使用MNE的find_ecg_events
    try:
        print("方法1: 使用MNE的find_ecg_events")
        events = find_ecg_events(raw, ch_name=ecg_picks[0], event_id=999,
                                l_freq=5, h_freq=35, tstart=0,
                                verbose=False)
        methods_tried += 1

        if len(events) >= min_required_peaks:
            r_peaks = events
            print(f"方法1成功: 检测到 {len(events)} 个R峰")
    except Exception as e:
        print(f"方法1失败: {e}")

    # 方法2: 使用scipy.signal.find_peaks
    if r_peaks is None or len(r_peaks) < min_required_peaks:
        try:
            from scipy.signal import find_peaks
            print("方法2: 使用scipy.signal.find_peaks")
            methods_tried += 1

            # 预处理ECG信号
            from scipy.signal import butter, filtfilt

            # 带通滤波 (5-20 Hz)
            def butter_bandpass(lowcut, highcut, fs, order=5):
                nyq = 0.5 * fs
                low = lowcut / nyq
                high = highcut / nyq
                b, a = butter(order, [low, high], btype='band')
                return b, a

            def butter_bandpass_filter(data, lowcut, highcut, fs, order=5):
                b, a = butter_bandpass(lowcut, highcut, fs, order=order)
                y = filtfilt(b, a, data)
                return y

            # 应用带通滤波
            ecg_filtered = butter_bandpass_filter(ecg_data[0], 5, 20, SAMPLE_RATE)

            # 标准化
            ecg_normalized = (ecg_filtered - np.mean(ecg_filtered)) / np.std(ecg_filtered)

            # 使用find_peaks检测R峰
            peaks, _ = find_peaks(ecg_normalized, height=1.0, distance=int(0.5*SAMPLE_RATE))

            if len(peaks) >= min_required_peaks:
                # 创建events数组
                events = np.zeros((len(peaks), 3), dtype=int)
                events[:, 0] = peaks  # 事件时间点
                events[:, 2] = 999    # 事件ID
                r_peaks = events
                print(f"方法2成功: 检测到 {len(peaks)} 个R峰")
        except Exception as e:
            print(f"方法2失败: {e}")

    # 方法3: 使用neurokit2的ecg_peaks
    if r_peaks is None or len(r_peaks) < min_required_peaks:
        try:
            import neurokit2 as nk
            print("方法3: 使用neurokit2的ecg_peaks")
            methods_tried += 1

            # 使用neurokit2检测R峰
            _, info = nk.ecg_peaks(ecg_data[0], sampling_rate=SAMPLE_RATE)
            peaks = info['ECG_R_Peaks']

            if len(peaks) >= min_required_peaks:
                # 创建events数组
                events = np.zeros((len(peaks), 3), dtype=int)
                events[:, 0] = peaks  # 事件时间点
                events[:, 2] = 999    # 事件ID
                r_peaks = events
                print(f"方法3成功: 检测到 {len(peaks)} 个R峰")
        except Exception as e:
            print(f"方法3失败: {e}")

    # 方法4: 使用Pan-Tompkins算法
    if r_peaks is None or len(r_peaks) < min_required_peaks:
        try:
            print("方法4: 使用Pan-Tompkins算法")
            methods_tried += 1

            # 简化版Pan-Tompkins算法
            def pan_tompkins_detector(ecg_signal, fs):
                # 带通滤波
                filtered_ecg = butter_bandpass_filter(ecg_signal, 5, 15, fs)

                # 差分
                diff_ecg = np.diff(filtered_ecg)
                diff_ecg = np.insert(diff_ecg, 0, 0)

                # 平方
                squared_ecg = diff_ecg ** 2

                # 移动平均
                window_size = int(0.15 * fs)
                moving_avg = np.zeros_like(squared_ecg)
                for i in range(len(squared_ecg)):
                    start = max(0, i - window_size)
                    moving_avg[i] = np.mean(squared_ecg[start:i+1])

                # 阈值检测
                threshold = 0.3 * np.max(moving_avg)
                peaks = []
                i = 0
                while i < len(moving_avg) - 1:
                    if moving_avg[i] > threshold:
                        # 在窗口内找到局部最大值
                        window_start = i
                        window_end = min(i + int(0.3 * fs), len(moving_avg))
                        local_max_idx = np.argmax(moving_avg[window_start:window_end]) + window_start
                        peaks.append(local_max_idx)
                        i = window_end
                    else:
                        i += 1

                return np.array(peaks)

            # 应用Pan-Tompkins算法
            peaks = pan_tompkins_detector(ecg_data[0], SAMPLE_RATE)

            if len(peaks) >= min_required_peaks:
                # 创建events数组
                events = np.zeros((len(peaks), 3), dtype=int)
                events[:, 0] = peaks  # 事件时间点
                events[:, 2] = 999    # 事件ID
                r_peaks = events
                print(f"方法4成功: 检测到 {len(peaks)} 个R峰")
        except Exception as e:
            print(f"方法4失败: {e}")

    # 检查是否成功检测到足够的R峰
    if r_peaks is None or len(r_peaks) < min_required_peaks:
        print(f"错误: 尝试了 {methods_tried} 种方法，但仍未能检测到足够的R峰 (至少需要 {min_required_peaks} 个)")
        return None

    print(f"最终检测到 {len(r_peaks)} 个R峰")

    # 创建epochs
    epochs = mne.Epochs(raw, r_peaks, event_id=999, tmin=tmin, tmax=tmax,
                        baseline=(tmin, 0), preload=True, verbose=False)

    # 添加metadata
    subject_id = os.path.basename(raw_file).split('_')[0]
    metadata = pd.DataFrame({
        'subject_id': [subject_id] * len(epochs),
        'file_name': [os.path.basename(raw_file)] * len(epochs)
    })
    epochs.metadata = metadata

    return epochs

def process_subject_stage(subject_id, stage, raw_data_dir, output_dir):
    """
    处理指定被试和阶段的数据

    参数:
    subject_id (str): 被试ID
    stage (str): 实验阶段
    raw_data_dir (str): 原始数据目录
    output_dir (str): 输出目录

    返回:
    bool: 是否成功处理
    """
    # 查找数据文件
    data_files = find_data_files(raw_data_dir, subject_id, stage)

    if not data_files:
        print(f"未找到被试 {subject_id} 的 {stage} 阶段数据文件")
        # 即使没有找到数据文件，也创建一个空的占位文件，以便后续分析脚本知道这个被试/阶段已经被处理过
        # 确定轮次ID和状态
        if stage == 'prac':
            round_id = '01'
            state = 'prac'
        elif stage.startswith('test'):
            state = 'test'
            if stage == 'test1':
                round_id = '01'
            elif stage == 'test2':
                round_id = '02'
            elif stage == 'test3':
                round_id = '03'
        elif stage.startswith('rest'):
            state = 'rest'
            if stage == 'rest1':
                round_id = '01'
            elif stage == 'rest2':
                round_id = '02'
            elif stage == 'rest3':
                round_id = '03'

        # 创建一个空的占位文件
        placeholder_file = os.path.join(output_dir, f"{subject_id}_{round_id}_{state}_placeholder.txt")
        with open(placeholder_file, 'w') as f:
            f.write(f"未找到被试 {subject_id} 的 {stage} 阶段数据文件\n")
        print(f"已创建占位文件: {placeholder_file}")

        return False

    print(f"找到 {len(data_files)} 个文件用于被试 {subject_id} 的 {stage} 阶段")

    # 处理每个文件
    all_epochs = []
    for file in data_files:
        print(f"处理文件: {os.path.basename(file)}")
        try:
            epochs = extract_hep(file)
            if epochs is not None and len(epochs) > 0:
                # 添加阶段信息到metadata
                if not hasattr(epochs, 'metadata') or epochs.metadata is None:
                    epochs.metadata = pd.DataFrame({'subject_id': [subject_id] * len(epochs), 'stage': [stage] * len(epochs)})
                else:
                    epochs.metadata['stage'] = stage

                all_epochs.append(epochs)
                print(f"从文件 {os.path.basename(file)} 中提取了 {len(epochs)} 个HEP epochs")
            else:
                print(f"警告: 从文件 {os.path.basename(file)} 中未能提取到有效的HEP epochs")
        except Exception as e:
            print(f"错误: 处理文件 {os.path.basename(file)} 时出错: {e}")
            # 记录错误信息，但继续处理其他文件
            continue

    if not all_epochs:
        print(f"未能从被试 {subject_id} 的 {stage} 阶段数据中提取HEP")
        # 创建一个错误日志文件
        if stage == 'prac':
            round_id = '01'
            state = 'prac'
        elif stage.startswith('test'):
            state = 'test'
            if stage == 'test1':
                round_id = '01'
            elif stage == 'test2':
                round_id = '02'
            elif stage == 'test3':
                round_id = '03'
        elif stage.startswith('rest'):
            state = 'rest'
            if stage == 'rest1':
                round_id = '01'
            elif stage == 'rest2':
                round_id = '02'
            elif stage == 'rest3':
                round_id = '03'

        error_file = os.path.join(output_dir, f"{subject_id}_{round_id}_{state}_error.txt")
        with open(error_file, 'w') as f:
            f.write(f"未能从被试 {subject_id} 的 {stage} 阶段数据中提取HEP\n")
            f.write(f"找到的文件: {data_files}\n")
        print(f"已创建错误日志文件: {error_file}")

        return False

    # 合并epochs
    combined_epochs = mne.concatenate_epochs(all_epochs)
    print(f"合并后共有 {len(combined_epochs)} 个HEP epochs")

    # 确定轮次ID和状态
    if stage == 'prac':
        round_id = '01'
        state = 'prac'
    elif stage.startswith('test'):
        state = 'test'
        if stage == 'test1':
            round_id = '01'
        elif stage == 'test2':
            round_id = '02'
        elif stage == 'test3':
            round_id = '03'
    elif stage.startswith('rest'):
        state = 'rest'
        if stage == 'rest1':
            round_id = '01'
        elif stage == 'rest2':
            round_id = '02'
        elif stage == 'rest3':
            round_id = '03'

    # 保存结果
    output_file = os.path.join(output_dir, f"{subject_id}_{round_id}_{state}.fif")
    try:
        combined_epochs.save(output_file, overwrite=True)
        print(f"已保存被试 {subject_id} 的 {stage} 阶段HEP数据至: {output_file}")
        return True
    except Exception as e:
        print(f"错误: 保存被试 {subject_id} 的 {stage} 阶段HEP数据时出错: {e}")
        # 创建一个错误日志文件
        error_file = os.path.join(output_dir, f"{subject_id}_{round_id}_{state}_save_error.txt")
        with open(error_file, 'w') as f:
            f.write(f"保存被试 {subject_id} 的 {stage} 阶段HEP数据时出错: {e}\n")
        print(f"已创建错误日志文件: {error_file}")
        return False

def merge_subject_data(subject_ids, stages, output_dir):
    """
    合并所有被试的数据

    参数:
    subject_ids (list): 被试ID列表
    stages (list): 实验阶段列表
    output_dir (str): 输出目录
    """
    for stage in stages:
        all_epochs = []
        processed_subjects = []

        # 确定轮次ID和状态
        if stage == 'prac':
            round_id = '01'
            state = 'prac'
        elif stage.startswith('test'):
            state = 'test'
            if stage == 'test1':
                round_id = '01'
            elif stage == 'test2':
                round_id = '02'
            elif stage == 'test3':
                round_id = '03'
        elif stage.startswith('rest'):
            state = 'rest'
            if stage == 'rest1':
                round_id = '01'
            elif stage == 'rest2':
                round_id = '02'
            elif stage == 'rest3':
                round_id = '03'

        for subject_id in subject_ids:
            file_path = os.path.join(output_dir, f"{subject_id}_{round_id}_{state}.fif")
            if os.path.exists(file_path):
                try:
                    epochs = mne.read_epochs(file_path, preload=True)
                    all_epochs.append(epochs)
                    processed_subjects.append(subject_id)
                    print(f"成功加载被试 {subject_id} 的 {stage} 阶段数据，包含 {len(epochs)} 个epochs")
                except Exception as e:
                    print(f"加载被试 {subject_id} 的 {stage} 阶段数据时出错: {e}")
            else:
                # 检查是否有占位文件或错误日志文件
                placeholder_file = os.path.join(output_dir, f"{subject_id}_{round_id}_{state}_placeholder.txt")
                error_file = os.path.join(output_dir, f"{subject_id}_{round_id}_{state}_error.txt")
                save_error_file = os.path.join(output_dir, f"{subject_id}_{round_id}_{state}_save_error.txt")

                if os.path.exists(placeholder_file):
                    print(f"被试 {subject_id} 的 {stage} 阶段数据不存在 (占位文件)")
                elif os.path.exists(error_file):
                    print(f"被试 {subject_id} 的 {stage} 阶段数据处理失败 (错误日志)")
                elif os.path.exists(save_error_file):
                    print(f"被试 {subject_id} 的 {stage} 阶段数据保存失败 (保存错误)")
                else:
                    print(f"未找到被试 {subject_id} 的 {stage} 阶段数据文件")

        if all_epochs:
            # 合并epochs
            try:
                combined_epochs = mne.concatenate_epochs(all_epochs)

                # 保存结果
                output_file = os.path.join(output_dir, f"all_subjects_{round_id}_{state}.fif")
                combined_epochs.save(output_file, overwrite=True)
                print(f"已合并 {len(processed_subjects)} 个被试的 {stage} 阶段HEP数据至: {output_file}")
                print(f"合并的被试: {', '.join(processed_subjects)}")
            except Exception as e:
                print(f"合并 {stage} 阶段数据时出错: {e}")
                # 创建一个错误日志文件
                error_file = os.path.join(output_dir, f"all_subjects_{round_id}_{state}_merge_error.txt")
                with open(error_file, 'w') as f:
                    f.write(f"合并 {stage} 阶段数据时出错: {e}\n")
                    f.write(f"处理的被试: {processed_subjects}\n")
                print(f"已创建错误日志文件: {error_file}")
        else:
            print(f"未找到任何被试的 {stage} 阶段数据")
            # 创建一个空的占位文件
            placeholder_file = os.path.join(output_dir, f"all_subjects_{round_id}_{state}_no_data.txt")
            with open(placeholder_file, 'w') as f:
                f.write(f"未找到任何被试的 {stage} 阶段数据\n")
            print(f"已创建占位文件: {placeholder_file}")

def create_fixed_data(subject_ids, stages, output_dir):
    """
    创建带有固定metadata的数据

    参数:
    subject_ids (list): 被试ID列表
    stages (list): 实验阶段列表
    output_dir (str): 输出目录
    """
    # 创建fixed目录
    fixed_dir = os.path.join(output_dir, 'fixed')
    os.makedirs(fixed_dir, exist_ok=True)

    for stage in stages:
        # 确定轮次ID和状态
        if stage == 'prac':
            round_id = '01'
            state = 'prac'
        elif stage.startswith('test'):
            state = 'test'
            if stage == 'test1':
                round_id = '01'
            elif stage == 'test2':
                round_id = '02'
            elif stage == 'test3':
                round_id = '03'
        elif stage.startswith('rest'):
            state = 'rest'
            if stage == 'rest1':
                round_id = '01'
            elif stage == 'rest2':
                round_id = '02'
            elif stage == 'rest3':
                round_id = '03'

        file_path = os.path.join(output_dir, f"all_subjects_{round_id}_{state}.fif")
        if os.path.exists(file_path):
            try:
                epochs = mne.read_epochs(file_path, preload=True)

                # 确保metadata存在
                if not hasattr(epochs, 'metadata') or epochs.metadata is None:
                    # 创建metadata
                    metadata = pd.DataFrame({
                        'subject_id': [subj for subj in subject_ids for _ in range(len(epochs) // len(subject_ids) + 1)][:len(epochs)],
                        'stage': [stage] * len(epochs),
                        'round_id': [round_id] * len(epochs),
                        'state': [state] * len(epochs)
                    })
                    epochs.metadata = metadata

                # 保存结果
                output_file = os.path.join(fixed_dir, f"all_subjects_{round_id}_{state}_fixed.fif")
                epochs.save(output_file, overwrite=True)
                print(f"已创建 {stage} 阶段的固定metadata数据至: {output_file}")
            except Exception as e:
                print(f"处理 {stage} 阶段数据时出错: {e}")
                # 创建一个错误日志文件
                error_file = os.path.join(fixed_dir, f"all_subjects_{round_id}_{state}_fixed_error.txt")
                with open(error_file, 'w') as f:
                    f.write(f"处理 {stage} 阶段数据时出错: {e}\n")
                print(f"已创建错误日志文件: {error_file}")
        else:
            print(f"未找到 {stage} 阶段的合并数据")
            # 检查是否有错误日志文件或占位文件
            merge_error_file = os.path.join(output_dir, f"all_subjects_{round_id}_{state}_merge_error.txt")
            no_data_file = os.path.join(output_dir, f"all_subjects_{round_id}_{state}_no_data.txt")

            if os.path.exists(merge_error_file):
                print(f"{stage} 阶段数据合并失败 (合并错误)")
                # 创建一个占位文件
                placeholder_file = os.path.join(fixed_dir, f"all_subjects_{round_id}_{state}_fixed_merge_error.txt")
                with open(placeholder_file, 'w') as f:
                    f.write(f"{stage} 阶段数据合并失败，无法创建固定metadata数据\n")
                print(f"已创建占位文件: {placeholder_file}")
            elif os.path.exists(no_data_file):
                print(f"{stage} 阶段没有数据 (无数据)")
                # 创建一个占位文件
                placeholder_file = os.path.join(fixed_dir, f"all_subjects_{round_id}_{state}_fixed_no_data.txt")
                with open(placeholder_file, 'w') as f:
                    f.write(f"{stage} 阶段没有数据，无法创建固定metadata数据\n")
                print(f"已创建占位文件: {placeholder_file}")
            else:
                # 创建一个占位文件
                placeholder_file = os.path.join(fixed_dir, f"all_subjects_{round_id}_{state}_fixed_missing.txt")
                with open(placeholder_file, 'w') as f:
                    f.write(f"未找到 {stage} 阶段的合并数据，无法创建固定metadata数据\n")
                print(f"已创建占位文件: {placeholder_file}")

def main():
    """主函数"""
    args = parse_arguments()

    # 解析参数
    subject_ids = args.subject_ids.split(',')
    stages = args.stages.split(',')
    raw_data_dir = args.raw_data_dir
    output_dir = args.output_dir

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(os.path.join(output_dir, 'fixed'), exist_ok=True)

    print(f"\n开始提取HEP数据...")
    print(f"被试: {subject_ids}")
    print(f"阶段: {stages}")
    print(f"原始数据目录: {raw_data_dir}")
    print(f"输出目录: {output_dir}")

    # 创建处理日志文件
    log_file = os.path.join(output_dir, 'processing_log.txt')
    with open(log_file, 'w') as f:
        f.write(f"HEP数据提取日志\n")
        f.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"被试: {subject_ids}\n")
        f.write(f"阶段: {stages}\n")
        f.write(f"原始数据目录: {raw_data_dir}\n")
        f.write(f"输出目录: {output_dir}\n\n")

    # 处理每个被试和阶段
    processed_subjects = {}
    for subject_id in subject_ids:
        processed_subjects[subject_id] = []
        for stage in stages:
            try:
                success = process_subject_stage(subject_id, stage, raw_data_dir, output_dir)
                if success:
                    processed_subjects[subject_id].append(stage)
                    # 记录成功处理的信息
                    with open(log_file, 'a') as f:
                        f.write(f"成功处理被试 {subject_id} 的 {stage} 阶段数据\n")
                else:
                    # 记录处理失败的信息
                    with open(log_file, 'a') as f:
                        f.write(f"处理被试 {subject_id} 的 {stage} 阶段数据失败\n")
            except Exception as e:
                print(f"处理被试 {subject_id} 的 {stage} 阶段数据时出现未捕获的异常: {e}")
                # 记录异常信息
                with open(log_file, 'a') as f:
                    f.write(f"处理被试 {subject_id} 的 {stage} 阶段数据时出现未捕获的异常: {e}\n")
                # 创建一个错误日志文件
                if stage == 'prac':
                    round_id = '01'
                    state = 'prac'
                elif stage.startswith('test'):
                    state = 'test'
                    if stage == 'test1':
                        round_id = '01'
                    elif stage == 'test2':
                        round_id = '02'
                    elif stage == 'test3':
                        round_id = '03'
                elif stage.startswith('rest'):
                    state = 'rest'
                    if stage == 'rest1':
                        round_id = '01'
                    elif stage == 'rest2':
                        round_id = '02'
                    elif stage == 'rest3':
                        round_id = '03'

                error_file = os.path.join(output_dir, f"{subject_id}_{round_id}_{state}_exception.txt")
                with open(error_file, 'w') as f:
                    f.write(f"处理被试 {subject_id} 的 {stage} 阶段数据时出现未捕获的异常: {e}\n")
                print(f"已创建错误日志文件: {error_file}")

    # 打印处理结果摘要
    print("\n处理结果摘要:")
    with open(log_file, 'a') as f:
        f.write("\n处理结果摘要:\n")
        for subject_id, processed_stages in processed_subjects.items():
            if processed_stages:
                summary = f"被试 {subject_id}: 成功处理 {len(processed_stages)}/{len(stages)} 个阶段 - {', '.join(processed_stages)}"
                print(summary)
                f.write(f"{summary}\n")
            else:
                summary = f"被试 {subject_id}: 未成功处理任何阶段"
                print(summary)
                f.write(f"{summary}\n")

    # 合并所有被试的数据
    try:
        merge_subject_data(subject_ids, stages, output_dir)
        with open(log_file, 'a') as f:
            f.write("\n成功合并所有被试的数据\n")
    except Exception as e:
        print(f"合并所有被试的数据时出现未捕获的异常: {e}")
        with open(log_file, 'a') as f:
            f.write(f"\n合并所有被试的数据时出现未捕获的异常: {e}\n")

    # 创建带有固定metadata的数据
    try:
        create_fixed_data(subject_ids, stages, output_dir)
        with open(log_file, 'a') as f:
            f.write("\n成功创建带有固定metadata的数据\n")
    except Exception as e:
        print(f"创建带有固定metadata的数据时出现未捕获的异常: {e}")
        with open(log_file, 'a') as f:
            f.write(f"\n创建带有固定metadata的数据时出现未捕获的异常: {e}\n")

    # 记录结束时间
    with open(log_file, 'a') as f:
        f.write(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

    print("\nHEP数据提取完成！")
    print(f"输出文件保存在: {output_dir}")
    print(f"处理日志保存在: {log_file}")

if __name__ == "__main__":
    main()
