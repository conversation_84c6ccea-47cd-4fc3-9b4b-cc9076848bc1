#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
比较静息态和刺激态的HEP数据

功能：
- 加载静息态和刺激态的HEP分析结果
- 比较不同条件下的HEP波形
- 进行统计分析和可视化

作者：AI助手
日期：2024年
"""

import numpy as np
import pandas as pd
import os
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
import matplotlib as mpl
import argparse
from scipy import stats
import h5py
import seaborn as sns

# 设置中文字体
try:
    font = FontProperties(fname=r"C:\Windows\Fonts\LXGW WenKai Mono.ttf")
    plt.rcParams['font.family'] = ['sans-serif']
    plt.rcParams['font.sans-serif'] = ['LXGW WenKai']
    plt.rcParams['axes.unicode_minus'] = False
    mpl.rcParams['font.size'] = 10
except:
    print("警告: 未找到LXGW WenKai字体，使用系统默认字体")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='比较静息态和刺激态的HEP数据')
    parser.add_argument('--data_dir', type=str, 
                        default="D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/raw_epochs",
                        help='数据目录')
    parser.add_argument('--output_dir', type=str, 
                        default="D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/comparison_results",
                        help='输出目录')
    parser.add_argument('--rest_stage', type=str, default="rest1",
                        help='静息态阶段')
    parser.add_argument('--test_stage', type=str, default="test1",
                        help='刺激态阶段')
    parser.add_argument('--channels', type=str, default="Fz,Cz,Pz",
                        help='要分析的通道，用逗号分隔')
    return parser.parse_args()

def load_h5_file(file_path):
    """
    加载HDF5文件
    
    参数:
    file_path (str): 文件路径
    
    返回:
    dict: 包含数据的字典
    """
    print(f"加载HDF5文件: {file_path}")
    
    with h5py.File(file_path, 'r') as f:
        # 获取数据
        data = f['data'][:]
        times = f['times'][:]
        ch_names = [ch.decode('utf-8') for ch in f['ch_names'][:]]
        subject_ids = [subj.decode('utf-8') for subj in f['subject_ids'][:]]
        stages = [stg.decode('utf-8') for stg in f['stages'][:]]
        
        # 获取属性
        attrs = dict(f.attrs)
    
    print(f"成功加载数据: {data.shape[0]}个epochs, {data.shape[1]}个通道, {data.shape[2]}个时间点")
    
    return {
        'data': data,
        'times': times,
        'ch_names': ch_names,
        'subject_ids': subject_ids,
        'stages': stages,
        'attrs': attrs
    }

def compare_hep_waveforms(rest_dict, test_dict, channels, output_dir):
    """
    比较静息态和刺激态的HEP波形
    
    参数:
    rest_dict (dict): 静息态数据字典
    test_dict (dict): 刺激态数据字典
    channels (list): 要分析的通道列表
    output_dir (str): 输出目录
    """
    print("比较静息态和刺激态的HEP波形...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取数据
    rest_data = rest_dict['data']
    test_data = test_dict['data']
    times = rest_dict['times']
    ch_names = rest_dict['ch_names']
    rest_subject_ids = rest_dict['subject_ids']
    test_subject_ids = test_dict['subject_ids']
    
    # 获取通道索引
    ch_indices = [ch_names.index(ch) for ch in channels if ch in ch_names]
    
    if not ch_indices:
        print(f"错误: 未找到指定的通道: {channels}")
        return
    
    # 找出两个条件下共有的被试
    rest_unique_subjects = np.unique(rest_subject_ids)
    test_unique_subjects = np.unique(test_subject_ids)
    common_subjects = np.intersect1d(rest_unique_subjects, test_unique_subjects)
    
    print(f"静息态被试数量: {len(rest_unique_subjects)}")
    print(f"刺激态被试数量: {len(test_unique_subjects)}")
    print(f"共有被试数量: {len(common_subjects)}")
    
    # 创建字典，用于存储每个被试的数据索引
    rest_subject_indices = {subj: [] for subj in common_subjects}
    test_subject_indices = {subj: [] for subj in common_subjects}
    
    for i, subj in enumerate(rest_subject_ids):
        if subj in common_subjects:
            rest_subject_indices[subj].append(i)
    
    for i, subj in enumerate(test_subject_ids):
        if subj in common_subjects:
            test_subject_indices[subj].append(i)
    
    # 计算每个被试在每个通道上的平均HEP
    rest_subject_means = np.zeros((len(common_subjects), len(ch_indices), len(times)))
    test_subject_means = np.zeros((len(common_subjects), len(ch_indices), len(times)))
    
    for i, subj in enumerate(common_subjects):
        rest_indices = rest_subject_indices[subj]
        test_indices = test_subject_indices[subj]
        
        rest_subject_data = rest_data[rest_indices]
        test_subject_data = test_data[test_indices]
        
        rest_subject_means[i] = np.mean(rest_subject_data[:, ch_indices, :], axis=0)
        test_subject_means[i] = np.mean(test_subject_data[:, ch_indices, :], axis=0)
    
    # 计算所有被试的总平均HEP
    rest_grand_mean = np.mean(rest_subject_means, axis=0)
    test_grand_mean = np.mean(test_subject_means, axis=0)
    
    # 计算标准误差
    rest_sem = np.std(rest_subject_means, axis=0) / np.sqrt(len(common_subjects))
    test_sem = np.std(test_subject_means, axis=0) / np.sqrt(len(common_subjects))
    
    # 进行统计检验
    p_values = np.zeros((len(ch_indices), len(times)))
    t_values = np.zeros((len(ch_indices), len(times)))
    
    for i in range(len(ch_indices)):
        for j in range(len(times)):
            t, p = stats.ttest_rel(rest_subject_means[:, i, j], test_subject_means[:, i, j])
            p_values[i, j] = p
            t_values[i, j] = t
    
    # 绘制比较图
    plt.figure(figsize=(15, 10))
    
    for i, ch_idx in enumerate(ch_indices):
        ch_name = ch_names[ch_idx]
        plt.subplot(len(ch_indices), 1, i+1)
        
        # 绘制静息态平均波形
        plt.plot(times, rest_grand_mean[i], 'b-', linewidth=2, label=f'静息态')
        plt.fill_between(times, 
                         rest_grand_mean[i] - rest_sem[i], 
                         rest_grand_mean[i] + rest_sem[i], 
                         color='b', alpha=0.2)
        
        # 绘制刺激态平均波形
        plt.plot(times, test_grand_mean[i], 'r-', linewidth=2, label=f'刺激态')
        plt.fill_between(times, 
                         test_grand_mean[i] - test_sem[i], 
                         test_grand_mean[i] + test_sem[i], 
                         color='r', alpha=0.2)
        
        # 添加零线
        plt.axhline(y=0, color='k', linestyle='-', alpha=0.2)
        
        # 添加R波时刻的垂直线
        plt.axvline(x=0, color='k', linestyle='--', alpha=0.5, label='R波')
        
        # 添加时间窗口
        for start, end in [(0.2, 0.4), (0.4, 0.6), (0.5, 0.7)]:
            plt.axvspan(start, end, color='g', alpha=0.1)
            plt.text((start+end)/2, plt.ylim()[0]*0.9, f'{start}-{end}s', 
                     horizontalalignment='center', color='g')
        
        # 标记显著性差异
        sig_times = times[p_values[i] < 0.05]
        if len(sig_times) > 0:
            y_min, y_max = plt.ylim()
            y_range = y_max - y_min
            plt.plot(sig_times, np.ones_like(sig_times) * (y_min + y_range * 0.05), 'k*', markersize=5)
        
        plt.title(f'{ch_name} 通道的静息态vs刺激态HEP波形比较')
        plt.xlabel('时间 (秒)')
        plt.ylabel('振幅 (μV)')
        plt.legend()
        plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'rest_vs_test_hep_comparison.png'))
    plt.close()
    
    print(f"已保存HEP波形比较图至: {os.path.join(output_dir, 'rest_vs_test_hep_comparison.png')}")
    
    # 绘制差异热图
    plt.figure(figsize=(12, 8))
    diff = test_grand_mean - rest_grand_mean
    plt.imshow(diff, aspect='auto', 
               extent=[times[0], times[-1], len(ch_indices)-0.5, -0.5],
               cmap='RdBu_r', origin='upper')
    plt.colorbar(label='差异振幅 (μV)')
    plt.yticks(range(len(ch_indices)), [ch_names[idx] for idx in ch_indices])
    plt.axvline(x=0, color='k', linestyle='--', alpha=0.5, label='R波')
    
    # 添加时间窗口
    for start, end in [(0.2, 0.4), (0.4, 0.6), (0.5, 0.7)]:
        plt.axvspan(start, end, color='g', alpha=0.1)
        plt.text((start+end)/2, -0.4, f'{start}-{end}s', 
                 horizontalalignment='center', color='g')
    
    plt.title('静息态vs刺激态HEP差异热图 (刺激态-静息态)')
    plt.xlabel('时间 (秒)')
    plt.ylabel('通道')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'rest_vs_test_difference_heatmap.png'))
    plt.close()
    
    print(f"已保存差异热图至: {os.path.join(output_dir, 'rest_vs_test_difference_heatmap.png')}")
    
    # 绘制统计显著性热图
    plt.figure(figsize=(12, 8))
    plt.imshow(-np.log10(p_values), aspect='auto', 
               extent=[times[0], times[-1], len(ch_indices)-0.5, -0.5],
               cmap='hot', origin='upper')
    plt.colorbar(label='-log10(p值)')
    plt.yticks(range(len(ch_indices)), [ch_names[idx] for idx in ch_indices])
    plt.axvline(x=0, color='k', linestyle='--', alpha=0.5, label='R波')
    
    # 添加时间窗口
    for start, end in [(0.2, 0.4), (0.4, 0.6), (0.5, 0.7)]:
        plt.axvspan(start, end, color='g', alpha=0.1)
        plt.text((start+end)/2, -0.4, f'{start}-{end}s', 
                 horizontalalignment='center', color='g')
    
    # 添加显著性阈值线
    plt.axhline(y=-0.5, color='w', linestyle='--', alpha=0.5)
    plt.text(times[-1], -0.5, 'p=0.05', color='w', horizontalalignment='right')
    
    plt.title('静息态vs刺激态HEP差异的统计显著性')
    plt.xlabel('时间 (秒)')
    plt.ylabel('通道')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'rest_vs_test_significance_heatmap.png'))
    plt.close()
    
    print(f"已保存统计显著性热图至: {os.path.join(output_dir, 'rest_vs_test_significance_heatmap.png')}")
    
    return rest_grand_mean, test_grand_mean, p_values, t_values

def compare_time_windows(rest_dict, test_dict, channels, output_dir):
    """
    比较不同时间窗口的HEP振幅
    
    参数:
    rest_dict (dict): 静息态数据字典
    test_dict (dict): 刺激态数据字典
    channels (list): 要分析的通道列表
    output_dir (str): 输出目录
    """
    print("比较不同时间窗口的HEP振幅...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取数据
    rest_data = rest_dict['data']
    test_data = test_dict['data']
    times = rest_dict['times']
    ch_names = rest_dict['ch_names']
    rest_subject_ids = rest_dict['subject_ids']
    test_subject_ids = test_dict['subject_ids']
    
    # 获取通道索引
    ch_indices = [ch_names.index(ch) for ch in channels if ch in ch_names]
    
    if not ch_indices:
        print(f"错误: 未找到指定的通道: {channels}")
        return
    
    # 定义时间窗口
    time_windows = [(0.2, 0.4), (0.4, 0.6), (0.5, 0.7)]
    
    # 找出两个条件下共有的被试
    rest_unique_subjects = np.unique(rest_subject_ids)
    test_unique_subjects = np.unique(test_subject_ids)
    common_subjects = np.intersect1d(rest_unique_subjects, test_unique_subjects)
    
    # 创建字典，用于存储每个被试的数据索引
    rest_subject_indices = {subj: [] for subj in common_subjects}
    test_subject_indices = {subj: [] for subj in common_subjects}
    
    for i, subj in enumerate(rest_subject_ids):
        if subj in common_subjects:
            rest_subject_indices[subj].append(i)
    
    for i, subj in enumerate(test_subject_ids):
        if subj in common_subjects:
            test_subject_indices[subj].append(i)
    
    # 计算每个被试在每个通道和每个时间窗口的平均振幅
    results = []
    
    for subj in common_subjects:
        rest_indices = rest_subject_indices[subj]
        test_indices = test_subject_indices[subj]
        
        rest_subject_data = rest_data[rest_indices]
        test_subject_data = test_data[test_indices]
        
        for ch_idx in ch_indices:
            ch_name = ch_names[ch_idx]
            
            for start, end in time_windows:
                # 获取时间窗口索引
                time_mask = (times >= start) & (times <= end)
                
                # 计算静息态平均振幅
                rest_mean_amp = np.mean(rest_subject_data[:, ch_idx, :][:, time_mask], axis=(0, 1))
                
                # 计算刺激态平均振幅
                test_mean_amp = np.mean(test_subject_data[:, ch_idx, :][:, time_mask], axis=(0, 1))
                
                results.append({
                    'subject_id': subj,
                    'channel': ch_name,
                    'window_start': start,
                    'window_end': end,
                    'rest_amplitude': rest_mean_amp,
                    'test_amplitude': test_mean_amp,
                    'difference': test_mean_amp - rest_mean_amp
                })
    
    # 转换为DataFrame
    results_df = pd.DataFrame(results)
    
    # 保存结果
    results_path = os.path.join(output_dir, 'rest_vs_test_time_window_comparison.csv')
    results_df.to_csv(results_path, index=False)
    
    print(f"已保存时间窗口比较结果至: {results_path}")
    
    # 进行统计分析
    stats_results = []
    
    for ch_name in channels:
        if ch_name not in ch_names:
            continue
            
        for start, end in time_windows:
            # 筛选该通道和时间窗口的数据
            mask = (results_df['channel'] == ch_name) & \
                   (results_df['window_start'] == start) & \
                   (results_df['window_end'] == end)
            
            window_df = results_df[mask]
            
            # 配对t检验
            t, p = stats.ttest_rel(window_df['rest_amplitude'], window_df['test_amplitude'])
            
            # 计算效应量 (Cohen's d)
            d = (window_df['test_amplitude'].mean() - window_df['rest_amplitude'].mean()) / \
                np.sqrt((window_df['test_amplitude'].std()**2 + window_df['rest_amplitude'].std()**2) / 2)
            
            stats_results.append({
                'channel': ch_name,
                'window_start': start,
                'window_end': end,
                'rest_mean': window_df['rest_amplitude'].mean(),
                'rest_std': window_df['rest_amplitude'].std(),
                'test_mean': window_df['test_amplitude'].mean(),
                'test_std': window_df['test_amplitude'].std(),
                'diff_mean': window_df['difference'].mean(),
                'diff_std': window_df['difference'].std(),
                't_stat': t,
                'p_value': p,
                'cohen_d': d,
                'n': len(window_df)
            })
    
    # 转换为DataFrame
    stats_df = pd.DataFrame(stats_results)
    
    # 保存统计结果
    stats_path = os.path.join(output_dir, 'rest_vs_test_statistics.csv')
    stats_df.to_csv(stats_path, index=False)
    
    print(f"已保存统计分析结果至: {stats_path}")
    
    # 绘制条形图
    plt.figure(figsize=(15, 10))
    
    for i, ch_name in enumerate(channels):
        if ch_name not in ch_names:
            continue
            
        plt.subplot(len(channels), 1, i+1)
        
        # 筛选该通道的数据
        ch_stats = stats_df[stats_df['channel'] == ch_name]
        
        # 准备数据
        window_labels = [f"{start}-{end}s" for start, end in time_windows]
        x = np.arange(len(window_labels))
        width = 0.35
        
        # 绘制条形图
        plt.bar(x - width/2, ch_stats['rest_mean'], width, label='静息态', color='blue', alpha=0.7)
        plt.bar(x + width/2, ch_stats['test_mean'], width, label='刺激态', color='red', alpha=0.7)
        
        # 添加误差线
        plt.errorbar(x - width/2, ch_stats['rest_mean'], yerr=ch_stats['rest_std']/np.sqrt(ch_stats['n']), 
                     fmt='none', ecolor='blue', capsize=5)
        plt.errorbar(x + width/2, ch_stats['test_mean'], yerr=ch_stats['test_std']/np.sqrt(ch_stats['n']), 
                     fmt='none', ecolor='red', capsize=5)
        
        # 标记显著性
        for j, p in enumerate(ch_stats['p_value']):
            if p < 0.001:
                plt.text(x[j], max(ch_stats['rest_mean'].iloc[j], ch_stats['test_mean'].iloc[j]) + 0.5, 
                         '***', ha='center', va='bottom')
            elif p < 0.01:
                plt.text(x[j], max(ch_stats['rest_mean'].iloc[j], ch_stats['test_mean'].iloc[j]) + 0.5, 
                         '**', ha='center', va='bottom')
            elif p < 0.05:
                plt.text(x[j], max(ch_stats['rest_mean'].iloc[j], ch_stats['test_mean'].iloc[j]) + 0.5, 
                         '*', ha='center', va='bottom')
        
        plt.xticks(x, window_labels)
        plt.title(f'{ch_name} 通道在不同时间窗口的静息态vs刺激态HEP振幅比较')
        plt.xlabel('时间窗口')
        plt.ylabel('平均振幅 (μV)')
        plt.legend()
        plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'rest_vs_test_bar_chart.png'))
    plt.close()
    
    print(f"已保存条形图至: {os.path.join(output_dir, 'rest_vs_test_bar_chart.png')}")
    
    return results_df, stats_df

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    print(f"比较{args.rest_stage}和{args.test_stage}阶段的HEP数据...")
    print(f"数据目录: {args.data_dir}")
    print(f"输出目录: {args.output_dir}")
    print(f"分析通道: {args.channels}")
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 查找静息态和刺激态的HDF5文件
    rest_files = [f for f in os.listdir(args.data_dir) if f.startswith(f"{args.rest_stage}_raw_epochs") and f.endswith('.h5')]
    test_files = [f for f in os.listdir(args.data_dir) if f.startswith(f"{args.test_stage}_raw_epochs") and f.endswith('.h5')]
    
    if not rest_files:
        print(f"错误: 未找到{args.rest_stage}阶段的HDF5文件")
        return
    
    if not test_files:
        print(f"错误: 未找到{args.test_stage}阶段的HDF5文件")
        return
    
    # 选择最新的文件
    rest_file = max([os.path.join(args.data_dir, f) for f in rest_files], key=os.path.getmtime)
    test_file = max([os.path.join(args.data_dir, f) for f in test_files], key=os.path.getmtime)
    
    print(f"静息态文件: {os.path.basename(rest_file)}")
    print(f"刺激态文件: {os.path.basename(test_file)}")
    
    # 加载数据
    rest_dict = load_h5_file(rest_file)
    test_dict = load_h5_file(test_file)
    
    # 解析通道列表
    channels = args.channels.split(',')
    
    # 比较HEP波形
    rest_grand_mean, test_grand_mean, p_values, t_values = compare_hep_waveforms(
        rest_dict, test_dict, channels, args.output_dir)
    
    # 比较时间窗口
    results_df, stats_df = compare_time_windows(
        rest_dict, test_dict, channels, args.output_dir)
    
    print(f"\n{args.rest_stage}和{args.test_stage}阶段的HEP数据比较完成")
    print(f"结果已保存至: {args.output_dir}")

if __name__ == "__main__":
    main()
