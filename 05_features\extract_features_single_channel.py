#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HEP特征提取 - 单通道版本

功能：
- 提取所有被试在所有阶段的单通道HEP特征
- 不提取通道组特征
- 将结果保存为npz格式

作者：AI助手
日期：2024年
"""

import os
import numpy as np
import pandas as pd
import mne
import argparse
from datetime import datetime
import time

# 定义常量
ROOT_DIR = r"D:/ecgeeg/30-数据分析/5-NeuroKit2/result"
DATA_DIR = r"D:/ecgeeg/19-eegecg手动预处理6-ICA3"
OUTPUT_DIR = os.path.join(ROOT_DIR, 'hep_analysis', 'features')

# 定义关键通道
KEY_CHANNELS = ['Fz', 'Cz', 'Pz', 'FCz', 'CPz']

# 定义阶段映射
STAGE_MAPPING = {
    'prac': ('01', 'rest'),
    'rest1': ('01', 'rest'),
    'test1': ('01', 'test'),
    'rest2': ('02', 'rest'),
    'test2': ('02', 'test'),
    'rest3': ('03', 'rest'),
    'test3': ('03', 'test')
}

# 定义所有阶段
STAGES = list(STAGE_MAPPING.keys())

# 定义阶段显示名称
STAGE_DISPLAY = {
    'prac': '练习阶段',
    'rest1': '静息态1',
    'test1': '刺激态1',
    'rest2': '静息态2',
    'test2': '刺激态2',
    'rest3': '静息态3',
    'test3': '刺激态3'
}

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='HEP特征提取 - 单通道版本')
    parser.add_argument('--data_dir', type=str, default=DATA_DIR,
                        help='预处理数据目录')
    parser.add_argument('--output_dir', type=str, default=OUTPUT_DIR,
                        help='输出目录')
    parser.add_argument('--all_channels', action='store_true',
                        help='是否提取所有通道的特征')
    parser.add_argument('--time_windows', type=str, default='0.2-0.4,0.4-0.6,0.5-0.7',
                        help='时间窗口列表，用逗号分隔，格式为"start-end"')
    return parser.parse_args()

def load_hep_data(data_dir, stage):
    """
    加载指定阶段的HEP数据
    
    参数:
    data_dir (str): 数据目录
    stage (str): 实验阶段
    
    返回:
    mne.Epochs: 加载的HEP数据
    """
    print(f"加载{STAGE_DISPLAY[stage]}阶段的HEP数据...")
    
    # 获取轮次ID和状态
    round_id, state = STAGE_MAPPING[stage]
    
    # 查找所有符合条件的文件
    all_files = []
    all_subjects = []
    
    # 遍历数据目录
    for file in os.listdir(data_dir):
        # 检查文件是否符合条件
        if file.endswith(f'_{state}.fif') and f'_{round_id}_' in file:
            file_path = os.path.join(data_dir, file)
            
            # 从文件名中提取被试ID
            subject_id = file.split('_')[0]
            
            all_files.append(file_path)
            all_subjects.append(subject_id)
    
    if not all_files:
        print(f"错误: 未找到{stage}阶段的数据文件")
        exit(1)
    
    print(f"找到{len(all_files)}个{stage}阶段的数据文件")
    
    # 加载所有文件并合并
    all_epochs = []
    
    # 处理所有被试
    for file_path, subject_id in zip(all_files, all_subjects):
        # 加载数据
        raw = mne.io.read_raw_fif(file_path, preload=True)
        
        # 检测R峰
        ecg_channel = None
        for ch in raw.ch_names:
            if 'ECG' in ch.upper():
                ecg_channel = ch
                break
        
        if ecg_channel is None:
            print(f"错误: 文件 {file_path} 中未找到ECG通道")
            exit(1)
        
        # 使用MNE的find_ecg_events函数检测R峰
        events = mne.preprocessing.find_ecg_events(raw, ch_name=ecg_channel, verbose=False)
        
        # 提取HEP epochs
        epochs = mne.Epochs(raw, events[0], tmin=-0.2, tmax=0.8, baseline=None, preload=True)
        
        # 添加metadata
        epochs.metadata = pd.DataFrame({
            'subject_id': [subject_id] * len(epochs),
            'stage': [stage] * len(epochs)
        })
        
        all_epochs.append(epochs)
        
        print(f"成功加载被试 {subject_id} 的{stage}阶段数据，包含{len(epochs)}个epochs")
    
    if not all_epochs:
        print(f"错误: 未能成功加载任何{stage}阶段的数据")
        exit(1)
    
    # 合并所有epochs
    merged_epochs = mne.concatenate_epochs(all_epochs)
    
    print(f"成功加载{STAGE_DISPLAY[stage]}阶段的数据，包含{len(merged_epochs)}个epochs，来自{len(merged_epochs.metadata['subject_id'].unique())}个被试")
    return merged_epochs

def extract_features(epochs, time_windows, all_channels=True):
    """
    提取HEP特征
    
    参数:
    epochs (mne.Epochs): HEP数据
    time_windows (list): 时间窗口列表
    all_channels (bool): 是否提取所有通道的特征
    
    返回:
    pd.DataFrame: 提取的特征
    """
    print("提取HEP特征...")
    
    # 如果提取所有通道，则使用所有通道
    if all_channels:
        channels = epochs.ch_names
    else:
        channels = KEY_CHANNELS
    
    # 创建特征列表
    features = []
    
    # 获取所有被试ID
    subject_ids = epochs.metadata['subject_id'].unique()
    
    # 对每个被试进行处理
    for subject_id in subject_ids:
        print(f"处理被试 {subject_id}...")
        
        # 获取该被试的数据
        subj_epochs = epochs[epochs.metadata['subject_id'] == subject_id]
        
        # 对每个通道进行处理
        for channel in channels:
            if channel in subj_epochs.ch_names:
                # 获取通道索引
                ch_idx = subj_epochs.ch_names.index(channel)
                
                # 获取通道数据
                channel_data = subj_epochs.get_data()[:, ch_idx, :]
                
                # 对每个时间窗口进行处理
                for time_window in time_windows:
                    # 获取时间窗口索引
                    time_mask = (subj_epochs.times >= time_window[0]) & (subj_epochs.times <= time_window[1])
                    
                    # 计算时间窗口内的平均值
                    window_mean = np.mean(channel_data[:, time_mask], axis=1)
                    
                    # 计算每个epoch的平均值
                    for i, epoch_mean in enumerate(window_mean):
                        # 获取epoch的metadata
                        epoch_metadata = subj_epochs.metadata.iloc[i]
                        
                        # 添加特征
                        features.append({
                            'subject_id': subject_id,
                            'channel': channel,
                            'window_start': time_window[0],
                            'window_end': time_window[1],
                            'epoch_index': i,
                            'mean_amplitude': epoch_mean,
                            'stage': epoch_metadata.get('stage', None)
                        })
    
    # 转换为DataFrame
    features_df = pd.DataFrame(features)
    
    print(f"成功提取特征，共{len(features_df)}条记录")
    return features_df

def save_features_npz(features_df, output_dir, filename):
    """
    保存特征为npz格式
    
    参数:
    features_df (pd.DataFrame): 特征
    output_dir (str): 输出目录
    filename (str): 文件名
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 构建文件路径
    file_path = os.path.join(output_dir, filename)
    
    # 保存为npz
    np.savez_compressed(
        file_path,
        subject_id=features_df['subject_id'].values,
        channel=features_df['channel'].values,
        window_start=features_df['window_start'].values,
        window_end=features_df['window_end'].values,
        epoch_index=features_df['epoch_index'].values,
        mean_amplitude=features_df['mean_amplitude'].values,
        stage=features_df['stage'].values
    )
    print(f"已保存特征至: {file_path}")
    
    # 同时保存一份CSV作为参考
    csv_path = file_path.replace('.npz', '.csv')
    features_df.to_csv(csv_path, index=False)
    print(f"已保存CSV参考文件至: {csv_path}")

def main():
    """主函数"""
    # 记录开始时间
    start_time = time.time()
    
    # 解析命令行参数
    args = parse_arguments()
    
    # 解析时间窗口列表
    time_windows = []
    for window_str in args.time_windows.split(','):
        start, end = map(float, window_str.split('-'))
        time_windows.append((start, end))
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 记录每个阶段的记录数
    stage_records = {}
    total_records = 0
    saved_files = []
    
    # 处理每个阶段
    for stage in STAGES:
        # 加载数据
        epochs = load_hep_data(args.data_dir, stage)
        
        # 添加阶段信息到metadata
        if 'stage' not in epochs.metadata.columns:
            epochs.metadata['stage'] = stage
        
        # 提取特征
        features = extract_features(epochs, time_windows, args.all_channels)
        
        # 记录该阶段的记录数
        stage_records[stage] = len(features)
        total_records += len(features)
        
        # 保存特征
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'{stage}_features_{timestamp}.npz'
        save_features_npz(features, args.output_dir, filename)
        saved_files.append(os.path.join(args.output_dir, filename))
    
    # 计算总处理时间
    end_time = time.time()
    total_time = end_time - start_time
    
    # 打印统计信息
    print("\n===== HEP特征提取统计 =====")
    print(f"总处理时间: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")
    print("\n各阶段记录数:")
    for stage, count in stage_records.items():
        print(f"  {STAGE_DISPLAY[stage]}: {count}条记录")
    print(f"\n总记录数: {total_records}条")
    
    print("\n保存的文件:")
    for file_path in saved_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # 转换为MB
            print(f"  {os.path.basename(file_path)}: {file_size:.2f} MB")
    
    print("\n特征提取完成")

if __name__ == "__main__":
    main()
