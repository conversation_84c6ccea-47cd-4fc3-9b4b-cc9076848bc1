import os
import pandas as pd
import numpy as np
from scipy.stats import ttest_rel, wilcoxon
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from matplotlib.transforms import Bbox

# 工具函数：自动选择不重叠角落
def auto_place_legend_and_text(ax, legend_labels, pr_text, fontsize_legend=20, fontsize_text=18, pr_loc=None):
    corners = ['upper right', 'upper left', 'lower right', 'lower left']
    legend = None
    if legend_labels:
        for loc in corners:
            legend = ax.legend(legend_labels, fontsize=fontsize_legend, loc=loc, frameon=False)
            fig = ax.get_figure()
            fig.canvas.draw()
            legend_bbox = legend.get_window_extent()
            break
    xlim = ax.get_xlim()
    ylim = ax.get_ylim()
    # 设置偏移量，防止文本与坐标轴重叠
    x_offset = 0.03 * (xlim[1] - xlim[0])
    y_offset = 0.05 * (ylim[1] - ylim[0])
    xy_dict = {
        'upper right': (xlim[1] - x_offset, ylim[1] - y_offset),
        'upper left': (xlim[0] + x_offset, ylim[1] - y_offset),
        'lower right': (xlim[1] - x_offset, ylim[0] + y_offset),
        'lower left': (xlim[0] + x_offset, ylim[0] + y_offset),
    }
    # 指定pr_loc优先，否则自动避开图例
    if pr_loc is not None:
        x, y = xy_dict[pr_loc]
        text = ax.text(x, y, pr_text, fontsize=fontsize_text, color='black', ha='left', va='top' if 'upper' in pr_loc else 'bottom')
        return legend, text
    for loc in corners:
        x, y = xy_dict[loc]
        text = ax.text(x, y, pr_text, fontsize=fontsize_text, color='black', ha='left', va='top' if 'upper' in loc else 'bottom')
        fig = ax.get_figure()
        fig.canvas.draw()
        if legend_labels:
            legend_bbox = legend.get_window_extent()
            text_bbox = text.get_window_extent()
            if not legend_bbox.overlaps(text_bbox):
                break
            else:
                text.remove()
        else:
            break
    return legend, text

plt.rcParams['font.sans-serif'] = ['Times New Roman']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['text.color'] = 'black'
plt.rcParams['axes.labelcolor'] = 'black'
plt.rcParams['xtick.color'] = 'black'
plt.rcParams['ytick.color'] = 'black'

# 自动查找最新的特征表
feature_dir = r"D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/features"
files = [f for f in os.listdir(feature_dir) if f.startswith('central_eeg_features_0.4_0.6') and f.endswith('.xlsx')]
assert len(files) > 0, '未找到特征表！'
files.sort(key=lambda x: os.path.getmtime(os.path.join(feature_dir, x)), reverse=True)
file = os.path.join(feature_dir, files[0])
print(f"读取特征文件: {file}")
df = pd.read_excel(file)

# 只保留rest1和test3
features = [c for c in df.columns if c not in ['subject_id', 'stage', 'channel']]
subjects = set(df[df['stage']=='rest1']['subject_id']) & set(df[df['stage']=='test3']['subject_id'])
channels = sorted(df['channel'].unique())

# 针对每个被试-阶段，中央区所有通道特征取均值
agg_rows = []
for subject in subjects:
    for stage in ['rest1', 'test3']:
        subdf = df[(df['subject_id']==subject) & (df['stage']==stage)]
        if subdf.empty:
            continue
        row = {'subject_id': subject, 'stage': stage}
        for feat in features:
            row[feat] = subdf[feat].mean()
        agg_rows.append(row)
agg_df = pd.DataFrame(agg_rows)
rest1 = agg_df[agg_df['stage']=='rest1'].sort_values('subject_id')
test3 = agg_df[agg_df['stage']=='test3'].sort_values('subject_id')
assert all(rest1['subject_id'].values == test3['subject_id'].values)

# 统计分析
stat_table = []
for feat in features:
    x = rest1[feat].values
    y = test3[feat].values
    t_stat, p_val = ttest_rel(x, y)
    w_stat, w_p = wilcoxon(x, y)
    mean1, mean2 = np.mean(x), np.mean(y)
    sem1, sem2 = np.std(x, ddof=1)/np.sqrt(len(x)), np.std(y, ddof=1)/np.sqrt(len(y))
    d = abs(mean1-mean2)/np.sqrt((np.std(x, ddof=1)**2 + np.std(y, ddof=1)**2)/2)
    r = d/np.sqrt(d**2+4)
    stat_table.append({'feature': feat, 'rest1_mean': mean1, 'rest1_sem': sem1, 'test3_mean': mean2, 'test3_sem': sem2,
                      't': t_stat, 'p': p_val, 'wilcoxon_p': w_p, 'cohen_d': d, 'r': r})
stat_df = pd.DataFrame(stat_table)
stat_df.to_excel(os.path.join(feature_dir, 'central_features_stats_0.4_0.6.xlsx'), index=False)
print('已保存统计结果表格 central_features_stats_0.4_0.6.xlsx')

# 中文特征名映射
feature_map = {
    'mean': '均值', 'std': '标准差', 'max': '最大值', 'min': '最小值',
    'pos_peak': '正峰值', 'neg_peak': '负峰值', 'pos_peak_latency': '正峰延迟', 'neg_peak_latency': '负峰延迟',
    'peak2peak': '峰-峰值', 'rms': 'RMS', 'energy': '能量', 'slope': '斜率',
    'skew': '偏度', 'kurtosis': '峰度', 'main_freq': '主频率', 'spec_centroid': '频谱质心',
    'hjorth_activity': '活动度', 'hjorth_mobility': '流动度', 'hjorth_complexity': '复杂度'
}

# 特征单位映射
feature_unit_map = {
    'mean': 'μV', 'std': 'μV', 'max': 'μV', 'min': 'μV',
    'pos_peak': 'μV', 'neg_peak': 'μV', 'pos_peak_latency': 's', 'neg_peak_latency': 's',
    'peak2peak': 'μV', 'rms': 'μV', 'energy': 'μV²', 'slope': 'μV/s',
    'skew': '', 'kurtosis': '', 'main_freq': 'Hz', 'spec_centroid': 'Hz',
    'hjorth_activity': 'μV²', 'hjorth_mobility': '', 'hjorth_complexity': ''
}

# 分离能量和其他特征
energy_idx = features.index('energy') if 'energy' in features else None
main_features = [f for f in features if f != 'energy']
main_stat_df = stat_df[stat_df['feature'].isin(main_features)]

# 只保留p值显著的特征，并按p值从小到大排序，右侧最多8个
significant_stat_df = main_stat_df[main_stat_df['p'] < 0.05].sort_values('p').reset_index(drop=True)

if len(significant_stat_df) == 0:
    print('无P值显著的特征！')
    exit()

main_feature_row = significant_stat_df.iloc[0]
right_features_df = significant_stat_df.iloc[1:9]  # 最多8个

plt.figure(figsize=(18, 18))
gs = gridspec.GridSpec(4, 3, width_ratios=[2, 1, 1], wspace=0.38, hspace=0.38)

# 主图（左侧大图）
ax_main = plt.subplot(gs[:, 0])
bar_width = 0.22  # 更细
bar_gap = 0.08
bars1 = ax_main.bar([-bar_width/2-bar_gap/2], [main_feature_row['rest1_mean']], bar_width, yerr=[main_feature_row['rest1_sem']], 
                   label='静息态1', capsize=6, color='#1f77b4', edgecolor='black', linewidth=2, alpha=1)
bars2 = ax_main.bar([bar_width/2+bar_gap/2], [main_feature_row['test3_mean']], bar_width, yerr=[main_feature_row['test3_sem']], 
                   label='刺激态3', capsize=6, color='#d62728', edgecolor='black', linewidth=2, alpha=1)
minv = min(main_feature_row['rest1_mean']-main_feature_row['rest1_sem'], main_feature_row['test3_mean']-main_feature_row['test3_sem'])
maxv = max(main_feature_row['rest1_mean']+main_feature_row['rest1_sem'], main_feature_row['test3_mean']+main_feature_row['test3_sem'])
ymin = minv - 0.1 * abs(maxv-minv)
ymax = maxv + 0.1 * abs(maxv-minv)
ax_main.set_ylim([ymin, ymax])
yticks = np.linspace(ymin, ymax, 7)
ax_main.set_yticks(yticks)
ax_main.set_yticklabels([f"{y:.2f}" for y in yticks], fontsize=28)
if main_feature_row['p'] < 0.001:
    marker = '***'
elif main_feature_row['p'] < 0.01:
    marker = '**'
elif main_feature_row['p'] < 0.05:
    marker = '*'
else:
    marker = ''
pr_text = f"{marker}\np={main_feature_row['p']:.6f}\nr={main_feature_row['r']:.2f}" if marker else f"p={main_feature_row['p']:.6f}\nr={main_feature_row['r']:.2f}"
ax_main.set_xticks([0])
ax_main.set_xticklabels([""], fontsize=30, color='black')  # 横轴不再显示特征名
main_unit = feature_unit_map.get(main_feature_row['feature'], '')
ax_main.set_ylabel(main_unit, fontsize=30, color='black')
# 主标题
plt.suptitle("HEP心跳诱发电位表征压力状态的有效性(0.4-0.6s)", fontsize=22, color='black', y=0.98, ha='center')
# 在主标题下方添加图例
legend_labels = ['静息态1', '刺激态3']
legend_handles = [plt.Rectangle((0,0),1,1, color='#1f77b4', label='静息态1'), plt.Rectangle((0,0),1,1, color='#d62728', label='刺激态3')]
plt.legend(handles=legend_handles, labels=legend_labels, fontsize=22, loc='upper center', bbox_to_anchor=(0.5, 0.93), ncol=2, frameon=False)
# 主图标题
ax_main.set_title(feature_map.get(main_feature_row['feature'], main_feature_row['feature']), fontsize=22, color='black', pad=18)
ax_main.tick_params(axis='both', which='major', labelsize=22)
main_mean = (main_feature_row['rest1_mean'] + main_feature_row['test3_mean']) / 2
main_pr_loc = 'upper left' if main_mean >= 0 else 'lower left'
# 主图
# 不再传递legend_labels，主图和子图都不显示图例
# auto_place_legend_and_text(ax_main, ['静息态1', '刺激态3'], pr_text, fontsize_legend=26, fontsize_text=28, pr_loc=main_pr_loc)
auto_place_legend_and_text(ax_main, [], pr_text, fontsize_legend=0, fontsize_text=22, pr_loc=main_pr_loc)

# 加粗主图坐标轴
for spine in ax_main.spines.values():
    spine.set_linewidth(2)

# 右侧其他特征小图矩阵（4行2列）
for i, (idx, row) in enumerate(right_features_df.iterrows()):
    row_idx = i // 2
    col_idx = i % 2 + 1  # 右侧两列
    ax = plt.subplot(gs[row_idx, col_idx])
    bars1 = ax.bar([-bar_width/2-bar_gap/2], [row['rest1_mean']], bar_width, yerr=[row['rest1_sem']], 
           label='静息态1', capsize=6, color='#1f77b4', edgecolor='black', linewidth=2, alpha=1)
    bars2 = ax.bar([bar_width/2+bar_gap/2], [row['test3_mean']], bar_width, yerr=[row['test3_sem']], 
           label='刺激态3', capsize=6, color='#d62728', edgecolor='black', linewidth=2, alpha=1)
    minv = min(row['rest1_mean']-row['rest1_sem'], row['test3_mean']-row['test3_sem'])
    maxv = max(row['rest1_mean']+row['rest1_sem'], row['test3_mean']+row['test3_sem'])
    ymin = minv - 0.1 * abs(maxv-minv)
    ymax = maxv + 0.1 * abs(maxv-minv)
    ax.set_ylim([ymin, ymax])
    yticks = np.linspace(ymin, ymax, 7)
    ax.set_yticks(yticks)
    ax.set_yticklabels([f"{y:.2f}" for y in yticks], fontsize=22)
    if row['p'] < 0.001:
        marker = '***'
    elif row['p'] < 0.01:
        marker = '**'
    elif row['p'] < 0.05:
        marker = '*'
    else:
        marker = ''
    pr_text = f"{marker}\np={row['p']:.6f}\nr={row['r']:.2f}" if marker else f"p={row['p']:.6f}\nr={row['r']:.2f}"
    ax.set_xticks([0])
    ax.set_xticklabels([""], fontsize=22, color='black')  # 横轴不再显示特征名
    if col_idx == 1:
        unit = feature_unit_map.get(row['feature'], '')
        ax.set_ylabel(unit, fontsize=22, color='black')
    else:
        ax.set_ylabel("")
    ax.set_title(feature_map.get(row['feature'], row['feature']), fontsize=22, color='black', pad=12)
    ax.tick_params(axis='both', which='major', labelsize=22)
    mean_val = (row['rest1_mean'] + row['test3_mean']) / 2
    pr_loc = 'upper left' if mean_val >= 0 else 'lower left'
    auto_place_legend_and_text(ax, [], pr_text, fontsize_legend=0, fontsize_text=22, pr_loc=pr_loc)

    # 加粗子图坐标轴
    for spine in ax.spines.values():
        spine.set_linewidth(2)

plt.tight_layout()
plt.savefig(os.path.join(feature_dir, 'central_features_matrix_0.4_0.6.eps'), format='eps', bbox_inches='tight', transparent=False)
plt.close()
print('已保存左右布局特征矩阵图 central_features_matrix_0.4_0.6.eps') 