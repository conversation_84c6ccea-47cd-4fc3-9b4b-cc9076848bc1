import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib as mpl

# 设置全局字体和字号
mpl.rcParams['font.sans-serif'] = ['LXGW Wenkai']
mpl.rcParams['axes.unicode_minus'] = False
mpl.rcParams['font.size'] = 10
mpl.rcParams['figure.facecolor'] = 'white'
mpl.rcParams['axes.facecolor'] = 'white'

# 1. 读取HEP特征表
feature_dir = r"D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/features"
files = [f for f in os.listdir(feature_dir) if f.startswith('central_eeg_features_0.5_0.6') and f.endswith('.xlsx')]
files.sort(key=lambda x: os.path.getmtime(os.path.join(feature_dir, x)), reverse=True)
file = os.path.join(feature_dir, files[0])
hep_df = pd.read_excel(file)

# 2. 只保留6个目标阶段
target_stages = ['rest1', 'test1', 'rest2', 'test2', 'rest3', 'test3']
hep_df = hep_df[hep_df['stage'].isin(target_stages)]

# 3. 针对每个被试-阶段，中央区所有通道特征取均值
features = ['neg_peak_latency', 'rms']
agg_rows = []
for subject in hep_df['subject_id'].unique():
    for stage in target_stages:
        subdf = hep_df[(hep_df['subject_id']==subject) & (hep_df['stage']==stage)]
        if subdf.empty:
            continue
        row = {'subject_id': subject, 'stage': stage}
        for feat in features:
            row[feat] = subdf[feat].mean()
        agg_rows.append(row)
agg_df = pd.DataFrame(agg_rows)

# 4. 绘制每个被试的时序曲线
stage_labels = ['静息态1', '刺激态1', '静息态2', '刺激态2', '静息态3', '刺激态3']
stage_map = dict(zip(target_stages, stage_labels))
agg_df['stage_label'] = agg_df['stage'].map(stage_map)

# 新增：静息态1和刺激态3合成一张大图（1行2列子图），每个特征各一张
plot_stage_pairs = [('rest1', 'test3')]
stage_labels_map = {'rest1': '静息态1', 'test3': '刺激态3'}

for feat in features:
    # 取静息态1和刺激态3
    stage1, stage2 = 'rest1', 'test3'
    stage1_df = hep_df[hep_df['stage'] == stage1]
    stage2_df = hep_df[hep_df['stage'] == stage2]
    # 计算最大HEP数，补齐
    max_len1 = stage1_df.groupby('subject_id').size().max() if not stage1_df.empty else 0
    max_len2 = stage2_df.groupby('subject_id').size().max() if not stage2_df.empty else 0
    max_len = max(max_len1, max_len2)
    # 静息态1
    all_seqs1 = []
    for subject, subdf in stage1_df.groupby('subject_id'):
        y = subdf[feat].values
        if len(y) < max_len:
            y = np.pad(y, (0, max_len-len(y)), constant_values=np.nan)
        all_seqs1.append(y)
    all_seqs1 = np.array(all_seqs1) if all_seqs1 else np.full((1, max_len), np.nan)
    mean_seq1 = np.nanmean(all_seqs1, axis=0)
    std_seq1 = np.nanstd(all_seqs1, axis=0)
    # 刺激态3
    all_seqs2 = []
    for subject, subdf in stage2_df.groupby('subject_id'):
        y = subdf[feat].values
        if len(y) < max_len:
            y = np.pad(y, (0, max_len-len(y)), constant_values=np.nan)
        all_seqs2.append(y)
    all_seqs2 = np.array(all_seqs2) if all_seqs2 else np.full((1, max_len), np.nan)
    mean_seq2 = np.nanmean(all_seqs2, axis=0)
    std_seq2 = np.nanstd(all_seqs2, axis=0)
    x = np.arange(1, max_len+1)
    plt.figure(figsize=(8, 5))
    # 静息态1：蓝色
    line1, = plt.plot(x, mean_seq1, color='#1f77b4', linewidth=2, label='静息态1均值')
    fill1 = plt.fill_between(x, mean_seq1-std_seq1, mean_seq1+std_seq1, color='#1f77b4', alpha=0.25, label='静息态1±标准差')
    # 刺激态3：红色
    line2, = plt.plot(x, mean_seq2, color='#d62728', linewidth=2, label='刺激态3均值')
    fill2 = plt.fill_between(x, mean_seq2-std_seq2, mean_seq2+std_seq2, color='#d62728', alpha=0.25, label='刺激态3±标准差')
    plt.xlabel('HEP序号', fontsize=12)
    plt.ylabel(feat, fontsize=12)
    plt.title(f'{feat}在静息态1与刺激态3的特征序列对比', fontsize=16, fontweight='bold', fontname='LXGW Wenkai')
    plt.legend(['静息态1均值', '静息态1±标准差', '刺激态3均值', '刺激态3±标准差'], fontsize=12, loc='upper right', frameon=False)
    plt.tight_layout()
    out_dir = r'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/hep_analysis/hep_feature_stage_timecourse'
    os.makedirs(out_dir, exist_ok=True)
    plt.savefig(os.path.join(out_dir, f'hep_{feat}_rest1_vs_test3_timecourse.png'), dpi=600, bbox_inches='tight')
    plt.savefig(os.path.join(out_dir, f'hep_{feat}_rest1_vs_test3_timecourse.svg'), format='svg', bbox_inches='tight')
    plt.close() 