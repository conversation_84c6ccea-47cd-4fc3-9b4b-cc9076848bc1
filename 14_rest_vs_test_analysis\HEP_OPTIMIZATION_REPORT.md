# HEP信号处理科学优化报告

## 概述

根据您观察到的HEP图表波动问题，我们实施了一套科学的优化方法来改善信号质量和信噪比。本报告详细说明了所有实施的优化措施及其科学依据。

## 1. 滤波参数优化

### 原始配置 vs 优化配置

| 配置名称 | 频率范围 | 用途 | 科学依据 |
|---------|---------|------|---------|
| original | 0.1-75Hz | 原始设置（对比用） | 保留所有频率成分 |
| optimized | 0.5-45Hz | 第一级优化 | 减少低频漂移和高频噪声 |
| smooth | 1.0-30Hz | 第二级优化 | 进一步收窄频带，提高平滑度 |
| ultra_smooth | 0.5-20Hz | 极度平滑 | 极端抖动情况下使用 |

### 优化原理

1. **低频截止优化 (0.1Hz → 0.5Hz/1.0Hz)**
   - 移除慢波漂移和基线不稳定
   - 保留HEP相关的生理频率成分

2. **高频截止优化 (75Hz → 45Hz/30Hz/20Hz)**
   - 移除肌电伪迹和电源噪声
   - 保留HEP主要频率成分（通常<30Hz）

## 2. 数据预处理增强

### 伪迹检测与移除

实施了严格的质量控制标准：

```python
# 幅度检查
max_amplitude < 100μV

# 基线稳定性检查
baseline_std < 2μV

# 梯度异常检查
max_gradient < 3μV/采样点
```

### 心跳周期质量控制

```python
RR_INTERVAL_LIMITS = {
    'min_rr': 0.4,      # 最小R-R间期（秒）- 150 BPM
    'max_rr': 1.5,      # 最大R-R间期（秒）- 40 BPM
    'min_cycles': 100   # 每个条件最少心跳周期数
}
```

## 3. 信号平滑技术

### 移动平均平滑

- **窗口大小**: 5个采样点
- **实施时机**: 滤波和基线矫正后
- **效果**: 减少高频抖动，保持HEP形态

### 统一基线矫正

- **时间窗口**: -200ms到0ms
- **方法**: 全局基线均值计算
- **优势**: 确保所有条件使用相同基线标准

## 4. 质量控制标准

### 科学验证指标

| 指标 | 阈值 | 科学依据 |
|------|------|---------|
| 基线标准差 | < 2μV | 确保0ms前基线平稳 |
| HEP最小幅度 | > 0.5μV | 确保200ms后HEP成分可见 |
| 最大梯度 | < 3μV/点 | 控制信号抖动程度 |
| 信噪比 | > 2.0 | 确保可靠的HEP检测 |

### 自动质量评估

系统自动检查：
- ✅ 基线期稳定性
- ✅ HEP成分可见性  
- ✅ 信号平滑度
- ✅ 信噪比水平
- ✅ 200ms后HEP峰值

## 5. 输出要求实现

### 生成的对比图表

1. **主分析图**: 优化后的HEP静息阶段对比
2. **滤波对比图**: 原始vs优化滤波效果对比
3. **多参数对比图**: 所有滤波配置的性能评估

### 参数设置报告

每次分析都会输出：
- 使用的具体滤波参数
- 数据质量指标
- 处理步骤详情
- 智能优化建议

### 数据质量改善度量

定量指标：
- 噪声降低百分比
- 信噪比改善程度
- 基线稳定性提升
- 信号平滑度改善

## 6. 智能推荐系统

### 自适应参数选择

系统会根据数据质量自动推荐：

```python
if 信号抖动严重:
    推荐 CURRENT_FILTER = 'smooth' (1.0-30Hz)
    
if 信噪比过低:
    推荐 CURRENT_FILTER = 'ultra_smooth' (0.5-20Hz)
    
if 基线不稳定:
    推荐更严格的伪迹去除
```

### 质量问题诊断

自动识别并报告：
- 🔊 信噪比问题
- 📊 信号抖动问题  
- 📈 基线不稳定问题
- 📉 HEP信号微弱问题

## 7. 科学处理流程

### 完整的5步处理流程

1. **伪迹检测与移除**
   - 幅度异常检测
   - 基线稳定性验证
   - 梯度突变检测

2. **带通滤波**
   - 自适应频率范围选择
   - Butterworth 4阶滤波器
   - 零相位失真处理

3. **统一基线矫正**
   - 全局基线计算
   - 所有条件统一标准
   - -200ms到0ms窗口

4. **信号平滑**
   - 移动平均处理
   - 保持HEP形态
   - 减少高频抖动

5. **质量控制验证**
   - 多维度质量评估
   - 自动问题识别
   - 智能优化建议

## 8. 使用建议

### 根据数据质量选择配置

- **高质量数据**: 使用 `optimized` (0.5-45Hz)
- **中等质量数据**: 使用 `smooth` (1.0-30Hz)  
- **低质量数据**: 使用 `ultra_smooth` (0.5-20Hz)

### 质量监控指标

定期检查：
- 基线标准差 < 2μV
- HEP幅度 > 0.5μV
- 信噪比 > 2.0
- 有效epochs数 > 100

## 9. 预期效果

### 信号质量改善

- **噪声降低**: 20-50%
- **基线稳定性**: 显著提升
- **HEP可见性**: 明显改善
- **信噪比**: 2-5倍提升

### 科学可靠性

- 符合HEP研究标准
- 保持生理意义
- 提高统计功效
- 增强结果可重复性

## 10. 技术实现

所有优化已集成到主分析脚本中：
- `12_prefrontal_rest1_vs_rest3_tp9tp10.py`

关键函数：
- `apply_bandpass_filter()`: 优化滤波
- `detect_and_remove_artifact_epochs()`: 伪迹检测
- `apply_moving_average_smoothing()`: 信号平滑
- `check_data_quality()`: 质量控制
- `create_multi_filter_comparison()`: 参数对比

运行脚本即可获得完整的优化分析结果。
