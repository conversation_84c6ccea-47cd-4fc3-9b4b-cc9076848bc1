#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Rest1 vs Rest3 前额叶HEP分析结果可视化

本脚本用于可视化Rest1和Rest3对比分析的统计结果
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
import csv

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-NeuroKit2'
RESULT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\14_rest_vs_test_analysis"

def load_statistical_results():
    """加载统计结果"""
    results = []
    csv_path = os.path.join(RESULT_DIR, 'statistical_results.csv')
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            # 转换数值类型
            for key in ['rest1_mean', 'rest1_std', 'rest3_mean', 'rest3_std', 
                       'difference', 't_statistic', 'p_value', 'cohen_d']:
                row[key] = float(row[key])
            row['significant'] = row['significant'] == 'True'
            results.append(row)
    
    return results

def plot_significant_results():
    """绘制显著结果图"""
    results = load_statistical_results()
    
    # 筛选显著结果
    sig_results = [r for r in results if r['significant']]
    
    if not sig_results:
        print("没有发现显著差异")
        return
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 显著差异条形图
    ax1 = axes[0, 0]
    electrodes = [r['electrode'] for r in sig_results]
    features = [r['feature'] for r in sig_results]
    differences = [r['difference'] for r in sig_results]
    p_values = [r['p_value'] for r in sig_results]
    
    # 创建标签
    labels = [f"{e}\n{f}" for e, f in zip(electrodes, features)]
    
    # 绘制条形图
    bars = ax1.bar(range(len(sig_results)), differences, 
                   color=['red' if d > 0 else 'blue' for d in differences],
                   alpha=0.7)
    
    # 添加p值标注
    for i, (bar, p_val) in enumerate(zip(bars, p_values)):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01 if height > 0 else height - 0.01,
                f'p={p_val:.3f}', ha='center', va='bottom' if height > 0 else 'top', fontsize=8)
    
    ax1.set_xticks(range(len(sig_results)))
    ax1.set_xticklabels(labels, rotation=45, ha='right')
    ax1.set_ylabel('差值 (Rest3 - Rest1) μV')
    ax1.set_title('显著差异结果 (p < 0.05)')
    ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax1.grid(True, alpha=0.3)
    
    # 2. 效应量图
    ax2 = axes[0, 1]
    cohen_d_values = [r['cohen_d'] for r in sig_results]
    
    bars2 = ax2.bar(range(len(sig_results)), cohen_d_values,
                    color=['red' if d > 0 else 'blue' for d in cohen_d_values],
                    alpha=0.7)
    
    # 添加效应量分类线
    ax2.axhline(y=0.2, color='green', linestyle='--', alpha=0.5, label='小效应量')
    ax2.axhline(y=0.5, color='orange', linestyle='--', alpha=0.5, label='中等效应量')
    ax2.axhline(y=0.8, color='red', linestyle='--', alpha=0.5, label='大效应量')
    ax2.axhline(y=-0.2, color='green', linestyle='--', alpha=0.5)
    ax2.axhline(y=-0.5, color='orange', linestyle='--', alpha=0.5)
    ax2.axhline(y=-0.8, color='red', linestyle='--', alpha=0.5)
    
    ax2.set_xticks(range(len(sig_results)))
    ax2.set_xticklabels(labels, rotation=45, ha='right')
    ax2.set_ylabel("Cohen's d")
    ax2.set_title('效应量 (Cohen\'s d)')
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 各电极显著特征统计
    ax3 = axes[1, 0]
    electrode_counts = {}
    for r in sig_results:
        electrode = r['electrode']
        electrode_counts[electrode] = electrode_counts.get(electrode, 0) + 1
    
    if electrode_counts:
        electrodes_list = list(electrode_counts.keys())
        counts = list(electrode_counts.values())
        
        bars3 = ax3.bar(electrodes_list, counts, color='steelblue', alpha=0.7)
        
        # 添加数值标注
        for bar, count in zip(bars3, counts):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                    f'{count}', ha='center', va='bottom', fontweight='bold')
        
        ax3.set_ylabel('显著特征数量')
        ax3.set_title('各电极显著特征统计')
        ax3.grid(True, alpha=0.3)
    
    # 4. 各特征显著电极统计
    ax4 = axes[1, 1]
    feature_counts = {}
    for r in sig_results:
        feature = r['feature']
        feature_counts[feature] = feature_counts.get(feature, 0) + 1
    
    if feature_counts:
        features_list = list(feature_counts.keys())
        counts = list(feature_counts.values())
        
        bars4 = ax4.bar(range(len(features_list)), counts, color='orange', alpha=0.7)
        
        # 添加数值标注
        for bar, count in zip(bars4, counts):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                    f'{count}', ha='center', va='bottom', fontweight='bold')
        
        ax4.set_xticks(range(len(features_list)))
        ax4.set_xticklabels(features_list, rotation=45, ha='right')
        ax4.set_ylabel('显著电极数量')
        ax4.set_title('各特征显著电极统计')
        ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    save_path = os.path.join(RESULT_DIR, 'significant_results_visualization.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.savefig(save_path.replace('.png', '.svg'), bbox_inches='tight')
    print(f"显著结果可视化已保存: {save_path}")
    plt.close()

def plot_all_results_heatmap():
    """绘制所有结果的热图"""
    results = load_statistical_results()
    
    # 获取唯一的电极和特征
    electrodes = sorted(list(set(r['electrode'] for r in results)))
    features = sorted(list(set(r['feature'] for r in results)))
    
    # 创建矩阵
    p_matrix = np.ones((len(electrodes), len(features)))
    d_matrix = np.zeros((len(electrodes), len(features)))
    diff_matrix = np.zeros((len(electrodes), len(features)))
    
    for r in results:
        i = electrodes.index(r['electrode'])
        j = features.index(r['feature'])
        p_matrix[i, j] = r['p_value']
        d_matrix[i, j] = r['cohen_d']
        diff_matrix[i, j] = r['difference']
    
    # 创建图形
    fig, axes = plt.subplots(1, 3, figsize=(20, 6))
    
    # P值热图
    im1 = axes[0].imshow(p_matrix, cmap='RdYlBu_r', aspect='auto', vmin=0, vmax=0.1)
    axes[0].set_xticks(range(len(features)))
    axes[0].set_xticklabels(features, rotation=45, ha='right')
    axes[0].set_yticks(range(len(electrodes)))
    axes[0].set_yticklabels(electrodes)
    axes[0].set_title('P值热图')
    plt.colorbar(im1, ax=axes[0])
    
    # 添加显著性标记
    for i in range(len(electrodes)):
        for j in range(len(features)):
            p_val = p_matrix[i, j]
            if p_val < 0.001:
                axes[0].text(j, i, '***', ha='center', va='center', color='white', fontweight='bold')
            elif p_val < 0.01:
                axes[0].text(j, i, '**', ha='center', va='center', color='white', fontweight='bold')
            elif p_val < 0.05:
                axes[0].text(j, i, '*', ha='center', va='center', color='white', fontweight='bold')
    
    # 效应量热图
    im2 = axes[1].imshow(d_matrix, cmap='RdBu_r', aspect='auto', vmin=-1, vmax=1)
    axes[1].set_xticks(range(len(features)))
    axes[1].set_xticklabels(features, rotation=45, ha='right')
    axes[1].set_yticks(range(len(electrodes)))
    axes[1].set_yticklabels(electrodes)
    axes[1].set_title('效应量 (Cohen\'s d)')
    plt.colorbar(im2, ax=axes[1])
    
    # 差值热图
    im3 = axes[2].imshow(diff_matrix, cmap='RdBu_r', aspect='auto')
    axes[2].set_xticks(range(len(features)))
    axes[2].set_xticklabels(features, rotation=45, ha='right')
    axes[2].set_yticks(range(len(electrodes)))
    axes[2].set_yticklabels(electrodes)
    axes[2].set_title('差值 (Rest3 - Rest1)')
    plt.colorbar(im3, ax=axes[2])
    
    plt.tight_layout()
    save_path = os.path.join(RESULT_DIR, 'all_results_heatmap.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.savefig(save_path.replace('.png', '.svg'), bbox_inches='tight')
    print(f"全结果热图已保存: {save_path}")
    plt.close()

def main():
    """主函数"""
    print("="*50)
    print("Rest1 vs Rest3 结果可视化")
    print("="*50)
    
    # 绘制显著结果
    print("1. 绘制显著结果图...")
    plot_significant_results()
    
    # 绘制全结果热图
    print("2. 绘制全结果热图...")
    plot_all_results_heatmap()
    
    print("\n可视化完成！")
    print(f"结果保存在: {RESULT_DIR}")

if __name__ == "__main__":
    main()
