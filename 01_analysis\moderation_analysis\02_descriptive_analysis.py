#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
心理韧性调节效应分析 - 模块2：描述性统计与相关分析

功能：
- 加载预处理后的数据
- 计算描述性统计量
- 进行相关分析
- 生成描述性统计图表

作者：AI助手
日期：2024年
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from matplotlib.font_manager import FontProperties
import matplotlib as mpl
from datetime import datetime

# 设置中文字体
try:
    font = FontProperties(fname=r"C:\Windows\Fonts\LXGW WenKai Mono.ttf")
    plt.rcParams['font.family'] = ['sans-serif']
    plt.rcParams['font.sans-serif'] = ['LXGW WenKai']
    plt.rcParams['axes.unicode_minus'] = False
    mpl.rcParams['font.size'] = 10
except:
    print("警告: 未找到LXGW WenKai字体，使用系统默认字体")

# 定义常量
DATA_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\moderation_analysis"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\moderation_analysis"

def find_latest_data_file(data_dir=DATA_DIR):
    """
    查找最新的数据文件
    
    参数:
    data_dir (str): 数据目录
    
    返回:
    str: 最新的数据文件路径
    """
    print(f"查找数据文件: {data_dir}")
    
    # 查找所有数据文件
    data_files = [f for f in os.listdir(data_dir) if f.startswith("moderation_analysis_data_") and f.endswith('.csv')]
    
    if not data_files:
        raise FileNotFoundError(f"未找到数据文件")
    
    # 选择最新的文件
    latest_file = max([os.path.join(data_dir, f) for f in data_files], key=os.path.getmtime)
    
    print(f"找到最新的数据文件: {os.path.basename(latest_file)}")
    
    return latest_file

def load_data(file_path):
    """
    加载数据
    
    参数:
    file_path (str): 数据文件路径
    
    返回:
    pd.DataFrame: 数据
    """
    print(f"加载数据: {file_path}")
    
    # 读取CSV文件
    df = pd.read_csv(file_path)
    
    print(f"数据包含{len(df)}条记录，{df['subject_id'].nunique()}个被试")
    
    return df

def calculate_descriptive_statistics(df):
    """
    计算描述性统计量
    
    参数:
    df (pd.DataFrame): 数据
    
    返回:
    pd.DataFrame: 描述性统计量
    """
    print("计算描述性统计量...")
    
    # 定义要分析的变量
    variables = ['resilience', 'trait_anxiety', 'state_anxiety', 'mean_amplitude']
    
    # 计算描述性统计量
    desc_stats = df[variables].describe()
    
    # 添加其他统计量
    for var in variables:
        # 偏度
        desc_stats.loc['skewness', var] = df[var].skew()
        # 峰度
        desc_stats.loc['kurtosis', var] = df[var].kurtosis()
        # 正态性检验（Shapiro-Wilk）
        if len(df[var].dropna().unique()) > 1:  # 确保有足够的唯一值
            _, p_value = stats.shapiro(df[var].dropna())
            desc_stats.loc['shapiro_p', var] = p_value
        else:
            desc_stats.loc['shapiro_p', var] = np.nan
    
    print("描述性统计量计算完成")
    
    return desc_stats

def analyze_by_group(df, group_var, target_vars):
    """
    按组分析
    
    参数:
    df (pd.DataFrame): 数据
    group_var (str): 分组变量
    target_vars (list): 目标变量
    
    返回:
    pd.DataFrame: 分组统计量
    """
    print(f"按{group_var}分组分析...")
    
    # 确保分组变量存在
    if group_var not in df.columns:
        print(f"警告: 分组变量{group_var}不存在")
        return None
    
    # 获取组别
    groups = df[group_var].unique()
    
    # 存储分组统计量
    group_stats = []
    
    # 对每个组计算统计量
    for group in groups:
        group_df = df[df[group_var] == group]
        
        for var in target_vars:
            if var in df.columns:
                stats_dict = {
                    'group_var': group_var,
                    'group': group,
                    'variable': var,
                    'count': len(group_df),
                    'mean': group_df[var].mean(),
                    'std': group_df[var].std(),
                    'min': group_df[var].min(),
                    'q1': group_df[var].quantile(0.25),
                    'median': group_df[var].median(),
                    'q3': group_df[var].quantile(0.75),
                    'max': group_df[var].max()
                }
                
                group_stats.append(stats_dict)
    
    # 转换为DataFrame
    group_stats_df = pd.DataFrame(group_stats)
    
    return group_stats_df

def correlation_analysis(df, variables):
    """
    相关分析
    
    参数:
    df (pd.DataFrame): 数据
    variables (list): 要分析的变量
    
    返回:
    tuple: (相关系数矩阵, p值矩阵)
    """
    print("进行相关分析...")
    
    # 确保变量存在
    existing_vars = [var for var in variables if var in df.columns]
    
    if len(existing_vars) < 2:
        print(f"警告: 可用变量不足，无法进行相关分析")
        return None, None
    
    # 计算相关系数和p值
    corr_matrix = np.zeros((len(existing_vars), len(existing_vars)))
    p_matrix = np.zeros((len(existing_vars), len(existing_vars)))
    
    for i, var1 in enumerate(existing_vars):
        for j, var2 in enumerate(existing_vars):
            if i == j:
                corr_matrix[i, j] = 1.0
                p_matrix[i, j] = 0.0
            else:
                r, p = stats.pearsonr(df[var1].dropna(), df[var2].dropna())
                corr_matrix[i, j] = r
                p_matrix[i, j] = p
    
    # 转换为DataFrame
    corr_df = pd.DataFrame(corr_matrix, index=existing_vars, columns=existing_vars)
    p_df = pd.DataFrame(p_matrix, index=existing_vars, columns=existing_vars)
    
    print("相关分析完成")
    
    return corr_df, p_df

def plot_correlation_matrix(corr_df, p_df, output_dir=OUTPUT_DIR):
    """
    绘制相关矩阵热图
    
    参数:
    corr_df (pd.DataFrame): 相关系数矩阵
    p_df (pd.DataFrame): p值矩阵
    output_dir (str): 输出目录
    
    返回:
    str: 图表文件路径
    """
    print("绘制相关矩阵热图...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建图表
    plt.figure(figsize=(10, 8))
    
    # 创建掩码，标记显著的相关系数
    mask = p_df > 0.05
    
    # 绘制热图
    sns.heatmap(corr_df, annot=True, cmap='coolwarm', vmin=-1, vmax=1, 
                mask=mask, cbar_kws={'label': 'Pearson相关系数'})
    
    # 添加标题
    plt.title('变量间相关系数矩阵 (仅显示p<0.05的相关系数)')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    fig_path = os.path.join(output_dir, f"correlation_matrix_{timestamp}.png")
    plt.savefig(fig_path)
    plt.close()
    
    print(f"相关矩阵热图已保存至: {fig_path}")
    
    return fig_path

def plot_distribution(df, variables, output_dir=OUTPUT_DIR):
    """
    绘制变量分布图
    
    参数:
    df (pd.DataFrame): 数据
    variables (list): 要绘制的变量
    output_dir (str): 输出目录
    
    返回:
    list: 图表文件路径列表
    """
    print("绘制变量分布图...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 存储图表路径
    fig_paths = []
    
    # 对每个变量绘制分布图
    for var in variables:
        if var in df.columns:
            # 创建图表
            plt.figure(figsize=(12, 5))
            
            # 创建子图
            plt.subplot(1, 2, 1)
            sns.histplot(df[var], kde=True)
            plt.title(f'{var}的分布直方图')
            
            plt.subplot(1, 2, 2)
            stats.probplot(df[var].dropna(), plot=plt)
            plt.title(f'{var}的Q-Q图')
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图表
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            fig_path = os.path.join(output_dir, f"{var}_distribution_{timestamp}.png")
            plt.savefig(fig_path)
            plt.close()
            
            fig_paths.append(fig_path)
            
            print(f"{var}的分布图已保存至: {fig_path}")
    
    return fig_paths

def plot_group_comparison(df, group_var, target_vars, output_dir=OUTPUT_DIR):
    """
    绘制分组比较图
    
    参数:
    df (pd.DataFrame): 数据
    group_var (str): 分组变量
    target_vars (list): 目标变量
    output_dir (str): 输出目录
    
    返回:
    list: 图表文件路径列表
    """
    print(f"绘制{group_var}分组比较图...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 存储图表路径
    fig_paths = []
    
    # 确保分组变量存在
    if group_var not in df.columns:
        print(f"警告: 分组变量{group_var}不存在")
        return fig_paths
    
    # 对每个目标变量绘制箱线图
    for var in target_vars:
        if var in df.columns:
            # 创建图表
            plt.figure(figsize=(10, 6))
            
            # 绘制箱线图
            sns.boxplot(x=group_var, y=var, data=df)
            
            # 添加标题和标签
            plt.title(f'{var}在不同{group_var}组的分布')
            plt.xlabel(group_var)
            plt.ylabel(var)
            
            # 进行t检验
            groups = df[group_var].unique()
            if len(groups) == 2:
                group1 = df[df[group_var] == groups[0]][var].dropna()
                group2 = df[df[group_var] == groups[1]][var].dropna()
                
                t, p = stats.ttest_ind(group1, group2)
                
                # 添加t检验结果
                plt.annotate(f't = {t:.2f}, p = {p:.3f}', 
                             xy=(0.5, 0.95), xycoords='axes fraction',
                             ha='center', va='top',
                             bbox=dict(boxstyle='round', fc='white', alpha=0.8))
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图表
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            fig_path = os.path.join(output_dir, f"{group_var}_{var}_comparison_{timestamp}.png")
            plt.savefig(fig_path)
            plt.close()
            
            fig_paths.append(fig_path)
            
            print(f"{var}的分组比较图已保存至: {fig_path}")
    
    return fig_paths

def plot_scatter_matrix(df, variables, output_dir=OUTPUT_DIR):
    """
    绘制散点图矩阵
    
    参数:
    df (pd.DataFrame): 数据
    variables (list): 要绘制的变量
    output_dir (str): 输出目录
    
    返回:
    str: 图表文件路径
    """
    print("绘制散点图矩阵...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 确保变量存在
    existing_vars = [var for var in variables if var in df.columns]
    
    if len(existing_vars) < 2:
        print(f"警告: 可用变量不足，无法绘制散点图矩阵")
        return None
    
    # 创建图表
    plt.figure(figsize=(12, 10))
    
    # 绘制散点图矩阵
    axes = pd.plotting.scatter_matrix(df[existing_vars], alpha=0.8, figsize=(12, 10), diagonal='kde')
    
    # 调整标签
    for i, var1 in enumerate(existing_vars):
        for j, var2 in enumerate(existing_vars):
            if i != j:
                # 计算相关系数
                r, p = stats.pearsonr(df[var1].dropna(), df[var2].dropna())
                
                # 添加相关系数
                axes[i, j].annotate(f'r = {r:.2f}\np = {p:.3f}', 
                                   xy=(0.1, 0.9), xycoords='axes fraction',
                                   ha='left', va='top',
                                   bbox=dict(boxstyle='round', fc='white', alpha=0.8))
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    fig_path = os.path.join(output_dir, f"scatter_matrix_{timestamp}.png")
    plt.savefig(fig_path)
    plt.close()
    
    print(f"散点图矩阵已保存至: {fig_path}")
    
    return fig_path

def save_results(desc_stats, group_stats, corr_df, p_df, output_dir=OUTPUT_DIR):
    """
    保存分析结果
    
    参数:
    desc_stats (pd.DataFrame): 描述性统计量
    group_stats (pd.DataFrame): 分组统计量
    corr_df (pd.DataFrame): 相关系数矩阵
    p_df (pd.DataFrame): p值矩阵
    output_dir (str): 输出目录
    
    返回:
    dict: 保存的文件路径
    """
    print("保存分析结果...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存描述性统计量
    desc_stats_path = os.path.join(output_dir, f"descriptive_statistics_{timestamp}.csv")
    desc_stats.to_csv(desc_stats_path)
    
    # 保存分组统计量
    group_stats_path = os.path.join(output_dir, f"group_statistics_{timestamp}.csv")
    group_stats.to_csv(group_stats_path)
    
    # 保存相关系数矩阵
    corr_path = os.path.join(output_dir, f"correlation_matrix_{timestamp}.csv")
    corr_df.to_csv(corr_path)
    
    # 保存p值矩阵
    p_path = os.path.join(output_dir, f"p_value_matrix_{timestamp}.csv")
    p_df.to_csv(p_path)
    
    print(f"分析结果已保存至: {output_dir}")
    
    return {
        'desc_stats': desc_stats_path,
        'group_stats': group_stats_path,
        'corr_matrix': corr_path,
        'p_matrix': p_path
    }

def main():
    """主函数"""
    # 查找最新的数据文件
    data_file = find_latest_data_file()
    
    # 加载数据
    df = load_data(data_file)
    
    # 计算描述性统计量
    desc_stats = calculate_descriptive_statistics(df)
    print("\n===== 描述性统计量 =====")
    print(desc_stats)
    
    # 按心理韧性分组分析
    target_vars = ['trait_anxiety', 'state_anxiety', 'mean_amplitude']
    group_stats = analyze_by_group(df, 'resilience_group', target_vars)
    print("\n===== 分组统计量 =====")
    print(group_stats)
    
    # 相关分析
    variables = ['resilience', 'trait_anxiety', 'state_anxiety', 'mean_amplitude', 
                'resilience_z', 'trait_anxiety_z', 'state_anxiety_z', 'mean_amplitude_z']
    corr_df, p_df = correlation_analysis(df, variables)
    print("\n===== 相关系数矩阵 =====")
    print(corr_df)
    print("\n===== p值矩阵 =====")
    print(p_df)
    
    # 绘制相关矩阵热图
    corr_fig_path = plot_correlation_matrix(corr_df, p_df)
    
    # 绘制变量分布图
    dist_fig_paths = plot_distribution(df, ['resilience', 'trait_anxiety', 'state_anxiety', 'mean_amplitude'])
    
    # 绘制分组比较图
    group_fig_paths = plot_group_comparison(df, 'resilience_group', ['trait_anxiety', 'state_anxiety', 'mean_amplitude'])
    
    # 绘制散点图矩阵
    scatter_fig_path = plot_scatter_matrix(df, ['resilience', 'trait_anxiety', 'state_anxiety', 'mean_amplitude'])
    
    # 保存分析结果
    result_paths = save_results(desc_stats, group_stats, corr_df, p_df)
    
    print("\n描述性统计与相关分析完成")
    print(f"分析结果已保存至: {OUTPUT_DIR}")

if __name__ == "__main__":
    main()
