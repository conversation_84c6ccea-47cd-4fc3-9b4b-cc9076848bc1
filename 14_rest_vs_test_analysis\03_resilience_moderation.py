#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
静息态与刺激态对比分析 - 模块3：心理韧性调节效应分析

功能：
- 加载预处理后的数据
- 分析心理韧性与HEP振幅的相关性
- 分析心理韧性是否调节静息态-刺激态HEP振幅变化
- 生成统计结果和可视化图表
- 保存分析结果

作者：AI助手
日期：2024年
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import statsmodels.api as sm
import statsmodels.formula.api as smf
from matplotlib.font_manager import FontProperties
import matplotlib as mpl
from datetime import datetime

# 设置中文字体
try:
    font = FontProperties(fname=r"C:\Windows\Fonts\LXGW WenKai Mono.ttf")
    plt.rcParams['font.family'] = ['sans-serif']
    plt.rcParams['font.sans-serif'] = ['LXGW WenKai']
    plt.rcParams['axes.unicode_minus'] = False
    mpl.rcParams['font.size'] = 10
except:
    print("警告: 未找到LXGW WenKai字体，使用系统默认字体")

# 定义常量
DATA_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\14_rest_vs_test_analysis"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\14_rest_vs_test_analysis"

def find_latest_data_file(data_dir=DATA_DIR, prefix="hep_merged_"):
    """
    查找最新的数据文件

    参数:
    data_dir (str): 数据目录
    prefix (str): 文件前缀

    返回:
    str: 最新的数据文件路径
    """
    print(f"查找数据文件: {data_dir}")

    # 查找所有数据文件
    data_files = [f for f in os.listdir(data_dir) if f.startswith(prefix) and f.endswith('.csv')]

    if not data_files:
        raise FileNotFoundError(f"未找到数据文件")

    # 选择最新的文件
    latest_file = max([os.path.join(data_dir, f) for f in data_files], key=os.path.getmtime)

    print(f"找到最新的数据文件: {os.path.basename(latest_file)}")

    return latest_file

def load_data(file_path):
    """
    加载数据

    参数:
    file_path (str): 数据文件路径

    返回:
    pd.DataFrame: 数据
    """
    print(f"加载数据: {file_path}")

    # 读取CSV文件
    df = pd.read_csv(file_path)

    print(f"数据包含{len(df)}条记录，{df['subject_id'].nunique()}个被试")

    return df

def preprocess_data(df):
    """
    数据预处理

    参数:
    df (pd.DataFrame): 数据

    返回:
    pd.DataFrame: 预处理后的数据
    """
    print("数据预处理...")

    # 复制数据
    processed_df = df.copy()

    # 标准化连续变量
    for col in ['resilience', 'tenacity', 'optimism', 'strength',
                'rest_amplitude', 'test_amplitude', 'diff_amplitude']:
        if col in processed_df.columns:
            mean = processed_df[col].mean()
            std = processed_df[col].std()
            processed_df[f'{col}_z'] = (processed_df[col] - mean) / std
            print(f"标准化{col}: 均值={mean:.2f}, 标准差={std:.2f}")

    # 创建高/低心理韧性分组
    median_resilience = processed_df['resilience'].median()
    processed_df['resilience_group'] = processed_df['resilience'].apply(
        lambda x: 'high' if x >= median_resilience else 'low'
    )
    print(f"心理韧性中位数: {median_resilience:.2f}")
    print(f"高心理韧性组: {(processed_df['resilience_group'] == 'high').sum()}条记录")
    print(f"低心理韧性组: {(processed_df['resilience_group'] == 'low').sum()}条记录")

    # 创建交互项
    processed_df['rest_x_resilience'] = processed_df['rest_amplitude_z'] * processed_df['resilience_z']
    processed_df['test_x_resilience'] = processed_df['test_amplitude_z'] * processed_df['resilience_z']
    processed_df['diff_x_resilience'] = processed_df['diff_amplitude_z'] * processed_df['resilience_z']

    return processed_df

def analyze_correlations(df):
    """
    分析心理韧性与HEP振幅的相关性

    参数:
    df (pd.DataFrame): 数据

    返回:
    pd.DataFrame: 相关分析结果
    """
    print("分析心理韧性与HEP振幅的相关性...")

    # 获取唯一的通道和时间窗口组合
    channels = df['channel'].unique()
    windows = df['window'].unique()

    # 存储相关分析结果
    corr_results = []

    # 对每个通道和时间窗口进行相关分析
    for channel in channels:
        for window in windows:
            # 筛选数据
            subset = df[(df['channel'] == channel) & (df['window'] == window)]

            # 分析心理韧性与静息态HEP振幅的相关性
            r_rest, p_rest = stats.pearsonr(subset['resilience'], subset['rest_amplitude'])

            # 分析心理韧性与刺激态HEP振幅的相关性
            r_test, p_test = stats.pearsonr(subset['resilience'], subset['test_amplitude'])

            # 分析心理韧性与HEP振幅差异的相关性
            r_diff, p_diff = stats.pearsonr(subset['resilience'], subset['diff_amplitude'])

            # 添加结果
            corr_results.append({
                'channel': channel,
                'window': window,
                'r_rest': r_rest,
                'p_rest': p_rest,
                'r_test': r_test,
                'p_test': p_test,
                'r_diff': r_diff,
                'p_diff': p_diff,
                'n': len(subset)
            })

    # 转换为DataFrame
    corr_df = pd.DataFrame(corr_results)

    # 添加显著性标记
    corr_df['sig_rest'] = corr_df['p_rest'] < 0.05
    corr_df['sig_test'] = corr_df['p_test'] < 0.05
    corr_df['sig_diff'] = corr_df['p_diff'] < 0.05

    # 应用FDR校正
    from statsmodels.stats.multitest import fdrcorrection
    _, corr_df['p_rest_fdr'] = fdrcorrection(corr_df['p_rest'])
    _, corr_df['p_test_fdr'] = fdrcorrection(corr_df['p_test'])
    _, corr_df['p_diff_fdr'] = fdrcorrection(corr_df['p_diff'])

    corr_df['sig_rest_fdr'] = corr_df['p_rest_fdr'] < 0.05
    corr_df['sig_test_fdr'] = corr_df['p_test_fdr'] < 0.05
    corr_df['sig_diff_fdr'] = corr_df['p_diff_fdr'] < 0.05

    print(f"相关分析完成，共{len(corr_df)}个结果")

    return corr_df

def analyze_resilience_dimensions(df):
    """
    分析心理韧性三个维度与HEP振幅的相关性

    参数:
    df (pd.DataFrame): 数据

    返回:
    pd.DataFrame: 相关分析结果
    """
    print("分析心理韧性三个维度与HEP振幅的相关性...")

    # 获取唯一的通道和时间窗口组合
    channels = df['channel'].unique()
    windows = df['window'].unique()

    # 存储相关分析结果
    dim_results = []

    # 对每个通道和时间窗口进行相关分析
    for channel in channels:
        for window in windows:
            # 筛选数据
            subset = df[(df['channel'] == channel) & (df['window'] == window)]

            # 对每个维度进行分析
            for dim in ['tenacity', 'optimism', 'strength']:
                # 分析维度与静息态HEP振幅的相关性
                r_rest, p_rest = stats.pearsonr(subset[dim], subset['rest_amplitude'])

                # 分析维度与刺激态HEP振幅的相关性
                r_test, p_test = stats.pearsonr(subset[dim], subset['test_amplitude'])

                # 分析维度与HEP振幅差异的相关性
                r_diff, p_diff = stats.pearsonr(subset[dim], subset['diff_amplitude'])

                # 添加结果
                dim_results.append({
                    'channel': channel,
                    'window': window,
                    'dimension': dim,
                    'r_rest': r_rest,
                    'p_rest': p_rest,
                    'r_test': r_test,
                    'p_test': p_test,
                    'r_diff': r_diff,
                    'p_diff': p_diff,
                    'n': len(subset)
                })

    # 转换为DataFrame
    dim_df = pd.DataFrame(dim_results)

    # 添加显著性标记
    dim_df['sig_rest'] = dim_df['p_rest'] < 0.05
    dim_df['sig_test'] = dim_df['p_test'] < 0.05
    dim_df['sig_diff'] = dim_df['p_diff'] < 0.05

    print(f"维度相关分析完成，共{len(dim_df)}个结果")

    return dim_df

def run_moderation_analysis(df):
    """
    进行调节效应分析

    参数:
    df (pd.DataFrame): 数据

    返回:
    pd.DataFrame: 调节效应分析结果
    """
    print("进行调节效应分析...")

    # 获取唯一的通道和时间窗口组合
    channels = df['channel'].unique()
    windows = df['window'].unique()

    # 存储调节效应分析结果
    mod_results = []

    # 对每个通道和时间窗口进行调节效应分析
    for channel in channels:
        for window in windows:
            # 筛选数据
            subset = df[(df['channel'] == channel) & (df['window'] == window)]

            # 分析心理韧性对静息态HEP振幅与刺激态HEP振幅关系的调节作用
            # 步骤1：只有主效应
            formula1 = "test_amplitude_z ~ rest_amplitude_z + resilience_z"
            model1 = smf.ols(formula1, data=subset).fit()

            # 步骤2：加入交互项
            formula2 = "test_amplitude_z ~ rest_amplitude_z + resilience_z + rest_amplitude_z:resilience_z"
            model2 = smf.ols(formula2, data=subset).fit()

            # 计算R²变化量
            r2_change = model2.rsquared - model1.rsquared
            f_change = ((model2.rsquared - model1.rsquared) / (model2.df_model - model1.df_model)) / \
                       ((1 - model2.rsquared) / model2.df_resid)
            p_change = 1 - stats.f.cdf(f_change, model2.df_model - model1.df_model, model2.df_resid)

            # 添加结果
            mod_results.append({
                'channel': channel,
                'window': window,
                'r2_model1': model1.rsquared,
                'r2_model2': model2.rsquared,
                'r2_change': r2_change,
                'f_change': f_change,
                'p_change': p_change,
                'interaction_coef': model2.params['rest_amplitude_z:resilience_z'],
                'interaction_p': model2.pvalues['rest_amplitude_z:resilience_z'],
                'n': len(subset)
            })

    # 转换为DataFrame
    mod_df = pd.DataFrame(mod_results)

    # 添加显著性标记
    mod_df['significant'] = mod_df['p_change'] < 0.05

    # 应用FDR校正
    from statsmodels.stats.multitest import fdrcorrection
    _, mod_df['p_change_fdr'] = fdrcorrection(mod_df['p_change'])
    mod_df['significant_fdr'] = mod_df['p_change_fdr'] < 0.05

    print(f"调节效应分析完成，共{len(mod_df)}个结果")

    return mod_df

def plot_correlation_results(corr_df, output_dir=OUTPUT_DIR):
    """
    绘制相关分析结果图

    参数:
    corr_df (pd.DataFrame): 相关分析结果
    output_dir (str): 输出目录

    返回:
    str: 图表文件路径
    """
    print("绘制相关分析结果图...")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 创建图表
    plt.figure(figsize=(12, 8))

    # 准备数据
    channels = corr_df['channel'].unique()
    windows = corr_df['window'].unique()

    # 设置x轴位置
    x = np.arange(len(corr_df))
    width = 0.25

    # 绘制静息态相关系数
    plt.bar(x - width, corr_df['r_rest'], width, label='静息态', color=['blue' if p < 0.05 else 'lightblue' for p in corr_df['p_rest']])

    # 绘制刺激态相关系数
    plt.bar(x, corr_df['r_test'], width, label='刺激态', color=['red' if p < 0.05 else 'lightcoral' for p in corr_df['p_test']])

    # 绘制差异相关系数
    plt.bar(x + width, corr_df['r_diff'], width, label='差异', color=['green' if p < 0.05 else 'lightgreen' for p in corr_df['p_diff']])

    # 添加水平线
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)

    # 添加标签和图例
    plt.xlabel('通道和时间窗口')
    plt.ylabel('相关系数 (r)')
    plt.title('心理韧性与HEP振幅的相关性')

    # 设置x轴刻度
    labels = [f"{row['channel']} {row['window']}" for _, row in corr_df.iterrows()]
    plt.xticks(x, labels, rotation=45, ha='right')

    # 添加图例
    plt.legend()

    # 添加显著性标记
    for i, (_, row) in enumerate(corr_df.iterrows()):
        # 静息态
        if row['p_rest'] < 0.05:
            plt.text(i - width, row['r_rest'] + 0.02, '*', ha='center', fontsize=12)
        # 刺激态
        if row['p_test'] < 0.05:
            plt.text(i, row['r_test'] + 0.02, '*', ha='center', fontsize=12)
        # 差异
        if row['p_diff'] < 0.05:
            plt.text(i + width, row['r_diff'] + 0.02, '*', ha='center', fontsize=12)

    # 调整布局
    plt.tight_layout()

    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    fig_path = os.path.join(output_dir, f"resilience_correlation_{timestamp}.png")
    plt.savefig(fig_path, dpi=300)
    plt.close()

    print(f"相关分析结果图已保存至: {fig_path}")

    return fig_path

def plot_dimension_results(dim_df, output_dir=OUTPUT_DIR):
    """
    绘制维度相关分析结果图

    参数:
    dim_df (pd.DataFrame): 维度相关分析结果
    output_dir (str): 输出目录

    返回:
    list: 图表文件路径列表
    """
    print("绘制维度相关分析结果图...")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 存储图表路径
    fig_paths = []

    # 对每个维度绘制图表
    for dimension in dim_df['dimension'].unique():
        # 筛选数据
        subset = dim_df[dim_df['dimension'] == dimension]

        # 创建图表
        plt.figure(figsize=(12, 8))

        # 准备数据
        channels = subset['channel'].unique()
        windows = subset['window'].unique()

        # 设置x轴位置
        x = np.arange(len(channels) * len(windows))
        width = 0.25

        # 绘制静息态相关系数
        plt.bar(x - width, subset['r_rest'], width, label='静息态', color=['blue' if p < 0.05 else 'lightblue' for p in subset['p_rest']])

        # 绘制刺激态相关系数
        plt.bar(x, subset['r_test'], width, label='刺激态', color=['red' if p < 0.05 else 'lightcoral' for p in subset['p_test']])

        # 绘制差异相关系数
        plt.bar(x + width, subset['r_diff'], width, label='差异', color=['green' if p < 0.05 else 'lightgreen' for p in subset['p_diff']])

        # 添加水平线
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)

        # 添加标签和图例
        plt.xlabel('通道和时间窗口')
        plt.ylabel('相关系数 (r)')

        # 设置标题
        dimension_names = {
            'tenacity': '坚韧性',
            'optimism': '乐观性',
            'strength': '力量性'
        }
        plt.title(f'{dimension_names.get(dimension, dimension)}与HEP振幅的相关性')

        # 设置x轴刻度
        labels = [f"{row['channel']} {row['window']}" for _, row in subset.iterrows()]
        plt.xticks(x, labels, rotation=45, ha='right')

        # 添加图例
        plt.legend()

        # 添加显著性标记
        for i, (_, row) in enumerate(subset.iterrows()):
            # 静息态
            if row['p_rest'] < 0.05:
                plt.text(i - width, row['r_rest'] + 0.02, '*', ha='center', fontsize=12)
            # 刺激态
            if row['p_test'] < 0.05:
                plt.text(i, row['r_test'] + 0.02, '*', ha='center', fontsize=12)
            # 差异
            if row['p_diff'] < 0.05:
                plt.text(i + width, row['r_diff'] + 0.02, '*', ha='center', fontsize=12)

        # 调整布局
        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        fig_path = os.path.join(output_dir, f"dimension_{dimension}_correlation_{timestamp}.png")
        plt.savefig(fig_path, dpi=300)
        plt.close()

        fig_paths.append(fig_path)

        print(f"{dimension}维度相关分析结果图已保存至: {fig_path}")

    return fig_paths

def plot_moderation_results(mod_df, output_dir=OUTPUT_DIR):
    """
    绘制调节效应分析结果图

    参数:
    mod_df (pd.DataFrame): 调节效应分析结果
    output_dir (str): 输出目录

    返回:
    str: 图表文件路径
    """
    print("绘制调节效应分析结果图...")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 创建图表
    plt.figure(figsize=(10, 6))

    # 设置x轴位置
    x = np.arange(len(mod_df))

    # 设置颜色
    colors = ['red' if p < 0.05 else 'lightgray' for p in mod_df['p_change']]

    # 绘制条形图
    plt.bar(x, mod_df['r2_change'], color=colors)

    # 添加标签
    plt.xlabel('通道和时间窗口')
    plt.ylabel('R²变化量')
    plt.title('心理韧性对HEP振幅关系的调节效应')

    # 设置x轴刻度
    labels = [f"{row['channel']} {row['window']}" for _, row in mod_df.iterrows()]
    plt.xticks(x, labels, rotation=45, ha='right')

    # 添加显著性标记
    for i, (_, row) in enumerate(mod_df.iterrows()):
        if row['p_change'] < 0.05:
            plt.text(i, row['r2_change'] + 0.01, '*', ha='center', fontsize=12)
            if row['p_change'] < 0.01:
                plt.text(i, row['r2_change'] + 0.02, '*', ha='center', fontsize=12)
            if row['p_change'] < 0.001:
                plt.text(i, row['r2_change'] + 0.03, '*', ha='center', fontsize=12)

    # 添加水平参考线
    plt.axhline(y=0.05, color='black', linestyle='--', alpha=0.5)
    plt.text(len(mod_df) - 1, 0.05, 'R²=0.05', ha='right', va='bottom', alpha=0.7)

    # 调整布局
    plt.tight_layout()

    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    fig_path = os.path.join(output_dir, f"resilience_moderation_{timestamp}.png")
    plt.savefig(fig_path, dpi=300)
    plt.close()

    print(f"调节效应分析结果图已保存至: {fig_path}")

    return fig_path

def save_results(corr_df, dim_df, mod_df, output_dir=OUTPUT_DIR):
    """
    保存分析结果

    参数:
    corr_df (pd.DataFrame): 相关分析结果
    dim_df (pd.DataFrame): 维度相关分析结果
    mod_df (pd.DataFrame): 调节效应分析结果
    output_dir (str): 输出目录

    返回:
    dict: 保存的文件路径
    """
    print("保存分析结果...")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 保存相关分析结果
    corr_path = os.path.join(output_dir, f"resilience_correlation_{timestamp}.csv")
    corr_df.to_csv(corr_path, index=False)

    # 保存维度相关分析结果
    dim_path = os.path.join(output_dir, f"dimension_correlation_{timestamp}.csv")
    dim_df.to_csv(dim_path, index=False)

    # 保存调节效应分析结果
    mod_path = os.path.join(output_dir, f"resilience_moderation_{timestamp}.csv")
    mod_df.to_csv(mod_path, index=False)

    print(f"分析结果已保存至: {output_dir}")

    return {
        'correlation': corr_path,
        'dimension': dim_path,
        'moderation': mod_path
    }

def main():
    """主函数"""
    # 查找最新的数据文件
    data_file = find_latest_data_file()

    # 加载数据
    df = load_data(data_file)

    # 数据预处理
    processed_df = preprocess_data(df)

    # 分析心理韧性与HEP振幅的相关性
    corr_df = analyze_correlations(processed_df)

    # 分析心理韧性三个维度与HEP振幅的相关性
    dim_df = analyze_resilience_dimensions(processed_df)

    # 进行调节效应分析
    mod_df = run_moderation_analysis(processed_df)

    # 绘制相关分析结果图
    corr_fig_path = plot_correlation_results(corr_df)

    # 绘制维度相关分析结果图
    dim_fig_paths = plot_dimension_results(dim_df)

    # 绘制调节效应分析结果图
    mod_fig_path = plot_moderation_results(mod_df)

    # 保存分析结果
    result_paths = save_results(corr_df, dim_df, mod_df)

    print("\n心理韧性调节效应分析完成")
    print(f"分析结果已保存至: {OUTPUT_DIR}")

if __name__ == "__main__":
    main()
