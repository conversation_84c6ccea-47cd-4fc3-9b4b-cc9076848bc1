#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
心理韧性调节效应分析 - 模块1：数据加载与预处理

功能：
- 加载HEP数据和心理量表数据
- 提取关键导联和时间窗口的HEP振幅
- 整合数据并进行预处理
- 保存处理后的数据集供后续分析使用

作者：AI助手
日期：2024年
"""

import os
import numpy as np
import pandas as pd
import h5py
import matplotlib.pyplot as plt
from datetime import datetime

# 定义常量
HEP_RAW_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs"
RATINGS_FILE = r"C:\Users\<USER>\Desktop\stress0422.xlsx"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\moderation_analysis"

# 定义关键导联和时间窗口
KEY_CHANNELS = ['Fz', 'Cz', 'Pz']  # 关键中线导联
TIME_WINDOWS = [(0.4, 0.6), (0.5, 0.7)]  # 关键时间窗口

def create_output_directory():
    """创建输出目录"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    print(f"已创建输出目录: {OUTPUT_DIR}")

def load_subjective_ratings(file_path=RATINGS_FILE):
    """
    加载主观量表数据
    
    参数:
    file_path (str): 主观量表数据文件路径
    
    返回:
    pd.DataFrame: 主观量表数据
    """
    print(f"加载主观量表数据: {file_path}")
    
    # 读取Excel文件
    ratings_df = pd.read_excel(file_path)
    
    print(f"原始量表列名: {ratings_df.columns.tolist()}")
    
    # 定义列名映射
    column_mapping = {
        '编号': 'subject_id',
        '被试姓名': 'subject_name',
        '压力1': 'stress_1',
        '压力2': 'stress_2',
        '压力3': 'stress_3',
        '成功1': 'success_1',
        '成功2': 'success_2',
        '成功3': 'success_3',
        '自信1': 'confidence_1',
        '自信2': 'confidence_2',
        '自信3': 'confidence_3',
        '疼痛1': 'pain_1',
        '疼痛2': 'pain_2',
        '疼痛3': 'pain_3',
        '特质焦虑0': 'trait_anxiety_0',
        '特质焦虑1': 'trait_anxiety_1',
        '特质焦虑2': 'trait_anxiety_2',
        '状态焦虑1': 'state_anxiety_1',
        '状态焦虑2': 'state_anxiety_2',
        ' 心理韧性': 'resilience',
        '坚韧': 'tenacity',
        '乐观': 'optimism',
        '力量': 'strength'
    }
    
    # 只重命名存在的列
    rename_dict = {k: v for k, v in column_mapping.items() if k in ratings_df.columns}
    ratings_df = ratings_df.rename(columns=rename_dict)
    
    print(f"重命名后的列名: {ratings_df.columns.tolist()}")
    
    # 确保subject_id是字符串类型
    if 'subject_id' in ratings_df.columns:
        ratings_df['subject_id'] = ratings_df['subject_id'].astype(str).str.zfill(2)
    
    # 检查关键列是否存在
    required_columns = ['subject_id', 'resilience', 'trait_anxiety_1', 'state_anxiety_1', 'state_anxiety_2']
    missing_columns = [col for col in required_columns if col not in ratings_df.columns]
    
    if missing_columns:
        print(f"警告: 以下关键列缺失: {missing_columns}")
    else:
        print("所有关键列均存在")
    
    # 检查数据完整性
    print(f"主观量表数据包含{len(ratings_df)}个被试")
    
    return ratings_df

def find_latest_hep_files(hep_dir=HEP_RAW_DIR):
    """
    查找最新的HEP数据文件
    
    参数:
    hep_dir (str): HEP数据目录
    
    返回:
    dict: 各阶段最新的HEP文件路径
    """
    print(f"查找HEP数据文件: {hep_dir}")
    
    # 定义阶段
    stages = ['prac', 'rest1', 'test1', 'rest2', 'test2', 'rest3', 'test3']
    
    # 存储各阶段最新的文件
    latest_files = {}
    
    # 查找各阶段最新的文件
    for stage in stages:
        # 查找该阶段的所有文件
        stage_files = [f for f in os.listdir(hep_dir) if f.startswith(f"{stage}_raw_epochs") and f.endswith('.h5')]
        
        if stage_files:
            # 选择最新的文件
            latest_file = max([os.path.join(hep_dir, f) for f in stage_files], key=os.path.getmtime)
            latest_files[stage] = latest_file
            print(f"  - {stage}: {os.path.basename(latest_file)}")
        else:
            print(f"  - {stage}: 未找到文件")
    
    return latest_files

def extract_hep_features(hep_files, channels=KEY_CHANNELS, time_windows=TIME_WINDOWS):
    """
    从HEP数据文件中提取特征
    
    参数:
    hep_files (dict): 各阶段的HEP文件路径
    channels (list): 要提取的通道
    time_windows (list): 要提取的时间窗口
    
    返回:
    pd.DataFrame: HEP特征数据
    """
    print("提取HEP特征...")
    
    # 存储所有特征
    all_features = []
    
    # 处理每个阶段的文件
    for stage, file_path in hep_files.items():
        print(f"处理{stage}阶段: {os.path.basename(file_path)}")
        
        # 加载H5文件
        with h5py.File(file_path, 'r') as f:
            # 获取数据
            data = f['data'][:]  # (n_epochs, n_channels, n_times)
            times = f['times'][:]
            ch_names = [ch.decode('utf-8') for ch in f['ch_names'][:]]
            subject_ids = [subj.decode('utf-8') for subj in f['subject_ids'][:]]
            
            # 获取通道索引
            ch_indices = [ch_names.index(ch) for ch in channels if ch in ch_names]
            
            if not ch_indices:
                print(f"警告: 未找到指定的通道: {channels}")
                continue
            
            # 对每个时间窗口计算平均振幅
            for window_start, window_end in time_windows:
                # 获取时间窗口索引
                time_mask = (times >= window_start) & (times <= window_end)
                
                # 对每个通道计算平均振幅
                for ch_idx in ch_indices:
                    ch_name = ch_names[ch_idx]
                    
                    # 对每个epoch计算平均振幅
                    for epoch_idx in range(data.shape[0]):
                        # 计算时间窗口内的平均振幅
                        mean_amp = np.mean(data[epoch_idx, ch_idx, time_mask])
                        
                        # 添加特征
                        all_features.append({
                            'subject_id': subject_ids[epoch_idx],
                            'channel': ch_name,
                            'window_start': window_start,
                            'window_end': window_end,
                            'window': f"{window_start}-{window_end}s",
                            'epoch_index': epoch_idx,
                            'mean_amplitude': mean_amp,
                            'stage': stage
                        })
    
    # 转换为DataFrame
    features_df = pd.DataFrame(all_features)
    
    # 确保subject_id是字符串类型
    features_df['subject_id'] = features_df['subject_id'].astype(str)
    
    # 添加条件列（rest/test）
    features_df['condition'] = features_df['stage'].apply(
        lambda x: 'rest' if 'rest' in x else 'test' if 'test' in x else 'practice'
    )
    
    print(f"共提取{len(features_df)}个特征")
    
    return features_df

def aggregate_hep_features(features_df):
    """
    聚合HEP特征
    
    参数:
    features_df (pd.DataFrame): HEP特征数据
    
    返回:
    pd.DataFrame: 聚合后的HEP特征数据
    """
    print("聚合HEP特征...")
    
    # 按被试ID、阶段、通道和时间窗口聚合数据
    agg_features = features_df.groupby(
        ['subject_id', 'stage', 'condition', 'channel', 'window_start', 'window_end', 'window']
    )['mean_amplitude'].mean().reset_index()
    
    print(f"聚合后共{len(agg_features)}个特征")
    
    return agg_features

def merge_data(hep_df, ratings_df):
    """
    整合HEP特征和主观量表数据
    
    参数:
    hep_df (pd.DataFrame): HEP特征数据
    ratings_df (pd.DataFrame): 主观量表数据
    
    返回:
    pd.DataFrame: 整合后的数据
    """
    print("整合HEP特征和主观量表数据...")
    
    # 确保subject_id是字符串类型
    hep_df['subject_id'] = hep_df['subject_id'].astype(str)
    ratings_df['subject_id'] = ratings_df['subject_id'].astype(str)
    
    # 创建一个空的合并数据框
    merged_data = []
    
    # 对每个HEP特征记录，添加对应的主观量表数据
    for _, hep_row in hep_df.iterrows():
        subject_id = hep_row['subject_id']
        stage = hep_row['stage']
        
        # 查找该被试的主观量表数据
        if subject_id in ratings_df['subject_id'].values:
            subj_ratings = ratings_df[ratings_df['subject_id'] == subject_id].iloc[0]
            
            # 创建合并记录
            merged_row = hep_row.copy()
            
            # 添加心理韧性数据（实验结束时测量）
            if 'resilience' in subj_ratings:
                merged_row['resilience'] = subj_ratings['resilience']
            
            # 添加焦虑数据（根据阶段选择适当的焦虑指标）
            # 特质焦虑1（实验开始时测量）
            if 'trait_anxiety_1' in subj_ratings:
                merged_row['trait_anxiety'] = subj_ratings['trait_anxiety_1']
            
            # 状态焦虑（根据阶段选择）
            if stage in ['prac', 'rest1', 'test1'] and 'state_anxiety_1' in subj_ratings:
                merged_row['state_anxiety'] = subj_ratings['state_anxiety_1']
            elif stage in ['rest2', 'test2', 'rest3', 'test3'] and 'state_anxiety_2' in subj_ratings:
                merged_row['state_anxiety'] = subj_ratings['state_anxiety_2']
            
            merged_data.append(merged_row)
    
    # 转换为DataFrame
    merged_df = pd.DataFrame(merged_data)
    
    # 检查合并后的数据
    print(f"合并后共{len(merged_df)}条记录")
    print(f"包含{merged_df['subject_id'].nunique()}个被试")
    
    # 检查缺失值
    missing_resilience = merged_df['resilience'].isna().sum()
    missing_trait = merged_df['trait_anxiety'].isna().sum()
    missing_state = merged_df['state_anxiety'].isna().sum()
    
    print(f"心理韧性缺失值: {missing_resilience}条 ({missing_resilience/len(merged_df)*100:.1f}%)")
    print(f"特质焦虑缺失值: {missing_trait}条 ({missing_trait/len(merged_df)*100:.1f}%)")
    print(f"状态焦虑缺失值: {missing_state}条 ({missing_state/len(merged_df)*100:.1f}%)")
    
    return merged_df

def preprocess_data(merged_df):
    """
    数据预处理
    
    参数:
    merged_df (pd.DataFrame): 整合后的数据
    
    返回:
    pd.DataFrame: 预处理后的数据
    """
    print("数据预处理...")
    
    # 复制数据
    df = merged_df.copy()
    
    # 删除缺失值
    df_complete = df.dropna(subset=['resilience', 'trait_anxiety', 'state_anxiety', 'mean_amplitude'])
    print(f"删除缺失值后剩余{len(df_complete)}条记录 (删除了{len(df)-len(df_complete)}条)")
    
    # 标准化连续变量
    for col in ['resilience', 'trait_anxiety', 'state_anxiety', 'mean_amplitude']:
        if col in df_complete.columns:
            mean = df_complete[col].mean()
            std = df_complete[col].std()
            df_complete[f'{col}_z'] = (df_complete[col] - mean) / std
            print(f"标准化{col}: 均值={mean:.2f}, 标准差={std:.2f}")
    
    # 创建高/低心理韧性分组
    median_resilience = df_complete['resilience'].median()
    df_complete['resilience_group'] = df_complete['resilience'].apply(
        lambda x: 'high' if x >= median_resilience else 'low'
    )
    print(f"心理韧性中位数: {median_resilience:.2f}")
    print(f"高心理韧性组: {(df_complete['resilience_group'] == 'high').sum()}条记录")
    print(f"低心理韧性组: {(df_complete['resilience_group'] == 'low').sum()}条记录")
    
    # 创建交互项
    df_complete['interaction_trait'] = df_complete['mean_amplitude_z'] * df_complete['resilience_z']
    df_complete['interaction_state'] = df_complete['mean_amplitude_z'] * df_complete['resilience_z']
    
    return df_complete

def save_processed_data(df, output_dir=OUTPUT_DIR):
    """
    保存处理后的数据
    
    参数:
    df (pd.DataFrame): 处理后的数据
    output_dir (str): 输出目录
    
    返回:
    str: 保存的文件路径
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存为CSV文件
    output_path = os.path.join(output_dir, f"moderation_analysis_data_{timestamp}.csv")
    df.to_csv(output_path, index=False)
    
    print(f"数据已保存至: {output_path}")
    
    return output_path

def main():
    """主函数"""
    # 创建输出目录
    create_output_directory()
    
    # 加载主观量表数据
    ratings_df = load_subjective_ratings()
    
    # 查找最新的HEP文件
    hep_files = find_latest_hep_files()
    
    # 提取HEP特征
    hep_features = extract_hep_features(hep_files)
    
    # 聚合HEP特征
    agg_features = aggregate_hep_features(hep_features)
    
    # 整合数据
    merged_df = merge_data(agg_features, ratings_df)
    
    # 数据预处理
    processed_df = preprocess_data(merged_df)
    
    # 保存处理后的数据
    data_path = save_processed_data(processed_df)
    
    print("\n数据加载与预处理完成")
    print(f"处理后的数据已保存至: {data_path}")
    print(f"数据包含{processed_df['subject_id'].nunique()}个被试, {len(processed_df)}条记录")

if __name__ == "__main__":
    main()
